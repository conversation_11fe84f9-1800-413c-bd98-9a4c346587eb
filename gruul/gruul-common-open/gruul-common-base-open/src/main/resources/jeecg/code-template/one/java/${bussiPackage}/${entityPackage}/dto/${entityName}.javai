package ${bussiPackage}.${entityPackage}.entity;
import com.medusa.gruul.common.core.param.QueryParam;
import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改${tableName}DTO")
public class ${entityName}Dto extends QueryParam {
    
    <#list originalColumns as po>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	<#else>
  <#if po.fieldType =='java.util.Date'>
	<#if po.fieldDbType =='date'>
	<#elseif po.fieldDbType =='datetime'>
	</#if>
	<#else>
  </#if>
  </#if>
    @ApiModelProperty(value = "${po.filedComment}")
	private <#if po.fieldType=='java.sql.Blob'>byte[]<#else>${po.fieldType}</#if> ${po.fieldName};
	</#list>
}
