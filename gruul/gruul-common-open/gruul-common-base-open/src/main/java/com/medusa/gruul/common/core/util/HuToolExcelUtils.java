package com.medusa.gruul.common.core.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

/**
 * Excel辅助类，使用Hutool工具进行解析
 * <AUTHOR>
 */
@Slf4j
public class HuToolExcelUtils {

    /**
     * excel导入封装成实体类列表
     * @param stream excel文件流数据
     * @param tClass 封装的对象
     * @param headerRowIndex 表头开始的行数
     * @param startRowIndex 数据开始的行数
     * @param <T> 对象
     * @return
     */
    public static <T> List<T> importExcelByStream(InputStream stream, Class<T> tClass, int headerRowIndex, int startRowIndex){
        List<T> readList = new ArrayList<>();
        Field[] declaredFields = tClass.getDeclaredFields();
        Map<String, String> headerAlias = new LinkedHashMap<>();
        for(Field f : declaredFields){
            ApiModelProperty annotation = f.getAnnotation(ApiModelProperty.class);
            if(annotation != null){
                headerAlias.put(annotation.value(), f.getName());
            }
        }
        ExcelReader reader = ExcelUtil.getReader(stream);
        if(!headerAlias.isEmpty()){
            reader.setHeaderAlias(headerAlias);
        }
        readList = reader.read(headerRowIndex, startRowIndex, tClass);
        return readList;
    }

    /**
     * <p>
     * 将数据生成Excel（xlsx格式）直接输出到response.getOutputStream()流
     * Excel表头会根据泛型中每个字段的Swagger注解@ApiModelProperty的value生成对应的中文
     *      1) 如果某字段没有标注@ApiModelProperty,则该字段不会被写入Excel
     *      2) 如果泛型中没有任何字段标注@ApiModelProperty,则默认全部字段导出,表头为字段名
     *  注意事项：
     *      hutool-poi:4.6.17 在这个版本中发现LocalDateTime类型的导出后格式为：-> 2023-04-28T16:06:00
     *      单元格格式为常规，所以日期类型的字段建议使用{@link java.util.Date},这样单元格格式是日期
     *      不知道新版hutool-poi中是否存在此问题？<br>
     * </p>
     * @param data      被导出的Excel数据
     * @param fileName  文件名(请不要带后缀)
     * @param <T>
     * @return          导出结果 成功=true;失败=false;
     */
    public static <T> boolean list2xlsx(List<T> data, String fileName, Class<T> tClass){
        if(null == data || data.isEmpty()){
            data = new ArrayList<T>();
        }
        /*T t = data.get(0);
        if(null == t){
            return false;
        }*/
        //Class<?> aClass = tClass.getClass();
        Field[] declaredFields = tClass.getDeclaredFields();
        Map<String, String> headerAlias = new LinkedHashMap<>();
        for(Field f : declaredFields){
            ApiModelProperty annotation = f.getAnnotation(ApiModelProperty.class);
            if(annotation != null){
                headerAlias.put(f.getName(), annotation.value());
            }
        }
        ExcelWriter excelWriter = ExcelUtil.getWriter(true);
        if(!headerAlias.isEmpty()){
            excelWriter.setHeaderAlias(headerAlias);
            excelWriter.setOnlyAlias(true);
        }
        excelWriter.write(data, true);
        try {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletResponse response = Objects.requireNonNull(servletRequestAttributes).getResponse();
            Objects.requireNonNull(response).setHeader(HttpHeaders.CONTENT_DISPOSITION, URLUtil.encode("attachment;fileName=" + fileName + ".xlsx"));
            excelWriter.flush(response.getOutputStream(), true);
            return true;
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            excelWriter.close();
        }
        return false;
    }
    /**
     * 导出数据
     * @param sourceList
     * @param fileName
     * @param function <S,T> 可为null、类型转换 -S -> T ,* 特殊处理的数据逻辑
     *   大数据集 copyProperties慢 ？  list2xlsx（）缓存mapping、分片 ？
     */
    public static  <S, T> void exportData(List<S> sourceList, String fileName, Function<S, T> function) {
        if (CollectionUtils.isEmpty(sourceList)) {
            throw new ServiceException("数据为空",SystemCode.DATA_NOT_EXIST_CODE);
        }
        long startTime = System.currentTimeMillis();

        boolean check;
        fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "_"+fileName;
        if(function != null){
            // 转换
            List<T> list = new ArrayList<>(sourceList.size());
            for (int i = 0; i < sourceList.size(); i++) {
                S item = sourceList.get( i);
                T target = function.apply(item);
                if (target != null) {
                    if(!item.getClass().equals(target.getClass())){
                        BeanUtils.copyProperties(item, target);
                    }
                    // 设置序号（如果目标对象有index字段）
                    setIndexIfExists(target, i + 1);
                    list.add(target);
                }
            }
            Class<?> tClass = list.get(0).getClass();
            check = list2xlsx(list, fileName, (Class<T>) tClass);
        }else {
            Class<?> tClass = sourceList.get(0).getClass();
            check = list2xlsx(sourceList, fileName, (Class<S>) tClass);
        }

        if (!check) {
            throw new ServiceException("导出失败",SystemCode.FAILURE.getCode());
        }
        long endTime = System.currentTimeMillis();

        log.info("Excel导出成功 - 文件名: {}, 数据量: {}, 耗时: {}ms",fileName, sourceList.size(), (endTime - startTime));
    }
    public static Page exportParamToMax(QueryParam param) {
        if (null ==  param){
            return null;
        }
       param.setCurrent(1);
       param.setSize(CommonConstants.MAX_EXPORT_SIZE);
       return new Page(param.getCurrent(), param.getSize());
    }
    private static <T> void setIndexIfExists(T target, int index) {
        try {
            Field indexField = target.getClass().getDeclaredField("index");
            indexField.setAccessible(true);
            indexField.setInt(target, index);
        } catch (Exception e) {
            // 如果没有index字段，忽略
        }
    }
}
