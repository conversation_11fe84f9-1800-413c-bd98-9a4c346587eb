package com.medusa.gruul.common.core.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.medusa.gruul.common.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

/**
 * 增强版Excel导出工具类，支持合计和序号功能
 * <AUTHOR>
 */
@Slf4j
public class EnhancedExcelUtils {

    /**
     * 导出带合计和序号的Excel数据
     * @param sourceList 源数据列表
     * @param fileName 文件名
     * @param function 数据转换函数
     * @param summaryFields 需要合计的字段名数组
     * @param <S> 源数据类型
     * @param <T> 目标数据类型
     */
    public static <S, T> void exportDataWithSummary(List<S> sourceList, String fileName, 
                                                   Function<S, T> function, String[] summaryFields) {
        if (CollectionUtils.isEmpty(sourceList)) {
            throw new ServiceException("数据为空", SystemCode.DATA_NOT_EXIST_CODE);
        }
        
        long startTime = System.currentTimeMillis();
        fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "_" + fileName;
        
        List<T> list = new ArrayList<>(sourceList.size());
        
        // 数据转换并添加序号
        for (int i = 0; i < sourceList.size(); i++) {
            S item = sourceList.get(i);
            T target = function.apply(item);
            if (target != null) {
                // 设置序号（如果目标对象有index字段）
                setIndexIfExists(target, i + 1);
                list.add(target);
            }
        }
        
        // 添加合计行
        if (summaryFields != null && summaryFields.length > 0) {
            T summaryRow = createSummaryRow(list, summaryFields);
            if (summaryRow != null) {
                list.add(summaryRow);
            }
        }
        
        Class<?> tClass = list.get(0).getClass();
        boolean check = exportToExcel(list, fileName, (Class<T>) tClass);
        
        if (!check) {
            throw new ServiceException("导出失败", SystemCode.FAILURE.getCode());
        }
        
        long endTime = System.currentTimeMillis();
        log.info("Excel导出成功 - 文件名: {}, 数据量: {}, 耗时: {}ms", fileName, sourceList.size(), (endTime - startTime));
    }
    
    /**
     * 设置序号字段（如果存在）
     */
    private static <T> void setIndexIfExists(T target, int index) {
        try {
            Field indexField = target.getClass().getDeclaredField("index");
            indexField.setAccessible(true);
            indexField.set(target, index);
        } catch (Exception e) {
            // 如果没有index字段，忽略
        }
    }
    
    /**
     * 创建合计行
     */
    private static <T> T createSummaryRow(List<T> dataList, String[] summaryFields) {
        if (dataList.isEmpty()) {
            return null;
        }
        
        try {
            Class<?> clazz = dataList.get(0).getClass();
            T summaryRow = (T) clazz.newInstance();
            
            // 计算合计值
            Map<String, Object> summaryValues = calculateSummary(dataList, summaryFields);
            
            // 设置合计行的值
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                String fieldName = field.getName();
                
                if (summaryValues.containsKey(fieldName)) {
                    field.set(summaryRow, summaryValues.get(fieldName));
                } else if (isFirstTextField(field, clazz)) {
                    // 第一个文本字段设置为"合计"
                    field.set(summaryRow, "合计");
                } else if (field.getType() == String.class) {
                    // 其他文本字段设置为空字符串
                    field.set(summaryRow, "");
                }
            }
            
            return summaryRow;
        } catch (Exception e) {
            log.error("创建合计行失败", e);
            return null;
        }
    }
    
    /**
     * 计算合计值
     */
    private static <T> Map<String, Object> calculateSummary(List<T> dataList, String[] summaryFields) {
        Map<String, Object> summaryValues = new HashMap<>();
        
        for (String fieldName : summaryFields) {
            try {
                Field field = dataList.get(0).getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                
                if (field.getType() == BigDecimal.class) {
                    BigDecimal total = BigDecimal.ZERO;
                    for (T item : dataList) {
                        BigDecimal value = (BigDecimal) field.get(item);
                        if (value != null) {
                            total = total.add(value);
                        }
                    }
                    summaryValues.put(fieldName, total);
                } else if (field.getType() == Integer.class || field.getType() == int.class) {
                    Integer total = 0;
                    for (T item : dataList) {
                        Integer value = (Integer) field.get(item);
                        if (value != null) {
                            total += value;
                        }
                    }
                    summaryValues.put(fieldName, total);
                } else if (field.getType() == Long.class || field.getType() == long.class) {
                    Long total = 0L;
                    for (T item : dataList) {
                        Long value = (Long) field.get(item);
                        if (value != null) {
                            total += value;
                        }
                    }
                    summaryValues.put(fieldName, total);
                } else if (field.getType() == Double.class || field.getType() == double.class) {
                    Double total = 0.0;
                    for (T item : dataList) {
                        Double value = (Double) field.get(item);
                        if (value != null) {
                            total += value;
                        }
                    }
                    summaryValues.put(fieldName, total);
                }
            } catch (Exception e) {
                log.warn("计算字段 {} 的合计值失败", fieldName, e);
            }
        }
        
        return summaryValues;
    }
    
    /**
     * 判断是否是第一个文本字段（用于显示"合计"）
     */
    private static boolean isFirstTextField(Field field, Class<?> clazz) {
        if (field.getType() != String.class) {
            return false;
        }
        
        // 跳过index字段
        if ("index".equals(field.getName())) {
            return false;
        }
        
        // 查找第一个有ApiModelProperty注解的String字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            if (f.getType() == String.class && 
                !f.getName().equals("index") && 
                f.getAnnotation(ApiModelProperty.class) != null) {
                return f.equals(field);
            }
        }
        
        return false;
    }
    
    /**
     * 导出到Excel文件
     */
    private static <T> boolean exportToExcel(List<T> data, String fileName, Class<T> tClass) {
        if (data == null || data.isEmpty()) {
            data = new ArrayList<>();
        }
        
        Field[] declaredFields = tClass.getDeclaredFields();
        Map<String, String> headerAlias = new LinkedHashMap<>();
        
        for (Field f : declaredFields) {
            ApiModelProperty annotation = f.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                headerAlias.put(f.getName(), annotation.value());
            }
        }
        
        ExcelWriter excelWriter = ExcelUtil.getWriter(true);
        if (!headerAlias.isEmpty()) {
            excelWriter.setHeaderAlias(headerAlias);
            excelWriter.setOnlyAlias(true);
        }
        
        excelWriter.write(data, true);
        
        try {
            ServletRequestAttributes servletRequestAttributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletResponse response = Objects.requireNonNull(servletRequestAttributes).getResponse();
            Objects.requireNonNull(response).setHeader(HttpHeaders.CONTENT_DISPOSITION, 
                URLUtil.encode("attachment;fileName=" + fileName + ".xlsx"));
            excelWriter.flush(response.getOutputStream(), true);
            return true;
        } catch (IOException e) {
            log.error("Excel导出失败", e);
        } finally {
            excelWriter.close();
        }
        
        return false;
    }
}
