package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum DeleteEnum {

    /**
     * 已删除
     */
    YES(1,"已删除"),
    /**
     * 未删除
     */
    NO(0,"未删除");


    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    DeleteEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
