package com.medusa.gruul.common.data.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @Description: 填充器
 * @Author: alan
 * @Date: 2019/8/31 9:37
 */
@Slf4j
@Component
public class IMetaObjectHandler implements MetaObjectHandler {

	@Override
	public void insertFill(MetaObject metaObject) {
        metaObject.setValue("deleted", false);
        metaObject.setValue("createTime", LocalDateTime.now());
        metaObject.setValue("updateTime", LocalDateTime.now());
    }

	@Override
	public void updateFill(MetaObject metaObject) {
        log.info("start update  fill ....");
        metaObject.setValue("updateTime", LocalDateTime.now());
	}

}
