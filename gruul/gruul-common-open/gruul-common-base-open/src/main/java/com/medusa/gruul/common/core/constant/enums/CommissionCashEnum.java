package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 提现单状态
 * @Date: Created in 20:33 2025/4/23
 */
@Getter
public enum CommissionCashEnum {


    /**
     * 审核中
     */
    IN_REVIEW(0,"审核中"),
    /**
     * 审核通过
     */
    APPROVED(1,"审核通过"),
    /**
     * 已驳回
     */
    REJECT(-1,"已驳回"),

    /**
     * 提现失败
     */
    FAIL(-2,"提现失败"),

    /**
     * 提现成功
     */
    SUCCESS(2,"提现成功");


    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    CommissionCashEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
