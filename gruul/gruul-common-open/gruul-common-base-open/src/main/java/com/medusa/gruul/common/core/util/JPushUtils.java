package com.medusa.gruul.common.core.util;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.google.gson.JsonObject;
import com.medusa.gruul.common.core.constant.enums.PushEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:30 2024/5/17
 */
@Slf4j
public class JPushUtils {

    private static String appKey = "5f977cf1dd926f8e894e74af";

    private static String masterSecret = "59c06b98e28c478d3d460d82";

    private static JPushClient jPushClient = new JPushClient(masterSecret,appKey);


    /**
     *
     * @param platForm android/ios
     * @param pattern 根据alias（根据别名）/registrationId（设备id）推送 / 全局推送（推所有用户）
     * @param title 标题
     * @param alert 内容
     * @param alias 别名
     * @param registrationIds 设备Id
     * @param key 拓展字段key值
     * @param value 拓展字段value
     * @param indent 是否点击消息推送打开到指定的页面
     * @return
     */
    public static Long jiguangSend_batch( String platForm,
                                          String pattern,
                                          String title,
                                          String alert,
                                          String[] alias,
                                          String[] registrationIds,
                                          String key,
                                          String value,
                                          String indent) {
        PushResult pushResult = null;
        try {

            JsonObject jsonObject = new JsonObject();
            if(StringUtils.isNotEmpty(indent)){
                jsonObject.addProperty("url",indent);
            }else{
                jsonObject.addProperty("url","");
            }
            PushPayload pushPayload = JPushUtils.pushBatch(title,alert, alias, registrationIds, platForm, pattern, key, value, jsonObject);
            pushResult = jPushClient.sendPush(pushPayload);
            if (pushResult.getResponseCode() == 200) {
                return pushResult.msg_id;
            }
        } catch (APIConnectionException | APIRequestException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     *
     * @param title 标题
     * @param alert 内容
     * @param alias 别名
     * @param registrationIds 注册Id
     * @param platForm android/ios
     * @param pattern 根据alias/registrationIds推送 / 全局推送（推所有用户）
     * @param key 拓展字段key值
     * @param value 拓展字段value
     * @param indent
     * @return 是否点击消息推送打开到指定的页面
     */
    private static PushPayload pushBatch(String title,String alert,String[] alias, String[] registrationIds,
                                         String platForm, String pattern, String key, String value, JsonObject indent){
        // 创建一个IosAlert对象，可指定APNs的alert、title等字段
        return PushPayload.newBuilder()
                // 指定要推送的平台
                .setPlatform(platForm.equals(PushEnum.Android.getDesc())? Platform.android()
                        :platForm.equals(PushEnum.IOS.getDesc())?Platform.ios():Platform.all())
                // 指定推送的接收对象，all代表所有人，也可以指定已经设置成功的tag或alias或该应应用客户端调用接口获取到的registration
                .setAudience(pattern.equals(PushEnum.Alias.getDesc()) ? Audience.alias(alias) :
                        pattern.equals(PushEnum.RegistrationId.getDesc()) ? Audience.registrationId(registrationIds) :
                                Audience.all())
                // jpush的通知
                .setNotification(Notification.newBuilder()
                        // 指定当前推送的通知
                        .addPlatformNotification(platForm.equals(PushEnum.Android.getDesc())? AndroidNotification.newBuilder().
                                setAlert(alert)
                                .setTitle(title)
                                .setIntent(indent)
                                .addExtra(key,value)
                                .build():
                                IosNotification.newBuilder().setThreadId(title).setAlert(alert).build())
                        .build())
                .setOptions(Options.newBuilder()
                        // 此字段的值是用来指定本推送要推送的apns环境，false表示开发，true表示生产；对android和自定义消息无意义
                        .setApnsProduction(false)
                        // 此字段是给开发者自己给推送编号，方便推送者分辨推送记录
                        .setSendno(1)
                        // 此字段的值是用来指定本推送的离线保存时长，如果不传此字段则默认保存一天，最多指定保留十天；
                        .setTimeToLive(86400)
                        .build()).
                build();

    }

}
