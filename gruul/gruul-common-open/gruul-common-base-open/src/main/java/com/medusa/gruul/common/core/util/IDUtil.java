package com.medusa.gruul.common.core.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;

/**
 * 生成雪花id的辅助类
 */
public class IDUtil {

    /**
     * 终端ID
     */
    private static Long workerId = 1L;

    /**
     * 数据中心ID
     */
    private static Long dataCenterId = 1L;

    /**
     * 获取雪花id
     * @return
     */
    public static Long getId(){
        Snowflake snowflake = IdUtil.createSnowflake(workerId, dataCenterId);
        return snowflake.nextId();
    }

    /**
     * 获取雪花id
     * @return
     */
    public static Long getId(Long dataCenterId){
        Snowflake snowflake = IdUtil.createSnowflake(workerId, dataCenterId);
        return snowflake.nextId();
    }

}
