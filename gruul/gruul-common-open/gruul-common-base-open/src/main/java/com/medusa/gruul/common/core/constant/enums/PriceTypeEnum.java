package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:09 2025/3/10
 */
@Getter
public enum PriceTypeEnum {

    MEMBER(1, "会员价"),
    REPEAT(2, "复购价"),
    ACTUAL(3, "实售价");

    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    PriceTypeEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
