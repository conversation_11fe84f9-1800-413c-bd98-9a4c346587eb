package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 微信消息模板模板类型
 * @Date: Created in 16:12 2024/11/12
 */
@Getter
public enum WxMessageTemplateTypeEnum {
    /**
     * 买家通知
     */
    MINI("1","订阅消息"),
    /**
     * 商家通知
     */
    MP("2","公众号模板消息");

    @EnumValue
    /**
     * 值
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;

    WxMessageTemplateTypeEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
