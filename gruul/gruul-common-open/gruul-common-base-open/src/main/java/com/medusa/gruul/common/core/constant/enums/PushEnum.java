package com.medusa.gruul.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:32 2024/5/17
 */
@Getter
public enum PushEnum {
    All("0","all"),
    Android("1","android"),
    IOS("2","ios"),
    <PERSON><PERSON>("3","alias"),
    RegistrationId("4","registrationId"),
    ;

    @EnumValue
    /**
     * 值
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;
    PushEnum(String  code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
