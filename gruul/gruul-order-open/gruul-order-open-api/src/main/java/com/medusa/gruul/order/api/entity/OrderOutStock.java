package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:47 2024/10/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order_out_stock")
@ApiModel(value = "OrderOutStock对象", description = "出库单表")
public class OrderOutStock extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    @TableField("order_id")
    private String orderId;

    /**
     * 商品标识
     */
    @ApiModelProperty(value = "商品标识")
    @TableField("product_code")
    private String productCode;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    @TableField("employee_id")
    private String employeeId;
    /**
     * 职员标识
     */
    @ApiModelProperty(value = "职员标识")
    @TableField("employee_out_id")
    private String employeeOutId;
    /**
     * 职员名称
     */
    @ApiModelProperty(value = "职员名称")
    @TableField("employee_name")
    private String employeeName;
    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    @TableField("department_id")
    private String departmentId;
    /**
     * 部门标识
     */
    @ApiModelProperty(value = "部门标识")
    @TableField("department_code")
    private String departmentCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField("department_name")
    private String departmentName;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    @TableField("stock_id")
    private String stockId;
    /**
     * 仓库标识
     */
    @ApiModelProperty(value = "仓库标识")
    @TableField("stock_code")
    private String stockCode;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    @TableField("stock_name")
    private String stockName;
    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    @TableField("store_front_id")
    private String storeFrontId;
    /**
     * 门店标识
     */
    @ApiModelProperty(value = "门店标识")
    @TableField("store_front_code")
    private String storeFrontCode;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    @TableField("store_front_name")
    private String storeFrontName;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("account_id")
    private String accountId;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField("account_name")
    private String accountName;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    @TableField("out_time")
    private String outTime;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField("number")
    private Integer number;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField("price")
    private BigDecimal price;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "会员id")
    @TableField("user_id")
    private String userId;


    /**
     * 发送状态
     */
    @ApiModelProperty(value = "发送状态0->未发送，1—>已发送")
    @TableField("send_status")
    private Integer sendStatus;

    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额")
    @TableField("all_amount")
    private BigDecimal allAmount;



}
