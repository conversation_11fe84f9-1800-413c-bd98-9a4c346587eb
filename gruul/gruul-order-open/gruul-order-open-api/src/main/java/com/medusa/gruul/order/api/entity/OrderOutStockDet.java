package com.medusa.gruul.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:02 2024/10/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order_out_stock_det")
@ApiModel(value = "OrderOutStockDet对象", description = "出库单明细表")
public class OrderOutStockDet  extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 本店店铺id
     */
    @ApiModelProperty(value = "本店店铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @TableField("main_id")
    private String mainId;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @TableField("product_id")
    private String productId;
    /**
     *商品skuId
     */
    @ApiModelProperty(value = "商品skuId")
    @TableField("sku_id")
    private String skuId;
    /**
     * 商品标识
     */
    @ApiModelProperty(value = "商品标识")
    @TableField("product_code")
    private String productCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @TableField("product_name")
    private String productName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField("number")
    private Integer number;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField("price")
    private BigDecimal price;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 规格2-颜色
     */
    @ApiModelProperty(value = "规格2-颜色")
    @TableField("specs2")
    private String specs2;

    /**
     * 关联商品id
     */
    @ApiModelProperty(value = "关联商品id")
    @TableField("link_product_id")
    private String linkProductId;
    /**
     *关联商品skuId
     */
    @ApiModelProperty(value = "关联商品skuId")
    @TableField("link_sku_id")
    private String linkSkuId;
    /**
     * 关联商品标识
     */
    @ApiModelProperty(value = "关联商品标识")
    @TableField("link_product_code")
    private String linkProductCode;
    /**
     * 关联商品名称
     */
    @ApiModelProperty(value = "关联商品名称")
    @TableField("link_product_name")
    private String linkProductName;

}
