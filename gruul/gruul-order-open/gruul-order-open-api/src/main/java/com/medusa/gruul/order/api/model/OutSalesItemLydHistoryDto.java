package com.medusa.gruul.order.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 商品规格数量Map
 *
 * <AUTHOR>
 * @date 2019/10/4 14:36
 */
@Data
@ApiModel(value = "商品规格数量Map", description = "商品规格数量Map")
public class OutSalesItemLydHistoryDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("商品名称")
    private String productName;

    @NotNull
    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("商品id")
    private Long productId;

    @ApiModelProperty("销售价格")
    private BigDecimal productPrice;

    @ApiModelProperty("购买数量")
    private BigDecimal productQuantity;

    @ApiModelProperty("商品促销分解金额")
    private BigDecimal promotionAmount;

    @ApiModelProperty("优惠券优惠分解金额")
    private BigDecimal couponAmount;

    @ApiModelProperty("该商品经过优惠后的最终金额")
    private BigDecimal realAmount;

    @NotNull
    @ApiModelProperty("商品SKU ID")
    private Long skuId;

    @ApiModelProperty("积分商品id")
    private String integralProductId;

    @ApiModelProperty("店铺id")
    private String shopId;

    @ApiModelProperty("商品类型")
    private Integer productType;
}
