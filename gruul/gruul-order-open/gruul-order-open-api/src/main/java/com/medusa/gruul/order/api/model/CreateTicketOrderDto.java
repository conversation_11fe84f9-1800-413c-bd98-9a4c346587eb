package com.medusa.gruul.order.api.model;

import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.enums.PayTypeEnum;
import com.medusa.gruul.order.api.enums.SourceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: 通惠证订单dto
 * @Date: Created in 16:22 2023/8/28
 */
@Data
@ApiModel(value = "创建通惠证订单参数", description = "创建通惠证订单参数")
public class CreateTicketOrderDto {

    @NotNull(message = "支付方式不能为空")
    @ApiModelProperty("支付方式")
    private PayTypeEnum payType;

    @NotNull(message = "订单来源不能为空")
    @ApiModelProperty(value = "订单来源")
    private SourceTypeEnum sourceType;


    @NotNull(message = "订单类型不能为空")
    @ApiModelProperty(value = "订单类型")
    private OrderTypeEnum orderType;

    @NotEmpty(message = "通惠证不能为空")
    @ApiModelProperty("通惠证数量Map")
    private List<TicketItemDto> itemDtoList;

    @ApiModelProperty(hidden = true)
    public List<Long> getItemTicketIds() {
        return new ArrayList<Long>(itemDtoList.stream().map(TicketItemDto::getTicketId).collect(Collectors.toSet()));
    }
}
