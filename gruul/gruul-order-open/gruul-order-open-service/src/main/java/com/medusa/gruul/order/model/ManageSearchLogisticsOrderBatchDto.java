package com.medusa.gruul.order.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:09 2024/8/20
 */
@Data
public class ManageSearchLogisticsOrderBatchDto implements Serializable {

    private static final long serialVersionUID = -3263921252635611456L;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty("仓库id")
    private Long warehouseId;

}
