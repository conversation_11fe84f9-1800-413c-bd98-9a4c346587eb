package com.medusa.gruul.order.model;

import com.medusa.gruul.order.api.model.ItemDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Get cost dto.
 * <AUTHOR>
 */
@Data
public class ProductRankingDto {
    @ApiModelProperty("商品名字")
    private String nick;
    @ApiModelProperty("价格")
    private BigDecimal price;
    @ApiModelProperty("数量或总金额")
    private Integer tradingVolume;
    @ApiModelProperty("图片路径")
    private String url;


}
