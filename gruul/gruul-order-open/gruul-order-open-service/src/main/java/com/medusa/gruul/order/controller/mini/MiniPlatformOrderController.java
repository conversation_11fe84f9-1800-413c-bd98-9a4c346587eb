package com.medusa.gruul.order.controller.mini;

import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.order.model.*;
import com.medusa.gruul.order.service.IMiniOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/mini-platform/order")
@Api(tags = "小程序端商家订单接口")
public class MiniPlatformOrderController {
    @Resource
    private IMiniOrderService miniOrderService;


    @ApiOperation(value = "查询会员历史订单", notes = "查询会员历史订单")
    @GetMapping("/searchHistory")
    public Result<PageUtils<ApiManageHistoryOrderVo>> searchHistory(ApiSearchHistoryOrderDto dto) {
        PageUtils<ApiManageHistoryOrderVo> page = miniOrderService.searchHistoryOrder(dto);
        return Result.ok(page);
    }


}
