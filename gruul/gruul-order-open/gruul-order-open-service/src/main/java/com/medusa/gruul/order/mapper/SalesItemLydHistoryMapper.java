package com.medusa.gruul.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.api.entity.SalesItemLydHistory;
import com.medusa.gruul.order.model.ProductRankingDto;
import com.medusa.gruul.order.model.SimpleOrderItemVo;
import com.medusa.gruul.order.model.SimpleSalesItemVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface SalesItemLydHistoryMapper extends BaseMapper<SalesItemLydHistory> {


    List<SimpleSalesItemVo> selectSimpleItemVoBySalesId(@Param(value = "salesId") Long salesId);

    /**
     * selectByOrderId
     *
     * @param orderId the order id
     * @return java.util.List<com.medusa.gruul.order.api.entity.OrderItem> list
     * <AUTHOR>
     * @date 2019 /12/1 17:04
     */
    List<OrderItem> selectByOrderId(@Param(value = "orderId") Long orderId);

}
