package com.medusa.gruul.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.api.entity.SalesItemLydHistory;
import com.medusa.gruul.order.mapper.OrderItemMapper;
import com.medusa.gruul.order.mapper.SalesItemLydHistoryMapper;
import com.medusa.gruul.order.model.ProductRankingDto;
import com.medusa.gruul.order.service.IOrderItemService;
import com.medusa.gruul.order.service.ISalesItemLydHistoryService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 订单中所包含的商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019 -09-02
 */
@Service
public class SalesItemLydHistoryServiceImpl extends ServiceImpl<SalesItemLydHistoryMapper, SalesItemLydHistory> implements ISalesItemLydHistoryService {

}
