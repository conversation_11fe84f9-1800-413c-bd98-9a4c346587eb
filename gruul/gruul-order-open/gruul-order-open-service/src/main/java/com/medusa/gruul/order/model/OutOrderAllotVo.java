package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 21:11 2024/12/25
 */
@ApiModel("生成外部系统调拨单Vo")
@Data
public class OutOrderAllotVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "经手人id")
    private String employeeId;

    @ApiModelProperty(value = "经手人名称")
    private String employeeName;

    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;


    @ApiModelProperty(value = "调出仓库标识")
    private String stockOutCode;

    @ApiModelProperty(value = "调出仓库名称")
    private String stockOutName;


    @ApiModelProperty(value = "调入仓库标识")
    private String stockInCode;

    @ApiModelProperty(value = "调入仓库名称")
    private String stockInName;

    @ApiModelProperty(value = "出库时间")
    private String outTime;

    @ApiModelProperty(value = "调拨单详情")
    private List<OutOrderAllotDetVo> outOrderAllotDet;
}
