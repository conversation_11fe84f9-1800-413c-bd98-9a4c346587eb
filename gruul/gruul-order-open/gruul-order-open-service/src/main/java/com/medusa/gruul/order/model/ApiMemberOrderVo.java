package com.medusa.gruul.order.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:35 2025/3/28
 */
@Data
@ApiModel(value = "会员消费记录")
public class ApiMemberOrderVo {

    @ApiModelProperty(value = "订单id")
    private Long id;

    @ApiModelProperty(value = "会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "单据金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付时间")
    private String payTime;

}
