<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.order.mapper.OrderMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.order.api.entity.Order">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="user_note" property="userNote"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="discounts_amount" property="discountsAmount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="freight_amount" property="freightAmount"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="pay_type" property="payType"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="pay_time" property="payTime"/>
        <result column="source_type" property="sourceType"/>
        <result column="close_time" property="closeTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="status" property="status"/>
        <result column="note" property="note"/>
        <result column="comment_time" property="commentTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="custom_form" property="customForm"/>
        <result column="full_scale_id" property="fullScaleId"/>
        <result column="full_scale_amount" property="fullScaleAmount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_transaction_id" property="refundTransactionId"/>
        <result column="estimated_delivery_time" property="estimatedDeliveryTime"/>
    </resultMap>
    <resultMap id="OrderDataVoMap" type="com.medusa.gruul.order.model.OrderDataVo">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="shop_id" property="shopId"/>
    </resultMap>
    <resultMap id="ManageOrderExcelMap" type="com.medusa.gruul.order.model.ManageOrderExcelVo">
        <result column="order_id" property="orderId"/>
        <result column="create_time" property="createTime"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="youhui_price" property="youhuiPrice"/>
        <result column="deduction_price" property="deductionPrice"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="status" property="status"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="shop_name" property="shopName"/>
        <result column="product_name" property="productName"/>
        <result column="specs" property="specs"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_price" property="productPrice"/>
        <result column="real_amount" property="realAmount"/>
        <result column="warehouse_full_name" property="warehouseFullName"/>
        <result column="note" property="note"/>
        <result column="sale_describe" property="saleDescribe"/>
    </resultMap>
    <resultMap id="ApiMemberOrderMap" type="com.medusa.gruul.order.model.ApiMemberOrderVo">
        <result column="id" property="id"/>
        <result column="member_level" property="memberLevel"/>
        <result column="user_id" property="userId"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_time" property="payTime"/>
    </resultMap>
    <resultMap id="OrderItemVoMap" type="com.medusa.gruul.order.model.ManageOrderItemVo">
        <result column="order_id" property="orderId"/>
        <result column="product_name" property="productName"/>
        <result column="product_price" property="productPrice"/>
        <result column="real_amount" property="realAmount"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="account_name" property="accountName"/>
    </resultMap>
    <!-- 小程序端订单列表查询映射结果 -->
    <resultMap id="ManageOrderTradeDtoMap" type="com.medusa.gruul.order.model.ManageOrderTradeDto">
        <result column="transactionVolume" property="transactionVolume"/>
        <result column="turnover" property="turnover"/>
        <result column="newdate" property="date"/>
    </resultMap>

    <!-- 小程序端历史订单列表查询映射结果 -->
    <resultMap id="ApiHistoryOrderVoMap" type="com.medusa.gruul.order.model.ApiManageHistoryOrderVo">
        <id column="order_id" property="orderId"/>
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="all_integral" property="allIntegral"/>
        <result column="pay_type" property="payType"/>
        <result column="status" property="status"/>
        <result column="expire_time" property="expireTime"/>
        <result column="wx_deliver_status" property="wxDeliverStatus"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="source" property="source"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        is_deleted,
        id, user_id, user_name, user_avatar_url, user_note, type, total_amount, discounts_amount,
         pay_amount, freight_amount, promotion_amount, coupon_id,
        coupon_amount, pay_type,transaction_id, pay_time, source_type, status,close_time,expire_time,
         note,
        comment_time, complete_time,custom_form, full_scale_id, full_scale_amount,
         refund_amount,refund_transaction_id,estimated_delivery_time
    </sql>


    <!-- 小程序端订单列表查询映射结果 -->
    <resultMap id="ApiOrderVoMap" type="com.medusa.gruul.order.model.ApiOrderVo">
        <id column="id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="all_integral" property="allIntegral"/>
        <result column="pay_type" property="payType"/>
        <result column="status" property="status"/>
        <result column="expire_time" property="expireTime"/>
        <result column="wx_deliver_status" property="wxDeliverStatus"/>
        <result column="transaction_id" property="transactionId"/>
        <collection column="{orderId=id}"
                    property="itemVoList" javaType="ArrayList"
                    ofType="com.medusa.gruul.order.model.SimpleOrderItemVo"
                    select="com.medusa.gruul.order.mapper.OrderItemMapper.selectSimpleOrderItemVoByOrderId"/>
    </resultMap>
    <select id="searchApiOrderVoPage" resultMap="ApiOrderVoMap">
        SELECT
        o.id,o.type,o.create_time,o.pay_amount,o.pay_type,o.expire_time ,o.status,o.all_integral,o.wx_deliver_status,o.transaction_id
        FROM
        t_order as o
        WHERE
        o.is_deleted =0
        AND o.user_id = #{userId}
        <if test="statusList!=null and statusList.size>0">
            AND o.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="statusList.contains(105) or statusList.contains(104) or statusList.contains(104) ">
                AND o.type != 103
            </if>
        </if>
        <if test="searchAfterOrder">
            AND o.id in (select ta.receipt_bill_id from t_afs_order ta where ta.user_id = #{userId} )
        </if>
        AND o.type != 104
        ORDER BY create_time DESC
    </select>

    <update id="updateSendStatus">
        update t_order set send_status=#{sendStatus} where id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <update id="updateReceiveSyncStatus">
        update t_order set receive_sync_status=#{receiveSyncStatus} where id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <!-- PC管理端订单列表查询映射结果 -->
    <resultMap id="ManageOrderVoMap" type="com.medusa.gruul.order.model.ManageOrderVo">
        <id column="id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_post_code" property="receiverPostCode"/>
        <result column="receiver_province" property="receiverProvince"/>
        <result column="receiver_city" property="receiverCity"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="delivery_company" property="deliveryCompany"/>
        <result column="delivery_sn" property="deliverySn"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="all_integral" property="allIntegral"/>
        <result column="status" property="status"/>
        <result column="note" property="note"/>
        <result column="send_status" property="sendStatus"/>
        <result column="shop_id" property="shopId"/>
        <result column="youhui_price" property="youhuiPrice"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="deduction_price" property="deductionPrice"/>
        <result column="deduction_user" property="deductionUser"/>
        <result column="deduction_time" property="deductionTime"/>
        <collection column="id" property="itemVoList" javaType="ArrayList"
                    select="com.medusa.gruul.order.mapper.OrderItemMapper.selectSimpleOrderItemVoByOrderId">
        </collection>
    </resultMap>
    <resultMap id="ExternalOrderMap" type="com.medusa.gruul.order.model.ExternalOrder">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="real_user_id" property="realUserId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="user_note" property="userNote"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="discounts_amount" property="discountsAmount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="freight_amount" property="freightAmount"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_id" property="couponId"/>
        <result column="pay_type" property="payType"/>
        <result column="transaction_id" property="transactionId"/>

        <result column="pay_time" property="payTime"/>
        <result column="status" property="status"/>
        <result column="note" property="note"/>
        <result column="source_type" property="sourceType"/>
        <result column="close_time" property="closeTime"/>
        <result column="comment_time" property="commentTime"/>
        <result column="complete_time" property="completeTime"/>

        <result column="estimated_delivery_time" property="estimatedDeliveryTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="custom_form" property="customForm"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_transaction_id" property="refundTransactionId"/>
        <result column="warehouse_id" property="warehouseId"/>

        <collection column="id" property="orderItemList" javaType="ArrayList"
                    select="externalOrderItemList">
        </collection>
    </resultMap>
    <resultMap id="OrderItemMap" type="com.medusa.gruul.order.api.entity.OrderItem">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="order_id" property="orderId"/>
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_sn" property="productSn"/>
        <result column="product_price" property="productPrice"/>
        <result column="product_original_price" property="productOriginalPrice"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="real_amount" property="realAmount"/>
        <result column="specs" property="specs"/>
        <result column="provider_id" property="providerId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="sale_mode" property="saleMode"/>
    </resultMap>



    <!-- PC管理端订单列表查询映射结果 -->
    <resultMap id="transacTionOverviewMap" type="com.medusa.gruul.order.model.ManageOrderTradeDto">
        <result column="date" property="date"/>
        <result column="transactionVolume" property="transactionVolume"/>
        <result column="turnover" property="turnover"/>
    </resultMap>

    <resultMap id="OutReceiveOrderMap" type="com.medusa.gruul.order.model.OutReceiveOrderVo">
        <id column="id" property="id"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="department_code" property="departmentCode"/>
        <result column="department_name" property="departmentName"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 管理端订单汇总报表查询映射结果 -->
    <resultMap id="ManageOrderStaticMap" type="com.medusa.gruul.order.model.ManageOrderStaticVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="package_Qty" property="packageQty"/>
        <result column="package_Amount" property="packageAmount"/>
        <result column="product_Qty" property="productQty"/>
        <result column="product_Amount" property="productAmount"/>
        <result column="total_Qty" property="totalQty"/>
        <result column="total_Amount" property="totalAmount"/>
        <result column="member_Qty" property="memberQty"/>
        <result column="is_catalog" property="isCatalog"/>
        <result column="package_Verify_Qty" property="packageVerifyQty"/>
        <result column="package_Un_Verify_Qty" property="packageUnVerifyQty"/>
    </resultMap>


    <select id="searchManageOrderVoPage" resultMap="ManageOrderVoMap">
        SELECT
        *
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        LEFT JOIN t_platform_lyd_store_front as plsf ON plsf.id = o.store_front_id
        left join t_mini_account_extends as e on o.user_id = e.shop_user_id
        left join t_mini_account as ac on ac.user_id = e.user_id
        WHERE
        o.is_deleted = 0
        AND (od.is_deleted = 0 or od.is_deleted is null)
        <if test="dto.goodsName!=null and dto.goodsName!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM
            t_order_item as oi
            WHERE
            oi.is_deleted = 0
            AND oi.product_name LIKE concat('%',#{dto.goodsName},'%') )
        </if>
        <if test="dto.area!=null and dto.area!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM t_order_item as oi
            WHERE
            oi.is_deleted = 0
            and oi.sale_mode=#{dto.area})
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            AND o.user_name LIKE concat('%',#{dto.userName},'%')
        </if>
        <if test="dto.receiverName!=null and dto.receiverName!=''">
            AND od.receiver_name LIKE concat('%',#{dto.receiverName},'%')
        </if>
        <if test="dto.receiverPhone!=null and dto.receiverPhone!=''">
            AND od.receiver_phone LIKE concat('%',#{dto.receiverPhone},'%')
        </if>
        <if test="dto.orderId!=null  and dto.orderId!=''">
            AND o.id LIKE concat('%',#{dto.orderId},'%')
        </if>
        <if test="dto.deliverySn !=null  and dto.deliverySn!=''">
            AND od.delivery_sn LIKE concat('%',#{dto.deliverySn},'%')
        </if>
        <if test="dto.lineId !=null and dto.lineId!=''">
            AND od.line_id = #{dto.lineId}
        </if>
        <if test="dto.deliverType == 0">
            AND od.delivery_type != 102
        </if>
        <if test="dto.deliverType == 100">
            AND od.delivery_type = 100
        </if>
        <if test="dto.deliverType == 101">
            AND od.delivery_type = 101
        </if>
        <if test="dto.deliverType == 102">
            AND od.delivery_type = 102
        </if>
        <if test='dto.remarkType == 1'>
            AND o.note != ''
        </if>
        <if test='dto.remarkType == 2'>
            AND o.note = ''
        </if>
        <if test="dto.note !=null and dto.note !=''">
            AND o.note LIKE concat('%',#{dto.note},'%')
        </if>
        <if test="dto.phone !=null and dto.phone !=''">
            AND ac.phone LIKE concat('%',#{dto.phone},'%')
        </if>
        <if test="dto.startTime!=null and dto.startTime!='' and dto.endTime!=null and dto.endTime!=''">
            AND o.pay_time BETWEEN concat(#{dto.startTime},' 00:00:00') AND concat(#{dto.endTime},' 23:59:59')
        </if>
        <if test="startDate!=null and endDate!=null">
            AND o.create_time BETWEEN concat(#{startDate},' 00:00:00') AND concat(#{endDate},' 23:59:59')
        </if>
        <if test='dto.sendBillId == -1'>
            AND od.send_bill_id IS NULL
        </if>
        <if test="dto.sendBillId!=null and dto.sendBillId!=0 and dto.sendBillId!=-1">
            AND od.send_bill_id = #{dto.sendBillId}
        </if>
        <if test="statusList!=null and statusList.size>0">
            AND o.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.shopIds!=null and dto.shopIds.size>0">
            AND o.shop_id in
            <foreach collection="dto.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="dto.shopIds2!=null and dto.shopIds2.size>0">
            AND o.shop_id in
            <foreach collection="dto.shopIds2" item="shopId2" open="(" separator="," close=")">
                #{shopId2}
            </foreach>
        </if>
        <if test="dto.storeFrontName!=null and dto.storeFrontName!=''">
            AND plsf.store_full_name LIKE concat('%',#{dto.storeFrontName},'%')
        </if>
        ORDER BY
        o.create_time DESC
    </select>
    <select id="searchExternalOrder" resultMap="ExternalOrderMap">
        SELECT
        a.*, c.id as real_user_id
        FROM
        t_order a left join t_mini_account_extends b on a.user_id = b.shop_user_id
        left join t_mini_account c on b.user_id = c.user_id
        where  a.is_deleted='0' and (ISNULL(a.send_status) || a.send_status!='1')
        <if test="statusList!=null and statusList.size>0">
            AND status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="searchOutReceiveOrder" resultMap="OutReceiveOrderMap">
        SELECT
        t1.id AS id,
        t2.employee_id AS employee_id,
        t2.employee_name AS employee_name,
        t2.department_code AS department_code,
        t2.department_name AS department_name,
        DATE_FORMAT( t1.pay_time, '%Y-%m-%d' ) AS pay_time,
        t1.pay_amount AS pay_amount,
        '营销系统客户线上付款' AS remark
        FROM
        t_order t1
        LEFT JOIN
        t_platform_account_info t2 ON t1.account_id = t2.id
        where
        t1.is_deleted = '0'
        and (ISNULL(t1.receive_sync_status) || t1.receive_sync_status!='1')
        AND ifnull( t1.account_id, '' )!= ''
        <if test="statusList!=null and statusList.size>0">
            AND t1.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY t1.create_time DESC
    </select>



    <select id="externalOrderItemList" resultMap="OrderItemMap">
        SELECT
            *
        FROM t_order_item
        WHERE
            is_deleted = 0 AND order_id = #{id}
    </select>



    <!-- 通用查询映射结果 -->
    <resultMap id="OrderVoMap" type="com.medusa.gruul.order.api.model.OrderVo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="user_note" property="userNote"/>
        <result column="type" property="type"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="discounts_amount" property="discountsAmount"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="freight_amount" property="freightAmount"/>
        <result column="promotion_amount" property="promotionAmount"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_amount" property="couponAmount"/>
        <result column="youhui_price" property="youhuiPrice"/>
        <result column="pay_type" property="payType"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="pay_time" property="payTime"/>
        <result column="source_type" property="sourceType"/>
        <result column="close_time" property="closeTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="status" property="status"/>
        <result column="note" property="note"/>
        <result column="comment_time" property="commentTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="custom_form" property="customForm"/>
        <result column="full_scale_id" property="fullScaleId"/>
        <result column="full_scale_amount" property="fullScaleAmount"/>
        <result column="refund_transaction_id" property="refundTransactionId"/>
        <result column="estimated_delivery_time" property="estimatedDeliveryTime"/>
        <result column="wx_deliver_status" property="wxDeliverStatusEnum"/>
        <result column="deduction_price" property="deductionPrice"/>
        <result column="deduction_user" property="deductionUser"/>
        <result column="deduction_time" property="deductionTime"/>
        <result column="coupon_id" property="couponId"/>
        <association column="id" property="orderDelivery"
                     select="com.medusa.gruul.order.mapper.OrderDeliveryMapper.selectById">
        </association>
        <collection column="id" property="orderItemList" javaType="ArrayList"
                    select="com.medusa.gruul.order.mapper.OrderItemMapper.selectOrderItemVoByOrderId">
        </collection>
    </resultMap>
    <resultMap id="ApiIntegralOrderVoMap" type="com.medusa.gruul.order.model.ApiIntegralOrderVo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="product_name" property="productName"/>
        <result column="specs" property="specs"/>
        <result column="status" property="status"/>
        <result column="all_product_quantity" property="allProductQuantity"/>
        <result column="all_integral" property="allIntegral"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pic" property="pic"/>
    </resultMap>
    <select id="selectOrderVoById" resultMap="OrderVoMap">
        SELECT
            *
        FROM t_order
        WHERE
            is_deleted = 0 AND id = #{orderId}
    </select>
    <select id="selectOrderVoListByIds" resultMap="OrderVoMap">
        SELECT
        *
        FROM t_order
        WHERE
        is_deleted = 0 AND id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <!-- 小程序端订单列表查询映射结果 -->
    <resultMap id="SimpleOrderVoMap" type="com.medusa.gruul.order.model.SimpleOrderVo">
        <id column="id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_province" property="receiverProvince"/>
        <result column="receiver_city" property="receiverCity"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="send_bill_name" property="sendBillName"/>
        <result column="user_note" property="userNote"/>
        <result column="custom_form" property="customForm"/>
        <collection column="id" property="itemVoList" javaType="ArrayList"
                    ofType="com.medusa.gruul.order.model.SimpleOrderItemVo"
                    select="com.medusa.gruul.order.mapper.OrderItemMapper.selectSimpleOrderItemVoByOrderId">
        </collection>
    </resultMap>
    <select id="searchSimpleOrderVoForGroupPage" resultMap="SimpleOrderVoMap">
        SELECT
            *
        FROM
            t_order as o
                LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
            o.status = #{status}
          AND o.user_id = #{userId}
        ORDER BY o.create_time DESC
    </select>

    <!-- 返回给物流模块的数据 -->
    <resultMap id="GetOrderListMap" type="com.medusa.gruul.order.api.model.GetOrderListDto">
        <id column="id" property="orderId"/>
        <result column="pay_amount" property="price"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="buyerNick"/>
        <result column="user_avatar_url" property="img"/>
        <result column="user_note" property="remark"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="delivery_type" property="deliverType"/>
        <result column="point_id" property="pointId"/>
        <result column="line_id" property="lineId"/>
        <result column="receiver_name" property="reName"/>
        <result column="receiver_phone" property="rePhone"/>
        <result column="receiver_detail_address" property="reAddress"/>
        <result column="receiver_province" property="rProvince"/>
        <result column="receiver_city" property="rCity"/>
        <result column="receiver_region" property="rRegion"/>
        <result column="custom_form" property="customForm"/>
        <collection column="id" property="afsOrderList"
                    select="com.medusa.gruul.order.mapper.AfsOrderMapper.selectByOrderId">
        </collection>
    </resultMap>
    <select id="selectOrderListByIds" resultMap="GetOrderListMap">
        SELECT
        *
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.id IN
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND o.is_deleted= 0
    </select>

    <select id="selectListByPointIdAndDate" resultType="com.medusa.gruul.order.api.entity.Order">
        SELECT
            o.*
        FROM
            t_order as o
                LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
            o.create_time BETWEEN #{start} AND #{end}
          AND o.is_deleted = 0
          AND o.status != 100
        AND od.point_id = #{pointId}
    </select>
    <select id="selectListByPointIdAndDateBetween" resultType="com.medusa.gruul.order.api.entity.Order">
        SELECT
            o.*
        FROM
            t_order as o
                LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
            o.create_time BETWEEN #{start} AND #{end}
          AND o.is_deleted = 0
          AND o.status != 100
        AND od.point_id = #{pointId}
    </select>







    <!-- OrderDto结果 -->
    <resultMap id="OrderDto" type="com.medusa.gruul.order.api.model.OrderDto">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectOneById" resultMap="OrderDto">
        SELECT
            id as id,
            update_time as updateTime,
            user_id as user_id,
            status as status
        FROM t_order
        WHERE
            is_deleted = 0 AND id = #{orderId}
    </select>


    <update id="updateOneById" parameterType="com.medusa.gruul.order.api.model.OrderDto">
        update   t_order set  status=#{status}, update_time = #{updateTime} where id = #{id}
    </update>
    <update id="closeExchangeOrder">
        update t_order set status=#{status}, close_time = now() where type = 103 and id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>



    <!-- PC管理端订单列表查询映射结果 -->
    <resultMap id="ManageDeliveryOrdersVoMap" type="com.medusa.gruul.order.model.ManageDeliveryOrderVo">
        <id column="id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_post_code" property="receiverPostCode"/>
        <result column="receiver_province" property="receiverProvince"/>
        <result column="receiver_city" property="receiverCity"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="pay_amount" property="payAmount"/>
        <collection column="id" property="itemVoList" javaType="ArrayList"
                    select="com.medusa.gruul.order.mapper.OrderItemMapper.selectSimpleOrderItemVoByOrderId">
        </collection>
    </resultMap>
    <select id="searchLogisticsOrderList" resultMap="ManageDeliveryOrdersVoMap">
        SELECT
        *
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.status = 101
        AND o.is_deleted = 0
        AND od.is_deleted = 0
        AND od.delivery_type = 102
        <if test="dto.goodsName!=null and dto.goodsName!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM
            t_order_item as oi
            WHERE
            oi.is_deleted = 0
            AND oi.product_name LIKE concat('%',#{dto.goodsName},'%') )
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            AND o.user_name LIKE concat('%',#{dto.userName},'%')
        </if>

        <if test="dto.orderId!=null  and dto.orderId!=''">
            AND o.id LIKE concat('%',#{dto.orderId},'%')
        </if>
        <if test="dto.address!=null  and dto.address!=''">
            AND od.receiver_detail_address LIKE concat('%',#{dto.address},'%')
        </if>
        ORDER BY o.create_time DESC
    </select>
    <select id="selectListByPointIdAndStatus" resultMap="BaseResultMap">
        SELECT
        o.*
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.status in
        <foreach collection="orderStatusList" item="orderStatus" open="(" separator="," close=")">
            #{orderStatus}
        </foreach>
        AND od.point_id = #{pointId}
    </select>


    <select id="getProductLastBuyers" resultType="com.medusa.gruul.order.api.model.BuyerVo">
        SELECT
            o.user_name AS name,
            o.user_avatar_url AS avatar_url
        FROM
            t_order AS o
                LEFT JOIN t_order_item AS oi ON o.id = oi.order_id
        WHERE
            oi.product_id = #{productId}
        ORDER BY
            o.create_time DESC
            LIMIT 3
    </select>
    <select id="searchLogisticsOrder" resultMap="ManageOrderVoMap">
        SELECT
        *
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.is_deleted = 0
        AND o.status = 101
        AND od.is_deleted = 0
        AND od.delivery_type = 102
        <if test="orderIds!=null and  orderIds.size() > 0">
            AND o.id in
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        </if>
        ORDER BY o.create_time DESC
    </select>

    <!--    <select id="transacTionOverview" resultMap="transacTionOverviewMap">-->

    <!--        SELECT    DATE_FORMAT(create_time,'%Y-%m-%d')   as date,COUNT(DISTINCT(id)) AS transactionVolume,SUM(discounts_amount) AS turnover-->
    <!--        FROM t_order-->
    <!--        WHERE 1=1-->
    <!--        <if test="startData!=null and startData!=''">-->
    <!--                and    create_time>=#{startData}-->
    <!--        </if>-->
    <!--        <if test="endData!=null and endData!=''">-->
    <!--            and  create_time &lt;=#{endData}-->
    <!--        </if>-->

    <!--        GROUP BY  DATE_FORMAT(create_time,'%Y-%m-%d')-->
    <!--        ORDER BY  DATE_FORMAT(create_time,'%Y-%m-%d')-->

    <!--    </select>-->

    <select id="transacTionOverview" resultMap="transacTionOverviewMap">
        SELECT  DATE(create_time) as date, IFNULL(SUM(discounts_amount),0)   AS turnover, COUNT(DISTINCT(id),0) AS transactionVolume
        FROM t_order
        WHERE is_deleted = 0
        <if test="orderStatusList!=null and  orderStatusList.size() > 0">
            AND status in
            <foreach collection="orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="startData!=null and startData!=''">
            and create_time>=#{startData}
        </if>
        <if test="endData!=null and endData!=''">
            and  create_time &lt;=#{endData}
        </if>
        group by DATE(create_time)

    </select>



    <select id="transacTion" resultMap="ManageOrderTradeDtoMap">

        SELECT  IFNULL(SUM(discounts_amount),0)   AS turnover, COUNT(DISTINCT(id),0) AS transactionVolume
        FROM t_order
        WHERE is_deleted = 0
        <if test="orderStatusList!=null and  orderStatusList.size() > 0">
            AND status in
            <foreach collection="orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="startData!=null and startData!=''">
            and    create_time>=#{startData}
        </if>
        <if test="endData!=null and endData!=''">
            and  create_time &lt;=#{endData}
        </if>
    </select>

    <select id="getAgainBuyCustom" resultType="java.util.Map">
        select count(*) as number,user_id
        from t_order
        where 1=1
        <if test="startData!=null and startData!=''">
            and    pay_time>=#{startData}
        </if>
        <if test="endData!=null and endData!=''">
            and  pay_time &lt;=#{endData}
        </if>
        group by user_id
        having count(*)>1
    </select>
    <select id="countLogisticsWaitSend" resultType="java.lang.Integer">
        SELECT
            count(o.id)
        FROM
            t_order as o
                LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
            o.is_deleted = 0
          AND o.status = 101
          AND od.is_deleted = 0
          AND od.delivery_type = 102
    </select>
    <select id="selectListByAssId" resultType="com.medusa.gruul.order.api.entity.Order">
        SELECT
        o.*
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.is_deleted = 0
        AND o.status IN (101,102,103)
        <if test="keyword!=null and keyword!=''">
            AND (o.user_name LIKE concat('%',#{keyword},'%') or
            od.receiver_name LIKE concat('%',#{keyword},'%') or
            od.receiver_phone LIKE concat('%',#{keyword},'%')
            )
        </if>
    </select>
    <select id="selectShippedOrderByProductIds" resultType="java.lang.Long">
        SELECT
        o.id
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON od.order_id = o.id
        LEFT JOIN t_order_item as oi ON oi.order_id = o.id
        WHERE
        o.is_deleted = 0
        AND oi.is_deleted = 0
        AND od.is_deleted = 0
        AND o.status = 101
        AND od.delivery_type != 102
        AND od.send_bill_id IS NOT NULL
        <if test="productIds!=null and  productIds.size() > 0">
            AND oi.product_id IN
            <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
    </select>
    <select id="searchIncentiveDetailPage" resultMap="ManageOrderVoMap">
        SELECT
        *
        FROM
        t_order as o
        LEFT JOIN t_order_delivery as od ON o.id = od.order_id
        WHERE
        o.is_deleted = 0
        AND od.is_deleted = 0
        AND o.complete_time BETWEEN #{beginTime} AND #{endTime}
        <if test="deliverType == 100">
            AND od.delivery_type in (100,101)
        </if>
        <if test="deliverType == 102">
            AND od.delivery_type = 102
        </if>
        ORDER BY o.create_time DESC
    </select>
    <select id="waitSendProductList" resultType="java.lang.Long">
        SELECT
            DISTINCT oi.product_id
        FROM
            t_order as o
                LEFT JOIN t_order_delivery as od ON o.id = od.order_id
                LEFT JOIN t_order_item as oi ON oi.order_id = o.id
        WHERE
            o.is_deleted = 0
          AND oi.is_deleted = 0
          AND od.is_deleted = 0
          AND o.STATUS = 101
          AND od.delivery_type != 102
            AND od.send_bill_id =  #{sendBillId}
    </select>

    <select id="getSolitaireLatelyBuyer" resultType="com.medusa.gruul.order.model.LatelyBuyerVo">
        SELECT
            MIN( user_name ) as user_name,
            MIN( user_avatar_url ) as user_avatar_url,
            MIN( pay_time ) as pay_time,
            MIN( product_name ) as product_name,
            COUNT( oi.order_id )  as product_total_quantity
        FROM
            t_order as o
                LEFT JOIN t_order_item as oi ON oi.order_id = o.id
        WHERE
            o.is_deleted = 0
          AND o.pay_time IS NOT NULL
          AND o.solitaire_activity_id = #{solitaireActivityId}
        GROUP BY
            oi.order_id ,
            o.pay_time
        ORDER BY
            o.pay_time DESC LIMIT 5
    </select>
    <!-- PC管理端订单列表查询映射结果 -->
    <resultMap id="SolitaireBuyerVoMap" type="com.medusa.gruul.order.model.SolitaireBuyerVo">
        <result column="id" property="orderId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="pay_time" property="payTime"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="pay_amount" property="payAmount"/>
    </resultMap>
    <select id="getSolitaireBuyerPage" resultMap="SolitaireBuyerVoMap">
        SELECT
            id,
            user_name,
            user_avatar_url,
            pay_time
        FROM
            t_order
        WHERE
            is_deleted = 0
          AND pay_time IS NOT NULL
          AND solitaire_activity_id = #{solitaireActivityId}
        ORDER BY
            pay_time DESC
    </select>

    <select id="getMySolitaireOrder" resultMap="SolitaireBuyerVoMap">
        SELECT
            id,
            user_name,
            user_avatar_url,
            pay_time,
            receiver_name,
            receiver_phone,
            receiver_detail_address,
            pay_amount
        FROM
            t_order as o  LEFT JOIN t_order_delivery as od on o.id=od.order_id
        WHERE
            o.is_deleted = 0
          AND pay_time IS NOT NULL
          AND solitaire_activity_id = #{solitaireActivityId}
          AND user_id = #{userId}
        ORDER BY
            pay_time DESC
    </select>


    <select id="getUserExchangeNumByUserId" resultType="java.math.BigDecimal">
        select
            sum(t1.product_quantity)
        FROM
            t_order_item t1
                LEFT JOIN t_order t2 ON t1.order_id = t2.id and t2.is_deleted = 0
        where
            t2.type = 105	and t1.is_deleted = 0 and t2.user_id = #{userId}
          and t1.product_id = #{productId} and t1.product_sku_id = #{skuId}
    </select>
    <select id="getApiIntegralOrderVoByUserId" resultMap="ApiIntegralOrderVoMap">
        SELECT
            t1.id,
            t1.create_time,
            t3.name AS product_name,
            t4.specs,
            t1.status,
            sum( t2.product_quantity ) AS all_product_quantity,
            t1.all_integral,
            t1.pay_amount,
            t3.pic
        FROM
            t_order t1
                LEFT JOIN t_order_item t2 ON t1.id = t2.order_id
                AND t2.is_deleted = 0
                LEFT JOIN t_product t3 ON t3.id = t2.product_id
                AND t3.is_deleted = 0
                LEFT JOIN t_sku_stock t4 ON t4.id = t2.product_sku_id
                AND t4.is_deleted = 0
        WHERE
            t1.type = 105 and t1.is_deleted = 0 and t1.user_id = #{userId}
        GROUP BY
            t1.id,
            t1.create_time,
            t3.name,
            t4.specs,
            t1.status,
            t1.all_integral,
            t1.pay_amount,
            t3.pic
        order by
            t1.create_time desc
    </select>
    <select id="getOrderDataVo" resultMap="OrderDataVoMap">
        select
            t1.id,
            t1.tenant_id,
            t1.shop_id
        from t_order t1
        where t1.is_deleted = 0
          and (t1.id =#{orderId} or pay_id = #{orderId})
    </select>

    <select id="searchApiHistoryOrderVoPage" resultMap="ApiHistoryOrderVoMap">
        select * from (SELECT
        o.id, o.id as order_Id, o.type,o.create_time,o.pay_amount,o.pay_type,o.expire_time ,o.status,o.all_integral,o.wx_deliver_status,o.transaction_id,b.nike_name, b.phone, 'mall' as source
        FROM
        t_order as o left join t_mini_account_extends a on o.user_id = a.shop_user_id left join t_mini_account b on b.user_id = a.user_id
        WHERE
        o.is_deleted =0
        <if test="userId!=null and userId!=''">
            AND o.user_id = #{userId}
        </if>
        <if test="phone!=null and phone!=''">
            AND b.phone LIKE concat('%',#{phone},'%')
        </if>
        <if test="statusList!=null and statusList.size>0">
            AND o.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="statusList.contains(105) or statusList.contains(104) or statusList.contains(104) ">
                AND o.type != 103
            </if>
        </if>
        AND o.type != 104

        union all
        SELECT
        o.id,o.bill_no as order_id, o.type,o.complete_time as create_time,o.pay_amount,o.pay_type,o.expire_time ,o.status,o.all_integral,o.wx_deliver_status,o.transaction_id, o.user_name as nike_name,
        o.user_phone as phone, 'history' as source
        FROM
        t_sales_lyd_history as o
        WHERE
        o.is_deleted =0
        <if test="userId!=null and userId!=''">
            AND o.user_id = #{userId}
        </if>
        <if test="phone!=null and phone!=''">
            AND o.user_phone LIKE concat('%',#{phone},'%')
        </if>
        ) t
        ORDER BY t.create_time DESC
    </select>

    <!-- 门店、员工订单汇总 -->
    <select id="manageOrderStatic" resultMap="ManageOrderStaticMap">
        select id, name, is_catalog, IFNULL(SUM(package_Amount),0) AS package_Amount, IFNULL(SUM(package_Qty),0) AS package_Qty, IFNULL(SUM(product_Amount),0) AS product_Amount,
        IFNULL(SUM(product_Qty),0) AS product_Qty, IFNULL(SUM(total_Amount),0) AS total_Amount, IFNULL(SUM(total_Qty),0) AS total_Qty, IFNULL(SUM(member_Qty),0)  as member_Qty,
        IFNULL(SUM(package_Un_Verify_Qty),0) AS package_Un_Verify_Qty
        from (
        SELECT case when #{params.type} = 0 then b.class_code else a.account_id end as id, min(case when #{params.type} = 0 then b.store_full_name else a.account_name end) as name,
        min(case when #{params.type} = 0 then b.is_catalog else 0 end) as is_catalog, IFNULL(SUM(case when a.mall_order_type = 2 then a.discounts_amount else 0 end),0) AS package_Amount,
        IFNULL(SUM(case when a.mall_order_type = 2 then 1 else 0 end),0) AS package_Qty, IFNULL(SUM(case when a.mall_order_type = 1 or a.mall_order_type = 3 then a.discounts_amount else 0 end),0) AS product_Amount,
        IFNULL(SUM(case when a.mall_order_type = 1 or a.mall_order_type = 3 then 1 else 0 end),0) AS product_Qty, IFNULL(SUM(a.discounts_amount),0) AS total_Amount, IFNULL(count(1),0) AS total_Qty, 0 as member_Qty,
        IFNULL(SUM(package_Un_Verify_Qty),0) AS package_Un_Verify_Qty
        FROM t_order a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join (select order_id, IFNULL(SUM(all_times - already_times),0) AS package_Un_Verify_Qty from t_mini_account_package_goods c where status != 200 group by order_id) d on a.id = d.order_id
        left join t_mini_account_package_order e on e.order_id = a.id left join t_product f on f.id = e.package_id
        WHERE a.is_deleted = 0
        <if test="params.firstClassFlag!=null and params.firstClassFlag!='' and params.firstClassFlag == 1 ">
            and  b.class_code LIKE CONCAT('','','_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 0">
            and  b.class_code LIKE CONCAT('',#{params.parentCode},'_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 1">
            and  b.class_code  = #{params.parentCode}
        </if>
        <if test="params.orderStatusList!=null and  params.orderStatusList.size() > 0">
            AND a.status in
            <foreach collection="params.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and a.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            AND f.name LIKE concat('%',#{params.packageName},'%')
        </if>
        group by case when #{params.type} = 0 then b.class_code else a.account_id end

        union all

        SELECT  case when #{params.type} = 0 then b.class_code else a.platform_account_id end as id, min(case when #{params.type} = 0 then b.store_full_name else c.nike_name end) as name,
        min(case when #{params.type} = 0 then b.is_catalog else 0 end) as is_catalog, 0 AS package_Amount, 0 AS package_Qty, 0 AS product_Amount, 0 AS product_Qty, 0 AS total_Amount,
        0 AS total_Qty, IFNULL(count(1),0) AS member_Qty, 0 AS package_Un_Verify_Qty
        FROM t_mini_account a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join t_platform_account_info c on a.platform_account_id = c.id
        WHERE a.is_deleted = 0 and a.whether_authorization = 1
        <if test="params.firstClassFlag!=null and params.firstClassFlag!='' and params.firstClassFlag == 1 ">
            and  b.class_code LIKE CONCAT('','','_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 0">
            and  b.class_code LIKE CONCAT('',#{params.parentCode},'_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 1">
            and  b.class_code  = #{params.parentCode}
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and a.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        group by case when #{params.type} = 0 then b.class_code else a.platform_account_id end
        ) t group by id, name, is_catalog
        <if test="params.orderBy!=null and params.orderBy == 0">
            order by package_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 1">
            order by package_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 2">
            order by product_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 3">
            order by product_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 4">
            order by total_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 5">
            order by total_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 6">
            order by member_Qty
        </if>
        <if test="params.asc!=null and params.asc == 0">
            asc
        </if>
        <if test="params.asc!=null and params.asc == 1">
            desc
        </if>
    </select>

    <!-- 门店、员工订单汇总合计数据 -->
    <select id="manageOrderStaticTotal" resultMap="ManageOrderStaticMap">
        select '-1' as id, '总计' as name, '0' as is_catalog, IFNULL(SUM(package_Amount),0) AS package_Amount, IFNULL(SUM(package_Qty),0) AS package_Qty, IFNULL(SUM(product_Amount),0) AS product_Amount,
        IFNULL(SUM(product_Qty),0) AS product_Qty, IFNULL(SUM(total_Amount),0) AS total_Amount, IFNULL(SUM(total_Qty),0) AS total_Qty, IFNULL(SUM(member_Qty),0)  as member_Qty,
        IFNULL(SUM(package_Un_Verify_Qty),0) AS package_Un_Verify_Qty
        from (
        SELECT case when #{params.type} = 0 then b.class_code else a.account_id end as id, min(case when #{params.type} = 0 then b.store_full_name else a.account_name end) as name,
        min(case when #{params.type} = 0 then b.is_catalog else 0 end) as is_catalog, IFNULL(SUM(case when a.mall_order_type = 2 then a.discounts_amount else 0 end),0) AS package_Amount,
        IFNULL(SUM(case when a.mall_order_type = 2 then 1 else 0 end),0) AS package_Qty, IFNULL(SUM(case when a.mall_order_type = 1 or a.mall_order_type = 3 then a.discounts_amount else 0 end),0) AS product_Amount,
        IFNULL(SUM(case when a.mall_order_type = 1 or a.mall_order_type = 3 then 1 else 0 end),0) AS product_Qty, IFNULL(SUM(a.discounts_amount),0) AS total_Amount, IFNULL(count(1),0) AS total_Qty, 0 as member_Qty,
        IFNULL(SUM(package_Un_Verify_Qty),0) AS package_Un_Verify_Qty
        FROM t_order a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join (select order_id, IFNULL(SUM(all_times - already_times),0) AS package_Un_Verify_Qty from t_mini_account_package_goods c where status != 200 group by order_id) d on a.id = d.order_id
        left join t_mini_account_package_order e on e.order_id = a.id left join t_product f on f.id = e.package_id
        WHERE a.is_deleted = 0
        <if test="params.firstClassFlag!=null and params.firstClassFlag!='' and params.firstClassFlag == 1 ">
            and  b.class_code LIKE CONCAT('','','_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 0">
            and  b.class_code LIKE CONCAT('',#{params.parentCode},'_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 1">
            and  b.class_code  = #{params.parentCode}
        </if>
        <if test="params.orderStatusList!=null and  params.orderStatusList.size() > 0">
            AND a.status in
            <foreach collection="params.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and a.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            AND f.name LIKE concat('%',#{params.packageName},'%')
        </if>
        group by case when #{params.type} = 0 then b.class_code else a.account_id end

        union all

        SELECT  case when #{params.type} = 0 then b.class_code else a.platform_account_id end as id, min(case when #{params.type} = 0 then b.store_full_name else c.nike_name end) as name,
        min(case when #{params.type} = 0 then b.is_catalog else 0 end) as is_catalog, 0 AS package_Amount, 0 AS package_Qty, 0 AS product_Amount, 0 AS product_Qty, 0 AS total_Amount,
        0 AS total_Qty, IFNULL(count(1),0) AS member_Qty, 0 AS package_Un_Verify_Qty
        FROM t_mini_account a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join t_platform_account_info c on a.platform_account_id = c.id
        WHERE a.is_deleted = 0 and a.whether_authorization = 1
        <if test="params.firstClassFlag!=null and params.firstClassFlag!='' and params.firstClassFlag == 1 ">
            and  b.class_code LIKE CONCAT('','','_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 0">
            and  b.class_code LIKE CONCAT('',#{params.parentCode},'_____')
        </if>
        <if test="params.parentCode!='-1' and params.type == 1">
            and  b.class_code  = #{params.parentCode}
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and a.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        group by case when #{params.type} = 0 then b.class_code else a.platform_account_id end
        ) t

    </select>

    <!-- 权益包订单汇总 -->
    <select id="managePackageStatic" resultMap="ManageOrderStaticMap">
        SELECT e.package_id as id, min(f.name) as name, 0 as is_catalog, IFNULL(SUM(a.discounts_amount),0) AS package_Amount,
        IFNULL(count(1),0) AS package_Qty, 0 AS product_Amount, 0 AS product_Qty, IFNULL(SUM(a.discounts_amount),0) AS total_Amount, IFNULL(count(1),0) AS total_Qty, 0 as member_Qty,
        IFNULL(min(d.package_Un_Verify_Qty),0) AS package_Un_Verify_Qty, IFNULL(min(d.package_Verify_Qty),0) AS package_Verify_Qty
        FROM t_mini_account_package_order e left join t_order a on e.order_id = a.id left join t_platform_lyd_store_front b ON a.store_front_code  = b.class_code
        left join (select package_id, IFNULL(SUM(already_times),0) AS package_Verify_Qty, IFNULL(SUM(all_times - already_times),0) AS package_Un_Verify_Qty from t_mini_account_package_goods c where status != 200 group by package_id) d on e.package_id = d.package_id
        left join t_product f on f.id = e.package_id
        WHERE a.is_deleted = 0
        <if test="params.orderStatusList!=null and  params.orderStatusList.size() > 0">
            AND a.status in
            <foreach collection="params.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and e.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  e.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            AND f.name LIKE concat('%',#{params.packageName},'%')
        </if>
        group by e.package_id
        <if test="params.orderBy!=null and params.orderBy == 0">
            order by package_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 1">
            order by package_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 2">
            order by product_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 3">
            order by product_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 4">
            order by total_Qty
        </if>
        <if test="params.orderBy!=null and params.orderBy == 5">
            order by total_Amount
        </if>
        <if test="params.orderBy!=null and params.orderBy == 6">
            order by member_Qty
        </if>
        <if test="params.asc!=null and params.asc == 0">
            asc
        </if>
        <if test="params.asc!=null and params.asc == 1">
            desc
        </if>

    </select>

    <!-- 权益包订单汇总合计值 -->
    <select id="managePackageStaticTotal" resultMap="ManageOrderStaticMap">
        SELECT '-1' as id, '总计' as name, 0 as is_catalog, IFNULL(SUM(a.discounts_amount),0) AS package_Amount,
        IFNULL(count(1),0) AS package_Qty, 0 AS product_Amount, 0 AS product_Qty, IFNULL(SUM(a.discounts_amount),0) AS total_Amount, IFNULL(count(1),0) AS total_Qty, 0 as member_Qty,
        IFNULL(sum(package_Un_Verify_Qty),0) AS package_Un_Verify_Qty, IFNULL(sum(d.package_Verify_Qty),0) AS package_Verify_Qty
        FROM t_mini_account_package_order e left join t_order a on e.order_id = a.id left join t_platform_lyd_store_front b ON a.store_front_code  = b.class_code
        left join (select main_id, IFNULL(SUM(already_times),0) AS package_Verify_Qty, IFNULL(SUM(all_times - already_times),0) AS package_Un_Verify_Qty from t_mini_account_package_goods c  where status != 200 group by main_id) d on e.id = d.main_id
        left join t_product f on f.id = e.package_id
        WHERE a.is_deleted = 0
        <if test="params.orderStatusList!=null and  params.orderStatusList.size() > 0">
            AND a.status in
            <foreach collection="params.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and e.create_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  e.create_time &lt;=#{params.endTime}
        </if>
        <if test="params.storeName!=null and params.storeName!=''">
            AND b.store_full_name LIKE concat('%',#{params.storeName},'%')
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            AND f.name LIKE concat('%',#{params.packageName},'%')
        </if>
    </select>
    <select id="searchOrderItem" resultMap = "OrderItemVoMap">
        SELECT
        t1.id AS order_id,
        t2.product_name,
        t2.product_price,
        t2.real_amount,
        t4.nike_name,
        t4.phone,
        t5.delivery_type,
        t6.warehouse_full_name AS warehouse_name,
        t7.store_front_name,
        t7.nike_name as account_name,
        t2.product_quantity
        FROM
        t_order t1
        LEFT JOIN t_order_item t2 ON t1.id = t2.order_id
        LEFT JOIN t_mini_account_extends t3 ON t3.shop_user_id = t1.user_id
        LEFT JOIN t_mini_account t4 ON t3.user_id = t4.user_id
        LEFT JOIN t_order_delivery t5 ON t5.order_id = t1.id
        LEFT JOIN t_warehouse t6 ON t6.id = t1.warehouse_id
        LEFT JOIN t_platform_account_info t7 ON t7.id = t1.account_id
        LEFT JOIN t_afs_order t8 ON t8.receipt_bill_id = t1.id
        WHERE
        t1.is_deleted = 0
        AND IFNULL( t1.pay_time, '' )!= ''
        <if test="params.startDate!=null and params.startDate!='' and params.endDate!=null and params.endDate!=''">
            AND t1.create_time BETWEEN concat(#{params.startDate},' 00:00:00') AND concat(#{params.endDate},' 23:59:59')
        </if>
        <if test="params.productName !=null  and params.productName!=''">
            AND t2.product_name LIKE concat('%',#{params.productName},'%')
        </if>
        <if test="params.phone !=null  and params.phone!=''">
            AND t4.phone LIKE concat('%',#{params.phone},'%')
        </if>
        <if test="params.note !=null  and params.note!=''">
            AND t1.note LIKE concat('%',#{params.note},'%')
        </if>
        <if test="params.receiverPhone !=null  and params.receiverPhone!=''">
            AND t5.receiver_phone LIKE concat('%',#{params.receiverPhone},'%')
        </if>
        <if test="params.orderId!=null  and params.orderId!=''">
            AND t1.id LIKE concat('%',#{params.orderId},'%')
        </if>
        <if test="params.orderStatusList!=null and params.orderStatusList.size>0">
            AND t1.status in
            <foreach collection="params.orderStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>

        </if>

        <if test="params.isRefund !=null and params.isRefund == 1">
            AND  (t1.status  IN ('200', '201') OR t8.type in (3,5))
        </if>
        <if test="params.isRefund !=null and params.isRefund == 0">
            AND  (t1.status NOT IN ('200', '201') AND t8.type is null)
        </if>
        <if test="params.storeFrontName !=null  and params.storeFrontName!=''">
            AND t7.store_front_name LIKE concat('%',#{params.storeFrontName},'%')
        </if>
        <if test="params.accountName !=null  and params.accountName!=''">
            AND t7.nike_name LIKE concat('%',#{params.accountName},'%')
        </if>
        order by t1.create_time desc
    </select>
    <select id="searchApiMemberOrder" resultMap="ApiMemberOrderMap">
        SELECT
            t1.id,
            t2.member_level,
            t1.user_id,
            t1.pay_amount,
            DATE_FORMAT( t1.pay_time, '%Y-%m-%d %H:%i:%s' ) AS pay_time
        FROM
            t_order t1
        LEFT JOIN
            t_member_level t2 ON t1.member_id = t2.id
        WHERE
            t1.is_deleted = 0
          AND t1.id IN ( SELECT t3.order_id FROM t_order_item t3 WHERE t3.is_deleted = 0 AND t3.price_type = 1 )
          AND t1.user_id = #{params.userId}
          AND t1.pay_time IS NOT NULL
          AND t1.status !=200
            <if test="params.keyword !=null  and params.keyword!=''">
                and ( t1.id LIKE concat('%',#{params.keyword},'%') or t2.member_level LIKE concat('%',#{params.keyword},'%') or t1.pay_amount = #{params.keyword})
            </if>
        ORDER BY
            t1.pay_time DESC
    </select>

    <select id="searchManageOrderExcelPage" resultMap="ManageOrderExcelMap">
        SELECT
        o.id AS order_id,
        DATE_FORMAT( o.create_time, '%Y-%m-%d %H:%i:%s' ) AS create_time,
        o.total_amount,
        o.youhui_price,
        o.deduction_price,
        o.pay_amount,
        CASE
        WHEN o.status = 100 THEN
        '待买家付款'
        WHEN o.status = 101 THEN
        '待卖家发货'
        WHEN o.status = 102 THEN
        '配送中'
        WHEN o.status = 103 THEN
        '等待买家取货'
        WHEN o.status = 104 THEN
        '等待评价'
        WHEN o.status = 105 THEN
        '订单已完成'
        WHEN o.status = 115 THEN
        '审核通过'
        WHEN o.status = 200 THEN
        '订单退货或全额退款'
        WHEN o.status = 201 THEN
        '部分退款'
        WHEN o.status = 300 THEN
        '支付超时关闭'
        WHEN o.status = 301 THEN
        '买家取消关闭'
        WHEN o.status = 302 THEN
        '卖家取消关闭'
        WHEN o.status = 303 THEN
        '换货成功关闭'
        WHEN o.status = 304 THEN
        '换货关闭'
        END AS status,
        CASE
        WHEN od.delivery_type = 103 THEN
        '手动发货'
        WHEN od.delivery_type = 102 THEN
        '物流配送'
        WHEN od.delivery_type = 101 THEN
        '送货上门'
        WHEN od.delivery_type = 0 THEN
        '无需物流配送'
        WHEN od.delivery_type = 100 THEN
        '自提'
        END AS delivery_type,
        ac.nike_name,
        ac.phone,
        sp.name as shop_name,
        oi.product_name,
        ss.specs,
        oi.product_quantity,
        oi.product_price,
        oi.real_amount,
        w.warehouse_full_name,
        o.note,
        p.sale_describe
        FROM
        t_order AS o
        LEFT JOIN t_order_delivery AS od ON o.id = od.order_id
        LEFT JOIN t_platform_lyd_store_front AS plsf ON plsf.id = o.store_front_id
        LEFT JOIN t_mini_account_extends AS e ON o.user_id = e.shop_user_id
        LEFT JOIN t_mini_account AS ac ON ac.user_id = e.user_id
        LEFT JOIN t_order_item AS oi ON o.id = oi.order_id
        LEFT JOIN t_shops_partner as sp on sp.shop_id = o.shop_id
        LEFT JOIN t_sku_stock as ss on ss.id = oi.product_sku_id
        left join t_warehouse as w on w.id = o.warehouse_id
        left join t_product as p on p.id = oi.product_id
        WHERE
        o.is_deleted = 0
        AND (
        od.is_deleted = 0
        OR od.is_deleted IS NULL)
        <if test="dto.goodsName!=null and dto.goodsName!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM
            t_order_item as oi
            WHERE
            oi.is_deleted = 0
            AND oi.product_name LIKE concat('%',#{dto.goodsName},'%') )
        </if>
        <if test="dto.area!=null and dto.area!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM t_order_item as oi
            WHERE
            oi.is_deleted = 0
            and oi.sale_mode=#{dto.area})
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            AND o.user_name LIKE concat('%',#{dto.userName},'%')
        </if>
        <if test="dto.receiverName!=null and dto.receiverName!=''">
            AND od.receiver_name LIKE concat('%',#{dto.receiverName},'%')
        </if>
        <if test="dto.orderId!=null  and dto.orderId!=''">
            AND o.id LIKE concat('%',#{dto.orderId},'%')
        </if>
        <if test="dto.deliverySn !=null  and dto.deliverySn!=''">
            AND od.delivery_sn LIKE concat('%',#{dto.deliverySn},'%')
        </if>
        <if test="dto.lineId !=null and dto.lineId!=''">
            AND od.line_id = #{dto.lineId}
        </if>
        <if test="dto.deliverType == 0">
            AND od.delivery_type != 102
        </if>
        <if test="dto.deliverType == 100">
            AND od.delivery_type = 100
        </if>
        <if test="dto.deliverType == 101">
            AND od.delivery_type = 101
        </if>
        <if test="dto.deliverType == 102">
            AND od.delivery_type = 102
        </if>
        <if test='dto.remarkType == 1'>
            AND o.note != ''
        </if>
        <if test='dto.remarkType == 2'>
            AND o.note = ''
        </if>
        <if test="dto.note !=null and dto.note !=''">
            AND o.note LIKE concat('%',#{dto.note},'%')
        </if>
        <if test="dto.phone !=null and dto.phone !=''">
            AND ac.phone LIKE concat('%',#{dto.phone},'%')
        </if>
        <if test="dto.startTime!=null and dto.startTime!='' and dto.endTime!=null and dto.endTime!=''">
            AND o.pay_time BETWEEN concat(#{dto.startTime},' 00:00:00') AND concat(#{dto.endTime},' 23:59:59')
        </if>
        <if test="startDate!=null and endDate!=null">
            AND o.create_time BETWEEN concat(#{startDate},' 00:00:00') AND concat(#{endDate},' 23:59:59')
        </if>
        <if test='dto.sendBillId == -1'>
            AND od.send_bill_id IS NULL
        </if>
        <if test="dto.sendBillId!=null and dto.sendBillId!=0 and dto.sendBillId!=-1">
            AND od.send_bill_id = #{dto.sendBillId}
        </if>
        <if test="statusList!=null and statusList.size>0">
            AND o.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.shopIds!=null and dto.shopIds.size>0">
            AND o.shop_id in
            <foreach collection="dto.shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="dto.shopIds2!=null and dto.shopIds2.size>0">
            AND o.shop_id in
            <foreach collection="dto.shopIds2" item="shopId2" open="(" separator="," close=")">
                #{shopId2}
            </foreach>
        </if>
        <if test="dto.storeFrontName!=null and dto.storeFrontName!=''">
            AND plsf.store_full_name LIKE concat('%',#{dto.storeFrontName},'%')
        </if>
        ORDER BY
        o.create_time DESC
    </select>

    <select id="searchByMiniAccount" resultMap="BaseResultMap">
        SELECT
        o.*
        FROM
        t_order as o
        left join t_mini_account_extends as e on o.user_id = e.shop_user_id
        left join t_mini_account as ac on ac.user_id = e.user_id
        WHERE
        o.is_deleted = 0
        <if test="dto.goodsName!=null and dto.goodsName!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM
            t_order_item as oi
            WHERE
            oi.is_deleted = 0
            AND oi.product_name LIKE concat('%',#{dto.goodsName},'%') )
        </if>
        <if test="dto.area!=null and dto.area!=''">
            AND o.id IN
            ( SELECT
            DISTINCT(oi.order_id)
            FROM t_order_item as oi
            WHERE
            oi.is_deleted = 0
            and oi.sale_mode=#{dto.area})
        </if>
        <if test="dto.userName!=null and dto.userName!=''">
            AND o.user_name LIKE concat('%',#{dto.userName},'%')
        </if>
        <if test="dto.orderId!=null  and dto.orderId!=''">
            AND o.id LIKE concat('%',#{dto.orderId},'%')
        </if>
        <if test="dto.note !=null and dto.note !=''">
            AND o.note LIKE concat('%',#{dto.note},'%')
        </if>
        <if test="dto.phone !=null and dto.phone !=''">
            AND ac.phone LIKE concat('%',#{dto.phone},'%')
        </if>
        <if test="dto.startTime!=null and dto.startTime!='' and dto.endTime!=null and dto.endTime!=''">
            AND o.pay_time BETWEEN concat(#{dto.startTime},' 00:00:00') AND concat(#{dto.endTime},' 23:59:59')
        </if>
        <if test="startDate!=null and endDate!=null">
            AND o.create_time BETWEEN concat(#{startDate},' 00:00:00') AND concat(#{endDate},' 23:59:59')
        </if>
        <if test="statusList!=null and statusList.size>0">
            AND o.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.miniAccountShopUserId!=null and dto.miniAccountShopUserId!=''">
            AND not exists
            (select 1 from t_mini_account_commission k where k.is_deleted = 0 and k.user_id = #{dto.miniAccountShopUserId} and k.order_id = o.id)
        </if>
        ORDER BY
        o.id DESC
    </select>

</mapper>
