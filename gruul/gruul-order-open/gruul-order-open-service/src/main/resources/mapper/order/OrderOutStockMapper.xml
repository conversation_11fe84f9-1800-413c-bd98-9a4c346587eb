<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.order.mapper.OrderOutStockMapper">
    <resultMap id="OutStockOutOrderMap" type="com.medusa.gruul.order.model.OutStockOutOrderVo">
        <id column="id" property="id"/>
        <result column="product_code" property="productCode"/>
        <result column="product_name" property="productName"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="department_code" property="departmentCode"/>
        <result column="department_name" property="departmentName"/>
        <result column="stock_code" property="stockCode"/>
        <result column="stock_name" property="stockName"/>
        <result column="out_time" property="outTime"/>
        <result column="number" property="number"/>
        <result column="price" property="price"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="all_amount" property="allAmount"/>
    </resultMap>
    <select id="searchOutStockOutOrder" resultMap="OutStockOutOrderMap">
        SELECT
            t1.id,
            t1.product_code AS product_code,
            t2.employee_id AS employee_id,
            t2.department_code AS department_code,
            t1.stock_code AS stock_code,
            t1.out_time AS out_time,
            t1.number AS number,
            t1.price AS price,
            t1.user_id AS user_id,
            t2.department_name AS department_name,
            t2.employee_name AS employee_name,
            t1.stock_name AS stock_name,
            t1.all_amount
        FROM
            t_order_out_stock t1
                LEFT JOIN t_platform_account_info t2 ON t2.id = t1.account_id
        WHERE
            t1.is_deleted = 0
          AND (ISNULL(t1.send_status) || t1.send_status!='1')
          AND ifnull( t1.account_id, '' )!= ''
    </select>

    <update id="updateSendStatus">
        update t_order_out_stock set send_status=#{sendStatus} where id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>
</mapper>
