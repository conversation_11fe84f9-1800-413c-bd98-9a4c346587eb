<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.order.mapper.OrderShareSetting">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.order.api.entity.OrderShareSetting">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="title" property="title"/>
        <result column="background" property="background"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        is_deleted,
        id, title, background
    </sql>

</mapper>
