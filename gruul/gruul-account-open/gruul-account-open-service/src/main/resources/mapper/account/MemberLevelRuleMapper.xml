<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRuleMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="MemberLevelRuleMap" type="com.medusa.gruul.account.model.vo.MemberLevelRuleVo">
        <id column="id" property="id"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level" property="memberLevel"/>
        <result column="amount_start" property="amountStart"/>
        <result column="amount_end" property="amountEnd"/>
        <result column="integral_start" property="integralStart"/>
        <result column="integral_end" property="integralEnd"/>
        <result column="disable" property="disable"/>
        <result column="amount_flag" property="amountFlag"/>
        <result column="integral_flag" property="integralFlag"/>
        <result column="sort" property="sort"/>
        <result column="member_amount_start" property="memberAmountStart"/>
        <result column="member_amount_end" property="memberAmountEnd"/>
        <result column="member_amount_flag" property="memberAmountFlag"/>
        <result column="again_flag" property="againFlag"/>
    </resultMap>

    <select id="getMemberLevelRule" resultMap="MemberLevelRuleMap">
        SELECT
            t2.id,
            t1.id AS member_level_id,
            t1.member_level,
            t2.amount_start,
            t2.amount_end,
            t2.integral_start,
            t2.integral_end,
            t1.DISABLE,
            t2.amount_flag,
            t2.integral_flag,
            t2.sort,
            t2.member_amount_start,
            t2.member_amount_end,
            t2.member_amount_flag,
            t2.again_flag
        FROM
            t_member_level t1
                LEFT JOIN t_member_level_rule t2 ON t1.id = t2.member_level_id and t2.is_deleted = 0
        WHERE
            t1.is_deleted = 0
        ORDER BY
            t2.sort DESC,
            t1.LEVEL ASC
    </select>
</mapper>
