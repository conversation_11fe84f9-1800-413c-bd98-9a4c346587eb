<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountCommissionCashMapper">

    <resultMap id="MiniAccountCommissionCashVoMap" type="com.medusa.gruul.account.model.vo.MiniAccountCommissionCashVo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="nike_name" property="nikeName"/>
        <result column="amount" property="amount"/>
        <result column="phone" property="phone"/>
        <result column="pay_time" property="payTime"/>
        <result column="status" property="status"/>
        <result column="pay_fail_reason" property="reason"/>
        <result column="approved_comments" property="comments"/>
        <result column="pay_status" property="payStatus"/>
        <result column="state" property="state"/>
        <result column="transfer_bill_no" property="transferBillNo"/>
        <result column="package_info" property="packageInfo"/>
    </resultMap>
    <resultMap id="MiniAccountCommissionCashExcelVoMap" type="com.medusa.gruul.account.model.vo.MiniAccountCommissionCashExcelVo">
        <result column="create_time" property="createTime"/>
        <result column="nike_name" property="nikeName"/>
        <result column="amount" property="amount"/>
        <result column="phone" property="phone"/>
        <result column="pay_time" property="payTime"/>
        <result column="status" property="status"/>
        <result column="pay_fail_reason" property="reason"/>
        <result column="approved_comments" property="comments"/>
    </resultMap>

    <select id="getMiniAccountCommissionCashVo" resultMap="MiniAccountCommissionCashVoMap">
        SELECT
            t1.id,
            t3.nike_name,
            t3.phone,
            t1.amount,
            t1.create_time,
            t1.pay_time,
            t1.status,
            t1.pay_fail_reason,
            t1.approved_comments,
            t1.pay_status,
            t1.state,
            t1.transfer_bill_no,
            t1.package_info
        FROM
            t_mini_account_commission_cash t1
                LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
                LEFT JOIN t_mini_account t3 ON t3.user_id = t2.user_id
        where t1.is_deleted = 0
        <if test="paramMap.shopUserId!=null">
           and t2.shop_user_id = #{paramMap.shopUserId}
        </if>
        <if test="paramMap.status!=null">
            and t1.status = #{paramMap.status}
        </if>
        <if test="paramMap.id!=null">
            and t1.id like concat('%',#{paramMap.id},'%')
        </if>
        <if test="paramMap.cashMessage!=null">
            and ( t3.nike_name like concat('%',#{paramMap.cashMessage},'%') or t3.phone like concat('%',#{paramMap.cashMessage},'%'))
        </if>
        <if test="paramMap.amount!=null">
            and t1.amount = #{paramMap.amount}
        </if>

        <if test="paramMap.startTime!=null">
            and t1.create_time &gt;= #{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null">
            and t1.create_time &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.typeTime!=null">
            and t1.create_time &gt;= #{paramMap.typeTime}
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort == 1">
            order by t1.create_time asc
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort == 2">
            order by t1.create_time desc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort == 1">
            order by t1.amount asc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort == 2">
            order by t1.amount desc
        </if>
        <if test="paramMap.payTimeSort!=null and paramMap.payTimeSort == 1">
            order by t1.pay_time asc
        </if>
        <if test="paramMap.payTimeSort!=null and paramMap.payTimeSort == 2">
            order by t1.pay_time desc
        </if>
    </select>

    <select id="getMiniAccountCommissionCashExcelVo" resultMap="MiniAccountCommissionCashExcelVoMap">
        SELECT
        t3.nike_name,
        t3.phone,
        t1.amount,
        t1.create_time,
        t1.pay_time,
        case t1.status
            when -1 then '已驳回'
            when -2 then '提现失败'
            when 0 then '审核中'
            when 1 then '审核通过'
            when 2 then '提现成功'
            else ''
        end as status,
        t1.pay_fail_reason,
        t1.approved_comments
        FROM
        t_mini_account_commission_cash t1
        LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
        LEFT JOIN t_mini_account t3 ON t3.user_id = t2.user_id
        where t1.is_deleted = 0
        <if test="paramMap.shopUserId!=null">
            and t2.shop_user_id = #{paramMap.shopUserId}
        </if>
        <if test="paramMap.status!=null">
            and t1.status = #{paramMap.status}
        </if>
        <if test="paramMap.id!=null">
            and t1.id like concat('%',#{paramMap.id},'%')
        </if>
        <if test="paramMap.cashMessage!=null">
            and ( t3.nike_name like concat('%',#{paramMap.cashMessage},'%') or t3.phone like concat('%',#{paramMap.cashMessage},'%'))
        </if>
        <if test="paramMap.amount!=null">
            and t1.amount = #{paramMap.amount}
        </if>

        <if test="paramMap.startTime!=null">
            and t1.create_time &gt;= #{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null">
            and t1.create_time &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.typeTime!=null">
            and t1.create_time &gt;= #{paramMap.typeTime}
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort == 1">
            order by t1.create_time asc
        </if>
        <if test="paramMap.createTimeSort!=null and paramMap.createTimeSort == 2">
            order by t1.create_time desc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort == 1">
            order by t1.amount asc
        </if>
        <if test="paramMap.amountSort!=null and paramMap.amountSort == 2">
            order by t1.amount desc
        </if>
        <if test="paramMap.payTimeSort!=null and paramMap.payTimeSort == 1">
            order by t1.pay_time asc
        </if>
        <if test="paramMap.payTimeSort!=null and paramMap.payTimeSort == 2">
            order by t1.pay_time desc
        </if>
    </select>

</mapper>
