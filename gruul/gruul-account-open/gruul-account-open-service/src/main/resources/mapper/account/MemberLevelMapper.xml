<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="MiniAccountMemberMap" type="com.medusa.gruul.account.api.entity.MemberLevel">
        <id column="id" property="id"/>
        <result column="member_level" property="memberLevel"/>
        <result column="remark" property="remark"/>
        <result column="point_exchange" property="pointExchange"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="member_card_name" property="memberCardName"/>
        <result column="legal_right" property="legalRight"/>
        <result column="disable" property="disable"/>
        <result column="level" property="level"/>
        <result column="default_level" property="defaultLevel"/>
        <result column="stock_flag" property="stockFlag"/>
        <result column="rule_type" property="ruleType"/>
        <result column="parent_receive" property="parentReceive"/>
        <result column="above_parent_receive" property="aboveParentReceive"/>
        <result column="min_pay_amount" property="minPayAmount"/>

    </resultMap>

    <resultMap id="MemberLevelVoMap" type="com.medusa.gruul.account.model.vo.MemberLevelVo">
        <id column="id" property="id"/>
        <result column="member_level" property="memberLevel"/>
        <result column="remark" property="remark"/>
        <result column="point_exchange" property="pointExchange"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="member_card_name" property="memberCardName"/>
        <result column="legal_right" property="legalRight"/>
        <result column="disable" property="disable"/>
        <result column="isSelected" property="isSelected"/>
        <result column="level" property="level"/>

    </resultMap>
    <resultMap id="MemberLevelDtoMap" type="com.medusa.gruul.account.model.dto.MemberLevelDto">
        <id column="id" property="id"/>
        <result column="member_level" property="memberLevel"/>
        <result column="level_code" property="levelCode"/>
        <result column="remark" property="remark"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="point_exchange" property="pointExchange"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="member_card_name" property="memberCardName"/>
        <result column="legal_right" property="legalRight"/>
        <result column="disable" property="disable"/>
        <result column="level" property="level"/>
        <result column="default_level" property="defaultLevel"/>
        <result column="send_status" property="sendStatus"/>
    </resultMap>


    <resultMap id="MemberLevelPriveMap" type="com.medusa.gruul.account.model.vo.MemberLevePriceVo">
        <id column="id" property="id"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level" property="memberLevel"/>
        <result column="specs" property="specs"/>
        <result column="price" property="price"/>
        <result column="original_price" property="originalPrice"/>
        <result column="member_level_price" property="memberLevelPrice"/>
        <result column="member_level_percentage" property="memberLevelPercentage"/>
        <result column="skuId" property="skuId"/>
    </resultMap>

    <resultMap id="MiniAccountMemberRightsAndInterestsMap" type="com.medusa.gruul.account.api.entity.MemberLevelRights">
        <id column="id" property="id"/>
       <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="power_explain" property="powerExplain"/>
        <result column="icon" property="icon"/>
    </resultMap>

    <resultMap id="RightsAndInterests" type="com.medusa.gruul.account.api.entity.MemberLevelRights">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="power_explain" property="powerExplain"/>
        <result column="enable" property="enable"/>
        <result column="icon" property="icon"/>
    </resultMap>
    <!-- 通用查询结果列 -->
<!--    <sql id="Base_Column_List">-->
<!--        create_time,-->
<!--        is_deleted,-->
<!--        update_time,-->
<!--        id, restrict_type, user_id-->
<!--    </sql>-->
<!--    修改会员等级表-->
    <update id="update"  parameterType="Object" >
        update t_member_level
        <trim   prefix="SET" suffixOverrides="," >
        <if test="memberLevel != null">
            /* 会员等级 */
            member_level = #{memberLevel,jdbcType=VARCHAR},
        </if>
        <if test="paymentAmount != null">
            /* 金额 */
            payment_amount = #{paymentAmount,jdbcType=VARCHAR},
        </if>
        <if test="memberCardName != null">
            /* 会员卡名字 */
            member_card_name = #{memberCardName,jdbcType=VARCHAR},
        </if>
        <if test="levelCode != null">
            /* 等级编码 */
            level_code = #{levelCode,jdbcType=VARCHAR},
        </if>
        <if test="stockFlag != null">

            stock_flag = #{stockFlag,jdbcType=VARCHAR},
        </if>
        <if test="ruleType != null">

            rule_Type = #{ruleType,jdbcType=INTEGER},
        </if>
        <if test="parentReceive != null">

            parent_Receive = #{parentReceive,jdbcType=DECIMAL},
        </if>
        <if test="aboveParentReceive != null">

            above_Parent_Receive = #{aboveParentReceive,jdbcType=DECIMAL},
        </if>
        <if test="minPayAmount != null">

            min_Pay_Amount = #{minPayAmount,jdbcType=DECIMAL},
        </if>
        </trim>
        where
         id = #{id}
    </update>

    <!--    删除会员等级表-->
    <update id="deleteMemberLevel"  parameterType="Object" >
        update t_member_level
        <trim   prefix="SET" suffixOverrides="," >
            <if test="deleted != null">
                /* 删除标识 */
                is_deleted = #{deleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                /* 更新时间 */
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        where
        id = #{id}
    </update>

    <insert id="add" parameterType="Object" >
	  INSERT  INTO  t_member_level   /* 会员表 */
					(
					    id,
                        is_deleted,
                        member_level,
                        disable,
                        payment_amount
					)
			values (
			        #{id,jdbcType=VARCHAR}
                    ,0
                    ,#{memberLevel,jdbcType=VARCHAR}
                    ,0
                    ,#{paymentAmount,jdbcType=VARCHAR}
					)
	</insert>


    <select id="selectMemberLevelList" resultMap="MiniAccountMemberMap">
       SELECT id,member_level,point_exchange,payment_amount,legal_right,disable,level,default_level,stock_flag,rule_type,parent_receive,above_parent_receive,min_pay_amount
       FROM    t_member_level where is_deleted = 0 order by level
    </select>

<!--    查询全部会员等级-->
    <select id="selectMemberLevelAllList" resultMap="MemberLevelVoMap">
       SELECT id,member_level,point_exchange,payment_amount,legal_right,disable,0 as isSelected,level
       FROM    t_member_level where is_deleted = 0 and disable ='0' order by level
    </select>

    <!--    权益表   -->
    <!--<select id="getPowerAll" resultType="RightsAndInterests">
       	 SELECT id,type,name,power_explain,enable,icon FROM t_member_rights_and_interests WHERE enable!='1' and is_delete!='1'
    </select>-->
    <select id="selectRightsAndInterestsList" resultMap="MiniAccountMemberRightsAndInterestsMap">
        SELECT id,type,name,power_explain,enable FROM t_member_level_rights WHERE
        id in
        <foreach collection="rightsIdList" item="id"
                 index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="selectRights" resultMap="MiniAccountMemberRightsAndInterestsMap">
        SELECT id,type,name,power_explain,enable,icon FROM t_member_level_rights

    </select>

    <select id="selectMemberLevePrice" resultMap="MemberLevelPriveMap">
        SELECT
            l.member_level,
            l.id as member_level_id,
            m.id,
            s.price,
            s.original_price,
            s.id as skuId,
            s.specs ,
            m.member_level_price,
            m.member_level_percentage
        FROM
            t_member_level l
                LEFT JOIN t_member_level_goods_price m ON m.member_level_id = l.id AND m.product_id = #{product_id}
                LEFT JOIN t_sku_stock s ON s.product_id = #{product_id}
        WHERE l.is_deleted=0 order by l.level
    </select>



    <!--    权益表  end -->


    <select id="selectExternalByMemberLeverList" resultMap="MemberLevelDtoMap">
        select
        DISTINCT
            tml.id,
            tml.member_level,
            tml.remark,
            tml.point_exchange,
            tml.payment_amount,
            tmr.rights_id as legal_right,
            tml.member_card_name,
            tml.level
        from t_member_level as tml
        left join t_member_level_rights_relation as tmr on tml.id = tmr.member_level_id
        where IFNULL(tml.send_status, 0) != 1 and tml.disable !=1 and tml.is_deleted = 0
    </select>
    <update id="updateSendStatus">
        update t_member_level set send_status=#{sendStatus} where id in
        <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </update>
</mapper>
