package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoodsCode;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.PackageGoodsCodeDetailParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsCodeDetailVo;
import com.medusa.gruul.account.api.model.vo.ShopPackageGoodsCodeVo;
import com.medusa.gruul.account.api.model.ManageVerifyPackageGoodsStaticDto;
import com.medusa.gruul.account.model.dto.MiniAccountPackageGoodsDto;
import com.medusa.gruul.account.model.dto.VerifyGoodsDto;
import com.medusa.gruul.account.model.param.ApiPackageGoodsCodeParam;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsCodeVo;
import com.medusa.gruul.account.api.model.vo.ManageVerifyPackageGoodsStaticVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsCodeVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:13 2024/9/10
 */
public interface IMiniAccountPackageGoodsCodeService extends IService<MiniAccountPackageGoodsCode> {
    /**
     * 添加记录，生成验证码
     * @param miniAccountPackageGoodsDto
     * @return
     */
    MiniAccountPackageGoodsCode add(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto);
    /**
     * 获取用户权益包商品验证码
     * @param miniAccountPackageGoodsDto
     * @return
     */
    MiniAccountPackageGoodsCodeVo getCode(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto);

    /**
     * pc获取用户权益包商品验证码
     * @param miniAccountPackageGoodsDto
     * @return
     */
    MiniAccountPackageGoodsCodeVo pcGetCode(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto);

    /**
     * 后台核销商品
     * @param miniAccountPackageGoodsDto
     */
    MiniAccountPackageGoodsCode writeOffPackageGoods(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto);

    /**
     * 核销权益包商品验证码
     * @param dto 核销商品
     * @return
     */
    MiniAccountPackageGoodsCode verifyCode(VerifyGoodsDto dto);

    /**
     * 商家用户查询自己核销的权益包商品记录
     * @return
     */
    IPage<ShopPackageGoodsCodeVo> pageShopUserVerifyCode(MiniAccountPackageGoodsCodeParam miniAccountPackageGoodsCodeParam);

    /**
     * 获取核销明细列表
     * @param param
     * @return
     */
    IPage<PackageGoodsCodeDetailVo>pagePackageGoodsCodeDetail(PackageGoodsCodeDetailParam param);


    /**
     * 通过验证码获取通惠证记录
     * @param verifyCode 核销码
     * @return
     */
    MiniAccountPackageGoodsVo getPackageGoodsByCode(String verifyCode);

    /**
     * 导出商家核销的记录
     * @param miniAccountPackageGoodsCodeParam
     */
    void exportShopPassTicketCode(MiniAccountPackageGoodsCodeParam miniAccountPackageGoodsCodeParam);

    /**
     * 分页获取小程序用户核销记录
     * @param param
     * @return
     */
    IPage<ApiPackageGoodsCodeVo>pageApiPackageGoodsCode(ApiPackageGoodsCodeParam param);

    /**
     * 门店、员工核销权益包数量汇总
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStatic( ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto);

    /**
     * 门店、员工核销权益包数量汇总合计
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticTotal( ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto);

    void exportApiPackageGoodsCode(ApiPackageGoodsCodeParam param);
}
