package com.medusa.gruul.account.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:43 2024/9/27
 */
@Data
@ApiModel(value = "小程序用户权益包管理vo")
public class ApiMiniAccountPackageOrderVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;
    /**
     * 权益包使用期限开始时间
     */
    @ApiModelProperty(value = "权益包使用期限开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageStartTime;
    /**
     * 权益包使用期限开始时间-格式化
     */
    @ApiModelProperty(value = "权益包使用期限开始时间-格式化")
    private String packageStartTimeStr;


    /**
     * 权益包使用期限结束时间
     */
    @ApiModelProperty(value = "权益包使用期限结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageEndTime;

    /**
     * 权益包使用期限结束时间-格式化
     */
    @ApiModelProperty(value = "权益包使用期限结束时间-格式化")
    private String packageEndTimeStr;

    /**
     * 权益包使用期限结束时间
     */
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String departmentName;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName2;

    /**
     * 权益包商品
     */
    @ApiModelProperty(value = "权益包商品")
    private List<MiniAccountPackageGoods>packageGoodsList;
}
