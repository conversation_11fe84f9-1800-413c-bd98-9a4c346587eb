package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:24 2024/10/28
 */
@Data
@ApiModel(value = "会员详情")
public class MiniAccountDetailVo {
    @ApiModelProperty(value = "id ")
    private Long id;
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "会员卡号")
    private String cardNumber;
    @ApiModelProperty(value = "用户昵称")
    private String nikeName;
    @ApiModelProperty(value = "会员等级")
    private String memberLevel;
    @ApiModelProperty(value = "成为会员时间")
    private LocalDateTime firstLoginTime;
    @ApiModelProperty(value = "手机号码")
    private String phone;
    @ApiModelProperty(value = "推荐人")
    private String parentName;
    @ApiModelProperty(value = "消费次数")
    private Integer consumeNum;
    @ApiModelProperty(value = "交易总额")
    private BigDecimal consumeTotalMoney;
    @ApiModelProperty(value = "会员消费额")
    private BigDecimal memberMoney;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "性别 0：未知、1：男、2：女")
    private Integer gender;
    @ApiModelProperty(value = "客户名称")
    private String userName;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "身份证")
    private String card;
    @ApiModelProperty(value = "积分")
    private BigDecimal integral;
    @ApiModelProperty(value = "已消费积分")
    private BigDecimal usedIntegral;
    @ApiModelProperty(value = "当前积分")
    private BigDecimal currentIntegral;
    @ApiModelProperty(value = "销售积分")
    private BigDecimal saleIntegral;
    @ApiModelProperty(value = "总积分")
    private BigDecimal allIntegral;
}
