package com.medusa.gruul.account.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountPassTicketVo;
import com.medusa.gruul.account.api.model.vo.PackageGoodsCodeDetailVo;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketCodeDto;
import com.medusa.gruul.account.model.param.MiniAccountPassTicketParam;
import com.medusa.gruul.account.model.param.MiniShopsPartnerParam;
import com.medusa.gruul.account.model.vo.MiniAccountPassTicketCodeVo;
import com.medusa.gruul.account.model.vo.MiniShopsPartnerVo;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序用户-通行票控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/passTicket")
@Api(tags = "小程序用户-通惠证相关接口")
public class MiniAccountPassTicketController {

    @Autowired
    private IMiniAccountPassTicketService miniAccountPassTicketService;

    @Autowired
    private IMiniAccountPassTicketCodeService miniAccountPassTicketCodeService;
    @Autowired
    private IMiniAccountPackageGoodsCodeService miniAccountPackageGoodsCodeService;
    /**
     * 获取用户可用通行票记录数
     *
     * @return
     */
    @PostMapping("/getTicketNum")
    @ApiOperation(value = "获取用户可用通行票记录数")
    public Result getTicketNum() {
        Integer num = miniAccountPassTicketService.myUnusedCount();
        JSONObject json = new JSONObject();
        json.put("num", num);
        return Result.ok(json);
    }

    /**
     * 分页获取用户通行票信息
     *
     * @param miniAccountPassTicketParam 分页数据
     * @return
     */
    @PostMapping("/pageUserPassTicket")
    @ApiOperation(value = "分页获取用户通行票信息")
    public Result<IPage<MiniAccountPassTicketVo>> pageUserPassTicket(@RequestBody MiniAccountPassTicketParam miniAccountPassTicketParam) {
        IPage<MiniAccountPassTicketVo> page = miniAccountPassTicketService.pageMyTicket(miniAccountPassTicketParam) ;
        return Result.ok(page);
    }


    /**
     * 分页获取通行票可用商家记录
     *
     * @param miniShopsPartnerParam
     * @return
     */
    @PostMapping("/pageShopsPartnerByTicketId")
    @ApiOperation(value = "分页获取通行票可用商家记录")
    public Result<IPage<MiniShopsPartnerVo>> pageShopsPartnerByTicketId(@RequestBody MiniShopsPartnerParam miniShopsPartnerParam) {
        IPage<MiniShopsPartnerVo> page = miniAccountPassTicketService.pageShopsPartnerOrderByTicketId(miniShopsPartnerParam);
        return Result.ok(page);
    }

    /**
     * 获取通行票验证码
     *
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    @PostMapping("/getCode")
    @ApiOperation(value = "获取通行票验证码")
    public Result<MiniAccountPassTicketCodeVo> getCode(@RequestBody MiniAccountPassTicketCodeDto miniAccountPassTicketCodeDto) {
        MiniAccountPassTicketCodeVo codeVo = miniAccountPassTicketCodeService.getCode(miniAccountPassTicketCodeDto);
        return Result.ok(codeVo);
    }



  /* // 查不出数据
   @RequestMapping(value = "/export/shopPassTicketCode", method = RequestMethod.POST)
    @ApiOperation(value = "导出商家核销记录")
    public void exportShopPassTicketCode(@RequestBody MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam){
       this.miniAccountPassTicketCodeService.exportShopPassTicketCode(miniAccountPassTicketCodeParam);
    }*/


    @RequestMapping(value = "/export/shopPassTicketCode", method = RequestMethod.POST)
    @ApiOperation(value = "导出商家核销记录")
    public void exportShopPassTicketCode(@RequestBody MiniAccountPackageGoodsCodeParam param){
        miniAccountPackageGoodsCodeService.exportShopPassTicketCode(param);
    }
}
