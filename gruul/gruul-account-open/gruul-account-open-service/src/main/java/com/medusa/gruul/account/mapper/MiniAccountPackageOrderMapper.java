package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountPackageOrder;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.vo.ApiMiniAccountPackageOrderVo;
import com.medusa.gruul.account.model.param.ApiMiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: 会员权益包购买记录Mapper
 * @Date: Created in 15:12 2024/9/5
 */
@Repository
public interface MiniAccountPackageOrderMapper extends BaseMapper<MiniAccountPackageOrder> {

    IPage<MiniAccountPackageOrderVo> getPageList(Page<MiniAccountPackageOrderVo> page,
                                                 @Param("paramMap") MiniAccountPackageOrderParam miniAccountPackageOrderParam);

    /**
     * 分页获取小程序权益包管理信息
     * @param page
     * @param param
     * @return
     */
    IPage<ApiMiniAccountPackageOrderVo>getApiMiniAccountPackageOrder(Page<ApiMiniAccountPackageOrderVo>page,
                                                                     @Param("paramMap") ApiMiniAccountPackageOrderParam param);

}
