package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberLevelRule;
import com.medusa.gruul.account.model.dto.MemberLevelRuleDto;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:49 2024/11/5
 */
public interface IMemberLevelRuleService extends IService<MemberLevelRule> {
    /**
     * 获取会员等级规则
     * @return
     */
    List<MemberLevelRuleVo> getMemberLevelRule();

}
