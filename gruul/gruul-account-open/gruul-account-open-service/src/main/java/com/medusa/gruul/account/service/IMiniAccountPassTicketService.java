package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicket;
import com.medusa.gruul.account.api.model.vo.MiniAccountPassTicketVo;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketDto;
import com.medusa.gruul.account.model.param.MiniAccountPassTicketParam;
import com.medusa.gruul.account.model.param.MiniShopsPartnerParam;
import com.medusa.gruul.account.model.vo.MiniShopsPartnerVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.entity.ShopsPartner;

import java.util.List;

/**
 *会员-通行票服务接口
 * <AUTHOR>
 */
public interface IMiniAccountPassTicketService extends IService<MiniAccountPassTicket> {

    /**
     * 查询用户多少张未用通行票
     * @return
     */
    Integer myUnusedCount();

    /**
     * 添加记录
     * @param miniAccountPassTicketDto
     * @return
     */
    MiniAccountPassTicket add(MiniAccountPassTicketDto miniAccountPassTicketDto);

    /**
     * 添加队列发送通惠证订单消息
     * @param orderVo
     */
    void addMiniAccountPassTicketByMq(OrderVo orderVo);


    /**
     * 分页查询用户个人的通行票记录
     * @param param
     * @return
     */
    IPage<MiniAccountPassTicketVo> pageMyTicket(MiniAccountPassTicketParam param);

    /**
     * 分页查询通行票可用商家记录
     * @param miniShopsPartnerParam
     * @return
     */
    IPage<MiniShopsPartnerVo> pageShopsPartnerOrderByTicketId(MiniShopsPartnerParam miniShopsPartnerParam);

    /**
     * 根据订单id删除通惠证记录
     * @param orderId
     * @return
     */
    Boolean deleteByPassTicketId(Long orderId);
    /**
     * 根据订单id冻结通惠证记录
     * @param orderId
     * @return
     */
    Boolean freezeAccountPassTicket(Long orderId);
    /**
     * 根据订单id解冻通惠证记录
     * @param orderId
     * @return
     */
    Boolean thawAccountPassTicket(Long orderId);
}
