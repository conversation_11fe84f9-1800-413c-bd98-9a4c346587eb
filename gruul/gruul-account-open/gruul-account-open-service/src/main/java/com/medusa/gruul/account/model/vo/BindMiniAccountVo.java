package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:51 2024/10/28
 */
@Data
@ApiModel(value = "可绑定的客户")
public class BindMiniAccountVo {

    @ApiModelProperty(value = "小程序用户id")
    private String userId;
    @ApiModelProperty(value = "客户名称")
    private String nikeName;
    @ApiModelProperty(value = "客户手机号")
    private String phone;
    @ApiModelProperty(value = "加入时间")
    private String joinDate;

}
