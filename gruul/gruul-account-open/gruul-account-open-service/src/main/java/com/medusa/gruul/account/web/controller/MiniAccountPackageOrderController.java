package com.medusa.gruul.account.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.account.service.IMiniAccountPackageOrderService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:11 2024/9/5
 */
@RestController
@RequestMapping("/mini-account-package-order")
@Api(tags = "用户权益包订单相关接口")
public class MiniAccountPackageOrderController {

    @Autowired
    private IMiniAccountPackageOrderService miniAccountPackageOrderService;


    /**
     * 分页获取用户权益包订单
     * @param miniAccountPackageOrderParam
     * @return
     */
    @PostMapping("/getPageList")
    @ApiOperation(value = "分页获取用户权益包订单")
    public Result<PageUtils<MiniAccountPackageOrderVo>> getMiniAccountCouponSearchVo(@RequestBody MiniAccountPackageOrderParam miniAccountPackageOrderParam){
        IPage<MiniAccountPackageOrderVo> pageList = miniAccountPackageOrderService.getPageList(miniAccountPackageOrderParam);
        return Result.ok(new PageUtils<>(pageList));
    }


    /**
     * 导出销售记录
     * @param miniAccountPackageOrderParam
     * @return
     */
    @PostMapping("/exportSale")
    @ApiOperation(value = "导出销售记录")
    public void exportPackageOrder(@RequestBody MiniAccountPackageOrderParam miniAccountPackageOrderParam){
         miniAccountPackageOrderService.exportPackageOrder(miniAccountPackageOrderParam);
    }

}
