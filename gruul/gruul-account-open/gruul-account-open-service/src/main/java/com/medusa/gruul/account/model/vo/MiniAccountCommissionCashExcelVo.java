package com.medusa.gruul.account.model.vo;

import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: rbw
 * @Description: 提现列表导出excel数据
 * @Date: Created in 14:39 2023/9/26
 */
@Data
@ApiModel(value = "提现列表展示数据")
public class MiniAccountCommissionCashExcelVo {


    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private String createTime;

    /**
     * 提现人
     */
    @ApiModelProperty(value = "提现人")
    private String nikeName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;


    /**
     * 状态:100->待审核;101->审核通过;200->驳回;300->已打款
     */
    @ApiModelProperty(value = "状态")
    private String status;


    /**
     * 放款时间
     */
    @ApiModelProperty(value = "打款时间")
    private String payTime;

    /**
     * 付款失败原因
     */
    @ApiModelProperty(value = "付款失败原因")
    private String reason;

    /**
     * 审核失败原因
     */
    @ApiModelProperty(value = "审核失败原因")
    private String comments;

}
