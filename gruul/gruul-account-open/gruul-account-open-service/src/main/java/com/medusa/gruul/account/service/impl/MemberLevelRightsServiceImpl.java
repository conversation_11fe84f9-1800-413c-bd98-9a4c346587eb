package com.medusa.gruul.account.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberLevelRights;
import com.medusa.gruul.account.mapper.MemberLevelRightsMapper;
import com.medusa.gruul.account.model.vo.MemberLevelRightsVo;
import com.medusa.gruul.account.service.IMemberLevelRightsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 会员权益
 * @Author: jeecg-boot
 * @Date:   2022-02-23
 * @Version: V1.0
 */
@Service
public class MemberLevelRightsServiceImpl extends ServiceImpl<MemberLevelRightsMapper, MemberLevelRights> implements IMemberLevelRightsService {

    @Override
    public List<MemberLevelRights> selectRightsList(List<String> rightsIdList) {
        return  this.baseMapper.selectRightsList(rightsIdList);
    }

    @Override
    public List<MemberLevelRightsVo> selectRightsVoAllList() {
        return this.baseMapper.selectRightsVoAllList();
    }

    @Override
    public List<MemberLevelRights> selectRightsListByEnable(List<String> rightsIdList) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<MemberLevelRights>().eq(MemberLevelRights::getEnable,0).in(MemberLevelRights::getId,rightsIdList));
    }
}
