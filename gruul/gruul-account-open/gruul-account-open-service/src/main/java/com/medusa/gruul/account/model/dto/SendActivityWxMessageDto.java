package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:32 2024/11/13
 */
@Data
@ApiModel(value = "发送活动消息dto")
public class SendActivityWxMessageDto {

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动内容")
    private String content;

    @ApiModelProperty(value = "活动时间")
    private String activityDate;

    @ApiModelProperty(value = "客户用户id")
    private List<String> userIds;

    @ApiModelProperty(value = "标签id")
    private List<String>tagIds;

    @ApiModelProperty(value = "商品id")
    private String productId;


}
