package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsParam;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsShowVo;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.order.api.model.OrderVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:29 2024/9/4
 */
public interface IMiniAccountPackageGoodsService extends IService<MiniAccountPackageGoods> {

    /**
     * 分页获取权益包订单商品列表
     * @param miniAccountPackageGoodsParam
     * @return
     */
    IPage<MiniAccountPackageGoodsVo> getPageList(MiniAccountPackageGoodsParam miniAccountPackageGoodsParam);

    /**
     * 更新权益包商品使用状态
     */
    void updateMiniAccountPackageGoodsStatus(String mainId);

    /**
     * 根据用户权益包订单id获取权益包商品
     * @param miniAccountPackageGoodsParam
     * @return
     */
    IPage<MiniAccountPackageGoodsVo>getMiniAccountPackageGoods(MiniAccountPackageGoodsParam miniAccountPackageGoodsParam);

    /**
     * 根据用户权益包订单id获取用户权益包订单商品
     * @param mainId
     * @return
     */
    List<ApiPackageGoodsVo>getApiPackageGoods(String mainId);

    /**
     * 获取权益包商品核销记录
     * @param orderId
     * @param packageId
     * @return
     */
    List<PackageGoodsShowVo> getPackageGoodsShowVo(String orderId, String packageId);

    void export(MiniAccountPackageGoodsParam param);
}
