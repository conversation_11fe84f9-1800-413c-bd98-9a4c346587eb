package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.entity.MiniAccountPackageOrder;
import com.medusa.gruul.account.api.enums.PackageStatusEnum;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsShowVo;
import com.medusa.gruul.account.mapper.MiniAccountPackageGoodsMapper;
import com.medusa.gruul.account.mapper.MiniAccountPackageOrderMapper;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsExcelVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.crypto.Data;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:30 2024/9/4
 */
@Service
public class MiniAccountPackageGoodsServiceImpl extends ServiceImpl<MiniAccountPackageGoodsMapper, MiniAccountPackageGoods>implements IMiniAccountPackageGoodsService {

    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private MiniAccountPackageOrderMapper miniAccountPackageOrderMapper;

    @Override
    public IPage<MiniAccountPackageGoodsVo> getPageList(MiniAccountPackageGoodsParam miniAccountPackageGoodsParam) {
        IPage<MiniAccountPackageGoodsVo> pageList = this.baseMapper.getPageList(new Page<>(miniAccountPackageGoodsParam.getCurrent(), miniAccountPackageGoodsParam.getSize()),
                miniAccountPackageGoodsParam);
        return pageList;
    }

    @Override
    public void updateMiniAccountPackageGoodsStatus(String mainId) {
        LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
        List<Integer>statusList = new ArrayList<>();
        statusList.add(PackageStatusEnum.NO_EFFECTIVE.getStatus());
        statusList.add(PackageStatusEnum.UN_USE.getStatus());
        wrapper.in(MiniAccountPackageGoods::getStatus,statusList);
        if(StringUtil.isNotEmpty(mainId)){
            wrapper.eq(MiniAccountPackageGoods::getMainId,mainId);
        }
        List<MiniAccountPackageGoods> list = this.list(wrapper);
        LocalDateTime now = LocalDateTime.now();
        if(list!=null&&list.size()>0){
            for (MiniAccountPackageGoods miniAccountPackageGoods : list) {
                Integer notTerm = miniAccountPackageGoods.getNotTerm();
                LocalDateTime startTime = miniAccountPackageGoods.getStartTime();
                LocalDateTime endTime = miniAccountPackageGoods.getEndTime();
                if(now.isBefore(startTime)){
                    miniAccountPackageGoods.setStatus(PackageStatusEnum.NO_EFFECTIVE.getStatus());
                }
                if(startTime.isBefore(now)&&endTime.isAfter(now)){
                    miniAccountPackageGoods.setStatus(PackageStatusEnum.UN_USE.getStatus());
                }
                //有期限权益包商品，时间过后设为失效
                if(notTerm!=1){
                    if(now.isAfter(endTime)){
                        miniAccountPackageGoods.setStatus(PackageStatusEnum.EXPIRED.getStatus());
                    }
                }
                this.updateById(miniAccountPackageGoods);
            }
        }


    }

    @Override
    public IPage<MiniAccountPackageGoodsVo> getMiniAccountPackageGoods(MiniAccountPackageGoodsParam param) {
        IPage<MiniAccountPackageGoodsVo> pageList = this.baseMapper.getMiniAccountPackageGoods(new Page<>(param.getCurrent(),param.getSize()),param);
        return pageList;
    }

    @Override
    public List<ApiPackageGoodsVo> getApiPackageGoods(String mainId) {
        List<ApiPackageGoodsVo> list = this.baseMapper.getApiPackageGoods(mainId);
        if(list!=null&&list.size()>0){
            for (ApiPackageGoodsVo apiPackageGoodsVo : list) {
                if(apiPackageGoodsVo.getProductId()!=null){
                    ProductVo productVo = remoteGoodsService.findProductById(apiPackageGoodsVo.getProductId());
                    if(productVo!=null){
                        apiPackageGoodsVo.setProductPic(productVo.getPic());
                        apiPackageGoodsVo.setProductName(productVo.getName());
                    }
                }
                if(apiPackageGoodsVo.getSkuId()!=null){
                    SkuStock skuStock = remoteGoodsService.findSkuStockById(apiPackageGoodsVo.getSkuId());
                    if(skuStock!=null){
                        apiPackageGoodsVo.setSkuName(skuStock.getSpecs());
                    }
                }

            }
        }
        return list;
    }

    @Override
    @Transactional
    public List<PackageGoodsShowVo> getPackageGoodsShowVo(String orderId, String packageId) {
        //1.更新权益包商品状态
        LambdaQueryWrapper<MiniAccountPackageOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPackageOrder::getOrderId,orderId);
        List<MiniAccountPackageOrder> orderList = miniAccountPackageOrderMapper.selectList(wrapper);
        if(orderList!=null&&orderList.size()>0){
            for (MiniAccountPackageOrder miniAccountPackageOrder : orderList) {
                updateMiniAccountPackageGoodsStatus(miniAccountPackageOrder.getId()+"");
            }
        }
        //2.查询对应权益包商品
        List<PackageGoodsShowVo>list = this.baseMapper.getPackageGoodsShowVo(orderId,packageId);
        if(list!=null&&list.size()>0){
            for (PackageGoodsShowVo packageGoodsShowVo : list) {
                Long productId = packageGoodsShowVo.getProductId();
                ProductVo product = remoteGoodsService.findProductById(productId);
                packageGoodsShowVo.setProductName(product.getName());
                packageGoodsShowVo.setProductPic(product.getPic());
            }
        }
        return list;
    }

    @Override
    public void export(MiniAccountPackageGoodsParam param) {
        HuToolExcelUtils.exportParamToMax(param);
        IPage<MiniAccountPackageGoodsVo> pageList = this.getPageList(param);
        HuToolExcelUtils.exportData(pageList.getRecords(), "权益包商品列表", source->{
            MiniAccountPackageGoodsExcelVo target = new MiniAccountPackageGoodsExcelVo();
            target.setOrderId(source.getOrderId()+"");
            target.setRemainingTimes(source.getAllTimes()-source.getAlreadyTimes());
            DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            target.setStartTime(source.getStartTime().format(ofPattern));
            target.setEndTime(source.getEndTime().format(ofPattern));
            if (source.getStatus()!=null){
                switch (source.getStatus()){
                    case 100:
                        target.setStatus("未用");
                        break;
                    case 101:
                        target.setStatus("已用");
                        break;
                    case 200:
                        target.setStatus("已失效");
                        break;
                    default:
                        target.setStatus("未知");
                        break;
                }
            }
            return  target;
        });
    }
}
