package com.medusa.gruul.account.web.controller;

import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.model.dto.MemberLevelRuleDto;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;
import com.medusa.gruul.account.service.IMemberLevelRuleService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:02 2024/11/5
 */
@RestController
@RequestMapping("/member-level-rule")
@Api(tags = "会员等级规则相关接口")
public class MemberLevelRuleController {

    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;

    /**
     * 获取会员等级规则
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取会员等级规则")
    public Result list() {
        List<MemberLevelRuleVo> list = memberLevelRuleService.getMemberLevelRule();
        return Result.ok(list);
    }

}
