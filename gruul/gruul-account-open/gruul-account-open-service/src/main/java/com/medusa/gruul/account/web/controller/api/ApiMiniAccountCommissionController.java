package com.medusa.gruul.account.web.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketCodeDto;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMiniAccountCommissionService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序用户-佣金控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/mini-account-commission")
@Api(tags = "小程序用户-佣金相关接口")
public class ApiMiniAccountCommissionController {

    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;

    /**
     * 获取个人佣金聚合信息接口
     *
     * @return 
     */
    @PostMapping("/getMyCommissionMore")
    @ApiOperation(value = "获取个人佣金聚合信息接口")
    public Result<MyCommissionMoreVo> getMyCommissionMore() {
        MyCommissionMoreVo vo = miniAccountCommissionService.getCommissionMore();
        return Result.ok(vo);
    }
    /**
     * 分页查询用户个人的佣金明细记录
     *
     * @param miniAccountCommissionParam 分页数据
     * @return 佣金明细
     */
    @PostMapping("/pageUserCommission")
    @ApiOperation(value = "分页查询用户个人的佣金明细记录")
    public Result<PageUtils<MiniAccountCommission>> pageUserCommission(@RequestBody MiniAccountCommissionParam miniAccountCommissionParam) {
        IPage<MiniAccountCommission> page = miniAccountCommissionService.pageMyCommission(miniAccountCommissionParam) ;
        return Result.ok(new PageUtils<>(page));
    }
    /**
     * 获取当前用户佣金信息
     * @return
     */
    @GetMapping("/getMiniAccountCommissionVo")
    @ApiOperation(value = "获取当前用户佣金信息")
    public Result<UserCommissionVo> getMiniAccountCommissionVo() {
        UserCommissionVo userCommissionVo = miniAccountCommissionService.getUserCommissionVo();
        return Result.ok(userCommissionVo);
    }

    /**
     * 获取当前用户分销订单列表
     * @param distributionOrderParam
     * @return
     */
    @PostMapping("/getDistributionOrderVo")
    @ApiOperation(value = "获取当前用户分销订单列表")
    public Result<PageUtils<DistributionOrderVo>>getDistributionOrderVo(@RequestBody @Validated DistributionOrderParam distributionOrderParam){
        PageUtils<DistributionOrderVo> distributionOrderVo = miniAccountCommissionService.getDistributionOrderVo(distributionOrderParam);
        return Result.ok(distributionOrderVo);
    }




}
