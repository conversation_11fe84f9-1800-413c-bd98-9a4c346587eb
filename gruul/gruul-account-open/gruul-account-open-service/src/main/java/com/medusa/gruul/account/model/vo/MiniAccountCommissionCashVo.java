package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: plh
 * @Description: 提现列表展示数据
 * @Date: Created in 14:39 2023/8/31
 */
@Data
@ApiModel(value = "提现列表展示数据")
public class MiniAccountCommissionCashVo {


    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime createTime;

    /**
     * 提现人
     */
    @ApiModelProperty(value = "提现人")
    private String nikeName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;


    /**
     * 状态:100->待审核;101->审核通过;200->驳回;300->已打款
     */
    @ApiModelProperty(value = "状态:100->待审核;101->审核通过;200->驳回;300->已打款")
    private String status;

    /**
     * 放款时间
     */
    @ApiModelProperty(value = "打款时间")
    private LocalDateTime payTime;

    /**
     * 付款失败原因
     */
    @ApiModelProperty(value = "付款失败原因")
    private String reason;

    /**
     * 审核失败原因
     */
    @ApiModelProperty(value = "审核失败原因")
    private String comments;

    /**
     *支付状态，SUCCESS:转账成功，FAILED:转账失败，PROCESSING:处理中
     */
    @ApiModelProperty(value = "支付状态，SUCCESS:转账成功，FAILED:转账失败，PROCESSING:处理中")
    private String payStatus;


    /**
     * 转账状态
     */
    @ApiModelProperty(value = "转账状态")
    private String state;

    /**
     * 微信转账单号
     */
    @ApiModelProperty(value = "微信转账单号")
    private String transferBillNo;

    /**
     * 跳转领取页面的package信息
     */
    @ApiModelProperty(value = "跳转领取页面的package信息")
    private String packageInfo;
}
