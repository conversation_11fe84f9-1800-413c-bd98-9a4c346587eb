package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 后台佣金手动提现 dto
 */
@Data
public class MiniAccountManualCommissionCashDto {
    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    @NotNull(message = "佣金不能为空")
    private BigDecimal amount;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    /**
     * 后台备注
     */
    @ApiModelProperty(value = "后台备注")
    private String platformRemark;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    private Integer source;

    /**
     * 平台操作用户id
     */
    @ApiModelProperty(value = "平台操作用户id")
    private String platformUserId;

    /**
     * 平台操作用户名
     */
    @ApiModelProperty(value = "平台操作用户名")
    private String platformUserName;
}
