package com.medusa.gruul.account.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:08 2024/9/5
 */
@Data
@ApiModel(value = "查询权益包购买商品记录")
public class MiniAccountPackageGoodsExcelVo {


    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;

    /**
     * 小程序用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 小程序用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String userPhone;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;
    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;


    /**
     * 购买店面
     */
    @ApiModelProperty(value = "购买店面")
    private String payStore;


    /**
     * 总次数
     */
    @ApiModelProperty(value = "可用次数")
    private Integer allTimes;

    /**
     * 已用次数
     */
    @ApiModelProperty(value = "已用次数")
    private Integer alreadyTimes;

    @ApiModelProperty(value = "剩余次数")
    private Integer remainingTimes;

    /**
     * 状态:100->未用;101->已用;200->已失效
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
