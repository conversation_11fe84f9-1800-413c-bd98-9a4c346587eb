package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:16 2024/8/28
 */
@Data
@ApiModel(value = "用户下单选择的优惠券Dto")
public class MiniAccountCouponByOrderDto {

    /**
     * 订单总价
     */
    @ApiModelProperty(value = "id")
    private BigDecimal totalPrice;

    /**
     * 订单店铺id
     */
    @ApiModelProperty(value = "id")
    private String shopId;

    /**
     * 用户下单商品详情
     */
    private List<MiniAccountCouponOrderItemDto> data;
}
