package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:31 2025/3/14
 */
@ApiModel(value = "MiniAccountRoyaltyDetParam 实体", description = "提成明细查询 param")
@Data
public class MiniAccountRoyaltyDetParam extends QueryParam {

    @ApiModelProperty(value = "会员昵称")
    private String nikeName;

    @ApiModelProperty(value = "会员号码")
    private String phone;

    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @ApiModelProperty(value = "订单开始时间")
    private String startTime;

    @ApiModelProperty(value = "订单结束时间")
    private String endTime;

}
