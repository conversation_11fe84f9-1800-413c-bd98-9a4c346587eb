package com.medusa.gruul.account.model.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:05 2024/9/5
 */
@Data
@ApiModel(value = "导出权益包购买记录")
public class MiniAccountPackageOrderExcelVo {


    @ApiModelProperty(value = "序号")
    private Integer index;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 小程序用户名称
     */
    @ApiModelProperty(value = "会员")
    private String userName;

    /**
     * 小程序用户电话
     */
    @ApiModelProperty(value = "电话号码")
    private String userPhone;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "购买时间")
    private String payTime;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "实付款")
    private BigDecimal payAmount;

    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量")
    private Integer productQuantity;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeFrontName;

    /**
     * 是否完成核销->0.否，1.是
     */
    private Integer writeOffFlag;

    @ApiModelProperty("完成核销")
    private String writeOffFlagDict;

    public void setWriteOffFlag(Integer writeOffFlag) {
        this.writeOffFlag = writeOffFlag;
        this.writeOffFlagDict = (writeOffFlag == 1 ? "是" : "否");
    }
}
