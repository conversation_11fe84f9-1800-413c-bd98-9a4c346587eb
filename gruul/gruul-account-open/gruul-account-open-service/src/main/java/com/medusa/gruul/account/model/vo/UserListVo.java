package com.medusa.gruul.account.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @data: 2019/11/19
 */
@Data
@ApiModel(value = "用户列表")
public class UserListVo {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "会员卡编号")
    private String cardNumber;

    @ApiModelProperty(value = "会员卡余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "会员卡返利余额")
    private BigDecimal rebateBonus;

    @ApiModelProperty(value = "会员注册时间")
    private LocalDateTime memberRegisterTime;

    @ApiModelProperty(value = "成为会员天数")
    private Long becomeMemberDayNumber;

    @ApiModelProperty(value = "店铺用户id")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "头像url")
    private String avatarUrl;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "最后交易时间")
    private LocalDateTime orderLastDealTime;

    @ApiModelProperty(value = "消费次数")
    private Integer consumeNum;

    @ApiModelProperty(value = "交易总额")
    private BigDecimal consumeTotleMoney;

    @ApiModelProperty(value = "可用积分")
    private BigDecimal integral;

    @ApiModelProperty(value = "用户团长身份类型 0-普通用户  1-团长  2-区域团长")
    private Integer communityType;

    @ApiModelProperty(value = "用户拥护的标签")
    private List<UserTagVo> userTagVos;

    @ApiModelProperty(value = "会员等级")
    private MemberLevelVo memberLevelVos;

    @ApiModelProperty(value = "首次登陆小程序时间")
    private LocalDateTime firstLoginTime;

    @ApiModelProperty(value = "会员等级id")
    private String memberLevelId;

    @ApiModelProperty(value = "会员等级编码")
    private String memberLevelCode;

    @ApiModelProperty(value = "会员等级名字")
    private String memberLevelName;

    @ApiModelProperty(value = "是否是会员：1是，2否")
    private Integer state;

    /**
     * 发送状态
     */
    @ApiModelProperty(value = "发送状态0->未发送，1—>已发送")
    private Integer sendStatus;

    @ApiModelProperty(value = "加入时间-外部系统需要的时间格式")
    private String joinDate;
    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;
    /**
     * 一级用户数
     */
    @ApiModelProperty(value = "一级用户数")
    private Integer oneTeamNum;
    /**
     * 二级用户数
     */
    @ApiModelProperty(value = "二级级用户数")
    private Integer TwoTeamNum;

    /**
     * 总佣金
     */
    @ApiModelProperty(value = "总佣金")
    private BigDecimal commission;

    /**
     * 当前佣金
     */
    @ApiModelProperty(value = "当前佣金")
    private BigDecimal currentCommission;

    /**
     * 介绍人
     */
    @ApiModelProperty(value = "介绍人")
    private String recommendName;
    /**
     * 部门标识
     */
    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    /**
     * 经手人id
     */
    @ApiModelProperty(value = "经手人id")
    private String employeeId;

    @ApiModelProperty(value = "性别 0：未知、1：男、2：女")
    private Integer gender;
    /**
     * 等级编码，为了跟易达直接对接上，此字段来自易达的卡类型编码
     */
    @ApiModelProperty(value = "等级编码")
    private String levelCode;

    @ApiModelProperty(value = "所属商家")
    private String shopNames;
}
