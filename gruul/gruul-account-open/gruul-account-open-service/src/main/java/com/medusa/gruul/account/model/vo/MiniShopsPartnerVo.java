package com.medusa.gruul.account.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "小程序-通惠证-商家（店铺）展示信息对象")
public class MiniShopsPartnerVo implements Serializable {

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;


    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    private String name;


    /**
     * 地域
     */
    @ApiModelProperty(value = "地域")
    private String region;


    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;


    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;


    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;



    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    private Double mapX;


    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    private Double mapY;

    /**
     * 与用户的距离，单位米
     */
    @ApiModelProperty(value = "与用户的距离，单位米")
    private Double distance;

    /**
     * 与用户的距离，格式化的距离
     */
    @ApiModelProperty(value = "与用户的距离，格式化的距离")
    private String formatDistance;

    /**
     * 购买标识，0未购买1已购买
     */
    @ApiModelProperty(value = "购买标识，0未购买1已购买")
    private Integer buyFlag;

    /**
     * 可用次数
     */
    @ApiModelProperty(value = "可用次数")
    private Integer usableNum;

    /**
     * 已用次数
     */
    @ApiModelProperty(value = "已用次数")
    private Integer usedNum;

    @ApiModelProperty(value = "营业开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalDateTime businessBeginHours;

    @ApiModelProperty(value = "营业结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalDateTime businessEndHours;

    /**
     * 营业标识
     */
    @ApiModelProperty(value = "营业标识，0休息1营业")
    private Integer businessFlag;

    /**
     * 店铺logo
     */
    @ApiModelProperty(value = "店铺logo")
    private String logo;

}
