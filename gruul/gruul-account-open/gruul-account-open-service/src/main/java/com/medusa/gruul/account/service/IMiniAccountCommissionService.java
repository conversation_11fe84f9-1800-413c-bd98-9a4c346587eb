package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicket;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionDto;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketDto;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;

/**
 *会员-佣金明细服务接口
 * <AUTHOR>
 */
public interface IMiniAccountCommissionService extends IService<MiniAccountCommission> {

    /**
     * 获取个人佣金聚合信息接口
     * @return
     */
    MyCommissionMoreVo getCommissionMore();

    /**
     * 添加记录
     * @param miniAccountCommissionDto
     * @return
     */
    MiniAccountCommission add(MiniAccountCommissionDto miniAccountCommissionDto);


    /**
     * 分页查询用户个人的佣金明细记录
     * @param param
     * @return
     */
    IPage<MiniAccountCommission> pageMyCommission(MiniAccountCommissionParam param);

    /**
     * 处理佣金
     * @param orderVo
     */
    void handleCommission(OrderVo orderVo);

    /**
     * 处理奖励活动
     * @param orderVo
     */
    void handleRewardScheme(OrderVo orderVo);

    /**
     * 处理奖励提成
     * @param orderVo
     */
    void handleRewardSchemeRoyalty(OrderVo orderVo);

    /**
     * 处理奖励佣金
     * @param orderVo
     */
    void handleRewardSchemeCommission(OrderVo orderVo);

    /**
     * 分页查询通行票可用商家记录
     * @param miniShopsPartnerParam
     * @return
     */
    //IPage<MiniShopsPartnerVo> pageShopsPartnerOrderByTicketId(MiniShopsPartnerParam miniShopsPartnerParam);

    /**
     * 获取当前用户佣金信息
     * @return
     */
    UserCommissionVo getUserCommissionVo();

    /**
     * 获取用户分销订单列表
     * @param distributionOrderParam
     * @return
     */
    PageUtils<DistributionOrderVo> getDistributionOrderVo(DistributionOrderParam distributionOrderParam);

    /**
     * 分页查询提成明细
     * @param param
     * @return
     */
    PageUtils<MiniAccountRoyaltyDetVo>searchMiniAccountRoyaltyDet(MiniAccountRoyaltyDetParam param);

    /**
     * 手动添加佣金
     * @param miniAccountCommissionDto
     * @return
     */
    MiniAccountCommission manualAddCommission(MiniAccountCommissionDto miniAccountCommissionDto);

}
