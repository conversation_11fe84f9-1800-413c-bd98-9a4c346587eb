package com.medusa.gruul.account.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 分销订单列表展示数据
 * @Date: Created in 16:29 2023/8/31
 */
@Data
@ApiModel(value = "分销订单列表展示数据")
public class DistributionOrderVo {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 商家
     */
    @ApiModelProperty(value = "商家")
    private String shopName;

    /**
     * 商家
     */
    @ApiModelProperty(value = "买家")
    private String buyName;

    /**
     * 推荐人
     */
    @ApiModelProperty(value = "推荐人")
    private String recommendName;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatarUrl;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal price;

}
