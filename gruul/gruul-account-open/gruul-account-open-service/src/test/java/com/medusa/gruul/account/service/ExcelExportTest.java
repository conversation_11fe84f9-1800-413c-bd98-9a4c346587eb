package com.medusa.gruul.account.service;

import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderExcelVo;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导出功能测试
 * <AUTHOR>
 */
public class ExcelExportTest {

    @Test
    public void testExcelDataStructure() {
        // 创建测试数据
        List<MiniAccountPackageOrderExcelVo> testData = createTestData();
        
        // 验证数据结构
        System.out.println("=== Excel导出数据结构测试 ===");
        for (MiniAccountPackageOrderExcelVo item : testData) {
            System.out.println(String.format(
                "序号: %s, 订单号: %s, 会员: %s, 实付款: %s, 购买数量: %s",
                item.getIndex(),
                item.getOrderId(),
                item.getUserName(),
                item.getPayAmount(),
                item.getProductQuantity()
            ));
        }
        
        // 验证合计计算
        BigDecimal totalAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        
        for (MiniAccountPackageOrderExcelVo item : testData) {
            if (!"合计".equals(item.getOrderId())) { // 排除合计行
                if (item.getPayAmount() != null) {
                    totalAmount = totalAmount.add(item.getPayAmount());
                }
                if (item.getProductQuantity() != null) {
                    totalQuantity += item.getProductQuantity();
                }
            }
        }
        
        System.out.println("\n=== 合计验证 ===");
        System.out.println("计算得出的总金额: " + totalAmount);
        System.out.println("计算得出的总数量: " + totalQuantity);
        
        // 检查最后一行是否为合计行
        MiniAccountPackageOrderExcelVo lastRow = testData.get(testData.size() - 1);
        System.out.println("最后一行订单号: " + lastRow.getOrderId());
        System.out.println("最后一行金额: " + lastRow.getPayAmount());
        System.out.println("最后一行数量: " + lastRow.getProductQuantity());
    }
    
    private List<MiniAccountPackageOrderExcelVo> createTestData() {
        List<MiniAccountPackageOrderExcelVo> list = new ArrayList<>();
        
        // 添加测试数据
        for (int i = 1; i <= 5; i++) {
            MiniAccountPackageOrderExcelVo item = new MiniAccountPackageOrderExcelVo();
            item.setIndex(i);
            item.setOrderId("ORDER" + String.format("%03d", i));
            item.setUserName("用户" + i);
            item.setUserPhone("**********" + i);
            item.setPayTime("2024-12-" + String.format("%02d", i) + " 10:00:00");
            item.setPayAmount(new BigDecimal(100 * i));
            item.setProductQuantity(i);
            item.setStoreFrontName("门店" + i);
            item.setWriteOffFlag(i % 2);
            
            list.add(item);
        }
        
        // 添加合计行
        MiniAccountPackageOrderExcelVo totalRow = new MiniAccountPackageOrderExcelVo();
        totalRow.setIndex(null);
        totalRow.setOrderId("合计");
        totalRow.setUserName("");
        totalRow.setUserPhone("");
        totalRow.setPayTime("");
        totalRow.setPayAmount(new BigDecimal(1500)); // 100+200+300+400+500
        totalRow.setProductQuantity(15); // 1+2+3+4+5
        totalRow.setStoreFrontName("");
        totalRow.setWriteOffFlagDict("");
        
        list.add(totalRow);
        
        return list;
    }
}
