package com.medusa.gruul.account.api.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:56 2025/4/2
 */
@Data
@ApiModel(value = "ChooseTagParam 实体", description = "可选择的标签param")
public class ChooseTagParam  extends QueryParam {

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "客户标签ids")
    private List<String> tagIds;
}
