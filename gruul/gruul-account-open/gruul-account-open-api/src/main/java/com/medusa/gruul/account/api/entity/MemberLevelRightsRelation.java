package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_member_level_rights_relation")
@ApiModel(value = "MiniAccountMemberLevelRightsRelation对象", description = "会员等级权益关联表")
public class MemberLevelRightsRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 会员等级表ID
     */
    @ApiModelProperty(value = "会员等级表ID")
    @TableField("member_level_id")
    private String memberLevelId;
    /**
     * 权益表id
     */
    @ApiModelProperty(value = "权益表id")
    @TableField("rights_id")
    private String rightsId;
    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    @TableField("value")
    private String value;

    public MemberLevelRightsRelation(String rightsId,String rightsValue,String memberLevelId){
        this.rightsId = rightsId;
        this.value=rightsValue;
        this.memberLevelId=memberLevelId;
    }
    public MemberLevelRightsRelation(){

    }

}
