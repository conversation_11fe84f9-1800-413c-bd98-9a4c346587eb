package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_member_information")
@ApiModel(value = "MiniAccountMemberInformation", description = "会员信息表")
public class MiniAccountMemberInformation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 卡号
     */
    @ApiModelProperty(value = "卡号")
    @TableField("card_number")
    private String cardNumber;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;
    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    @TableField("integral")
    private String integral;
    /**
     * 会员卡等级id
     */
    @ApiModelProperty(value = "会员卡等级id")
    @TableField("card_type")
    private String cardType;


    /**
     * 有效时间
     */
    @ApiModelProperty(value = "有效时间")
    @TableField("datetime")
    private String datetime;
    /**
     * 余额
     */
    @ApiModelProperty(value = "余额")
    @TableField("balance")
    private String balance;


}
