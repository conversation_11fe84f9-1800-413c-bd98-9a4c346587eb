package com.medusa.gruul.account.api.model.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户拥有的通惠证视图类
 */
@Data
@ApiModel(value = "MiniAccountPassTicketVo", description = "用户拥有的通惠证视图类")
public class MiniAccountPassTicketVo {


    @ApiModelProperty(value = "用户通惠证记录id")
    private Long miniAccountPassTicketId;

    /**
     * 通惠证id
     */
    @ApiModelProperty(value = "通惠证id")
    private Long passTicketId;


    /**
     * 通惠证名称
     */
    @ApiModelProperty(value = "通惠证名称")
    private String ticketName;

    /**
     * 购买单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    private Integer useableTimes;

    /**
     * 票类型:100->满减;101->折扣;
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    private Integer ticketType;

    /**
     * 满额（存满100减20的100值）
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期类型:100->指定时间;101->购买之日起计算;
     */
    @ApiModelProperty(value = "到期类型:100->指定时间;101->购买之日起计算;")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 过期天数，购买之日起，多少天过期
     */
    @ApiModelProperty(value = "过期天数，购买之日起，多少天过期")
    private Integer expiredDays;

    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    private Boolean productFlag;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    private Integer afterDaysValid;


}
