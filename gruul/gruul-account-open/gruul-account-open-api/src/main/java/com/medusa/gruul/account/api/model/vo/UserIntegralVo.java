package com.medusa.gruul.account.api.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 用户积分vo
 * @Date: Created in 17:57 2023/8/22
 */
@Data
public class UserIntegralVo {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;


    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    private BigDecimal integral;

    /**
     * 已消费积分
     */
    @ApiModelProperty(value = "已消费积分")
    private BigDecimal usedIntegral;

    /**
     * 当前积分
     */
    @ApiModelProperty(value = "当前积分")
    private BigDecimal currentIntegral;


}
