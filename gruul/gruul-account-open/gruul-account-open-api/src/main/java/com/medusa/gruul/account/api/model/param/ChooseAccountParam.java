package com.medusa.gruul.account.api.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:56 2025/4/2
 */
@Data
@ApiModel(value = "ChooseAccountParam 实体", description = "可选择的客户param")
public class ChooseAccountParam extends QueryParam {

    @ApiModelProperty(value = "客户昵称")
    private String nikeName;

    @ApiModelProperty(value = "客户电话号码")
    private String phone;


    @ApiModelProperty(value = "客户会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "客户ids")
    private List<String> accountIds;

}
