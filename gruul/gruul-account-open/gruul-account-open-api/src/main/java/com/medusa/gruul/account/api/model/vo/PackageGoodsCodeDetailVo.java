package com.medusa.gruul.account.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:37 2024/9/23
 */
@Data
@ApiModel(value = "PackageGoodsCodeDetailVo", description = "核销单明细实体类")
public class PackageGoodsCodeDetailVo {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String skuName;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    /**
     * 核销数量
     */
    @ApiModelProperty(value = "核销数量")
    private Integer verifyNumber;

    /**
     * 核销门店
     */
    @ApiModelProperty(value = "核销门店")
    private String departmentName;


    /**
     * 经手人
     */
    @ApiModelProperty(value = "经手人")
    private String empName;

    /**
     * 剩余数量
     */
    @ApiModelProperty(value = "剩余数量")
    private Integer canTimes;


    /**
     * 权益包展示开始时间
     */
    @ApiModelProperty(value = "权益包展示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageShowStartTime;

    /**
     * 权益包展示结束开始时间
     */
    @ApiModelProperty(value = "权益包展示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageShowEndTime;


    /**
     * 权益包使用期限开始时间
     */
    @ApiModelProperty(value = "权益包使用期限开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageStartTime;

    /**
     * 权益包使用期限结束时间
     */
    @ApiModelProperty(value = "权益包使用期限结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageEndTime;

    /**
     * 是否唯一
     */
    @ApiModelProperty(value = "是否唯一")
    private Integer mutexFlag;

    /**
     * 无期限
     */
    @ApiModelProperty(value = "无期限")
    private Integer notTime;

    /**
     * 不限次数
     */
    @ApiModelProperty(value = "不限次数")
    private Integer notTerm;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String accountName;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;

    /**
     * 实际核销商品
     */
    @ApiModelProperty(value = "实际核销商品")
    private String verifyGoodsName;

    @ApiModelProperty(value = "会员名称")
    private String userName;

    @ApiModelProperty(value = "手机号码")
    private String userMobile;

}
