package com.medusa.gruul.account.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 佣金类型枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CommissionTypeEnum {

    /**
     * 团队分佣
     */
    TEAM(100,"团队分佣"),
    /**
     * 奖励佣金
     */
    REWARD_COMMISSION(101,"奖励佣金"),

    /**
     * 奖励提成
     */
    REWARD_ROYALTY(102,"奖励提成"),
    /**
     * 佣金提现
     */
    CASH(200,"佣金提现")

    ;


    @EnumValue
    /**
     * 类型
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;


}
