package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:38 2024/11/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_member_level_rule")
@ApiModel(value = "MemberLevelRule对象", description = "会员等级规则表")
public class MemberLevelRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id")
    @TableField(value = "member_level_id")
    private String memberLevelId;

    /**
     * 消费额指标-开始
     */
    @ApiModelProperty(value = "消费额指标-开始")
    @TableField(value = "amount_start")
    private BigDecimal amountStart;

    /**
     * 消费额指标-结束
     */
    @ApiModelProperty(value = "消费额指标-结束")
    @TableField(value = "amount_end")
    private BigDecimal amountEnd;

    /**
     * 积分指标-开始
     */
    @ApiModelProperty(value = "积分指标-开始")
    @TableField(value = "integral_start")
    private BigDecimal integralStart;

    /**
     * 积分指标-结束
     */
    @ApiModelProperty(value = "积分指标-结束")
    @TableField(value = "integral_end")
    private BigDecimal integralEnd;

    /**
     * 消费额升级->0.不启用，1.启用
     */
    @ApiModelProperty(value = "消费额升级->0.不启用，1.启用")
    @TableField(value = "amount_flag")
    private Integer amountFlag;

    /**
     * 积分升级升级->0.不启用，1.启用
     */
    @ApiModelProperty(value = "积分升级升级->0.不启用，1.启用")
    @TableField(value = "integral_flag")
    private Integer integralFlag;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 会员消费额指标-开始
     */
    @ApiModelProperty(value = "会员消费额指标-开始")
    @TableField(value = "member_amount_start")
    private BigDecimal memberAmountStart;

    /**
     * 会员消费额指标-结束
     */
    @ApiModelProperty(value = "会员消费额指标-结束")
    @TableField(value = "member_amount_end")
    private BigDecimal memberAmountEnd;

    /**
     * 会员消费额升级->0.不启用，1.启用
     */
    @ApiModelProperty(value = "会员消费额升级->0.不启用，1.启用")
    @TableField(value = "member_amount_flag")
    private Integer memberAmountFlag;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @TableField(value = "main_id")
    private String mainId;

    /**
     * 会员复购
     */
    @ApiModelProperty(value = "会员复购")
    @TableField(value = "again_flag")
    private Integer againFlag;
}
