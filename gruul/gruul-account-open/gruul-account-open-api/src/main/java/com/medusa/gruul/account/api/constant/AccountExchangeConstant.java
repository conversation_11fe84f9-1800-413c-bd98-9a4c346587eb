package com.medusa.gruul.account.api.constant;

/**
 * <AUTHOR>
 * @description 用户模块交换机
 * @data: 2019/12/11
 */
public class AccountExchangeConstant {

    /**
     * 用户交换机
     */
    public static final String ACCOUNT_EXCHANGE = "gruul.account.exchange";

    /**
     *
     */
    public static final String ACCOUNT_PACKAGE_ORDER_EXCHANGE = "gruul.account.package.order";

    /**
     * 会员支付交换机
     */
    public static final String MEMBER_EXCHANGE = "payment.notify.exchange";

    /**
     * 会员佣金提现交换机
     */
    public static final String CASH_EXCHANGE = "commission.cash.exchange";

    /**
     * 发送优惠券交换机
     */
    public static final String SEND_COUPON_EXCHANGE = "send.coupon.exchange";

    /**
     * 用户佣金交换机
     */
    public static final String MINI_ACCOUNT_COMMISSION_CASH_EXCHANGE = "mini.account.commission.cash.exchange";
}
