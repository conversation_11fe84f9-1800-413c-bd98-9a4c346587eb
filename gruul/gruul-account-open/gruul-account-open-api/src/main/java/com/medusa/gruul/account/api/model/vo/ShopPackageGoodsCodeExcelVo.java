package com.medusa.gruul.account.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:47 2024/9/11
 */
@Data
@ApiModel(value = "ShopPackageGoodsCodeExcelVo", description = "商家权益包商品核销列表实体类")
public class ShopPackageGoodsCodeExcelVo {

    @ApiModelProperty(value = "序号")
    private String index;
    /**
     * 核销单号
     */
    @ApiModelProperty(value = "核销单号")
    private String verifyNo;
    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名")
    private String userName;
    /**
     * 会员手机号
     */
    @ApiModelProperty(value = "会员手机号")
    private String userMobile;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;


    /**
     * 权益包商品名称
     */
    @ApiModelProperty(value = "核销内容")
    private String productName;


    /**
     * 核销数量  ?
     */
    @ApiModelProperty(value = "核销数量")
    private Integer productNumber;

    /**
     * 剩余未核销
     */
    @ApiModelProperty(value = "剩余未核销")
    private Integer goodsNum;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;
    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String verifyTime;

    /**
     * 经手人
     */
    @ApiModelProperty(value = "经手人")
    private String accountName;



}
