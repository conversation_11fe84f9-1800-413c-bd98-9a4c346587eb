package com.medusa.gruul.account.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:37 2024/9/23
 */
@Data
@ApiModel(value = "PackageGoodsCodeDetailVo", description = "核销单明细实体类")
public class PackageGoodsCodeDetailExcelVo {


    @ApiModelProperty(value = "序号")
    private Long index;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;
    /**
     * 实际核销商品
     */
    @ApiModelProperty(value = "实际核销商品")
    private String verifyGoodsName;
    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String skuName;


    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;


    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String verifyTime;

    /**
     * 核销数量
     */
    @ApiModelProperty(value = "核销数量")
    private Integer verifyNumber;



    /**
     * 剩余数量
     */
    @ApiModelProperty(value = "剩余数量")
    private Integer canTimes;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "核销名称")
    private String storeFrontName;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "经手人")
    private String accountName;

    @ApiModelProperty(value = "权益包展示时间")
    private String packageShowTime;

    @ApiModelProperty(value = "使用期限")
    private String packageTime;


    /**
     * 经手人
     */
    @ApiModelProperty(value = "经手人")
    private String empName;

    /** 0显示为"否"，1显示为"是"
     * 是否唯一
     */
    @ApiModelProperty(value = "互斥商品")
    private String mutexFlag;

    /**
     * 无期限 0显示为"否"，1显示为"是"
     */
    @ApiModelProperty(value = "无期限")
    private String notTime;

}
