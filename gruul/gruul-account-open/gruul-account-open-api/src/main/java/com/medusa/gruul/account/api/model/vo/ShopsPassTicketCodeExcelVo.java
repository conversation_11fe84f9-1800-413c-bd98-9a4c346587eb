package com.medusa.gruul.account.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 22:04 2023/10/10
 */
@Data
@ApiModel(value = "ShopsPassTicketCodeExcelVo", description = "商家核销列表导出实体类")
public class ShopsPassTicketCodeExcelVo {

    /**
     * 会员手机号
     */
    @ApiModelProperty(value = "会员手机号")
    private String userMobile;

    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名")
    private String nickName;

    /**
     * 通惠证名称
     */
    @ApiModelProperty(value = "通惠证名称")
    private String passTicketName;

    /**
     * 核销码
     */
    @ApiModelProperty(value = "核销码")
    private String verifyCode;

    /**
     * 商家核销号
     */
    @ApiModelProperty(value = "商家核销号")
    private String verifyUserMobile;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;



}
