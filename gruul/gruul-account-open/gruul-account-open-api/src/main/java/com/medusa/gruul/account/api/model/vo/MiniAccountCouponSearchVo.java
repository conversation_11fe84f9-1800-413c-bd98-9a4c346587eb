package com.medusa.gruul.account.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:15 2024/8/28
 */
@Data
@ApiModel(value = "MiniAccountCouponSearchVo", description = "平台查看优惠券视图")
public class MiniAccountCouponSearchVo {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券面额
     */
    @ApiModelProperty(value = "优惠券面额")
    private BigDecimal promotion;

    /**
     * 使用人
     */
    @ApiModelProperty(value = "使用人")
    private String useName;

    /**
     * 使用店面
     */
    @ApiModelProperty(value = "使用店面")
    private String useShopName;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private Long orderId;

    /**
     * 使用时间
     */
    @ApiModelProperty(value = "使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date useTime;



}
