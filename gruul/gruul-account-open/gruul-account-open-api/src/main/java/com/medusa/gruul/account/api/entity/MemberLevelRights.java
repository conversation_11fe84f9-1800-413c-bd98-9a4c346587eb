package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date 2022/2/23 17:28
 *
 */

@Data

@Accessors(chain = true)
@TableName("t_member_level_rights")
@ApiModel(value = "MiniAccountMemberLevelRights对象", description = "会员等级权益表")
public class MemberLevelRights {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "权益名称")
    private String name;

    @ApiModelProperty(value = "权益说明")
    private String powerExplain;

    @ApiModelProperty(value = "是否启用")
    private Integer enable;

    @ApiModelProperty(value = "图标")
    private String icon;


}
