package com.medusa.gruul.account.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:43 2024/8/27
 */
@Data
@ApiModel(value = "MiniAccountCouponVo", description = "用户拥有的优惠券视图类")
public class MiniAccountCouponVo {
    @ApiModelProperty(value = "用户优惠券记录id")
    private Long miniAccountCouponId;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private Long couPonId;


    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 购买单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    private Integer useableTimes;

    /**
     * 票类型:100->满减;101->折扣;
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    private Integer ticketType;

    /**
     * 满额（存满100减20的100值）
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期类型:100->指定时间;101->购买之日起计算;
     */
    @ApiModelProperty(value = "到期类型:100->指定时间;101->购买之日起计算;")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 过期天数，购买之日起，多少天过期
     */
    @ApiModelProperty(value = "过期天数，购买之日起，多少天过期")
    private Integer expiredDays;

    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    private Boolean productFlag;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    private Integer afterDaysValid;

    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券")
    private Integer couponType;

}
