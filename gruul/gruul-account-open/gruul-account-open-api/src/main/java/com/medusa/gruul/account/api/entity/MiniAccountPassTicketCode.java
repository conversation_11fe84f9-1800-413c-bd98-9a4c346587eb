package com.medusa.gruul.account.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 小程序会员-通行票验证码记录
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_mini_account_pass_ticket_code")
@ApiModel(value = "MiniAccountPassTicketCode对象", description = "小程序会员-通行票验证码记录")
public class MiniAccountPassTicketCode extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name",fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id",fill = FieldFill.UPDATE)
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name",fill = FieldFill.UPDATE)
    private String lastModifyUserName;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 通行票id
     */
    @ApiModelProperty(value = "通行票id")
    @TableField("pass_ticket_id")
    private Long passTicketId;

    /**
     * 用户通行票记录id
     */
    @ApiModelProperty(value = "用户通行票id")
    @TableField("mini_account_pass_ticket_id")
    private Long miniAccountPassTicketId;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    @TableField("shop_id")
    private String shopId;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    @TableField(value = "verify_code")
    private String verifyCode;

    /**
     * 状态:100->未用;101->已用;200->已失效
     */
    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    @TableField("status")
    private Integer status;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "核销时间")
    @TableField("verify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    /**
     * 核销人id
     */
    @ApiModelProperty(value = "核销人id")
    @TableField(value = "verify_user_id")
    private Long verifyUserId;

}
