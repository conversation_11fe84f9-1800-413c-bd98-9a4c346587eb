package com.medusa.gruul.shops.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:48 2025/4/1
 */
@Data
@ApiModel(value = "优惠券-商品dto")
public class ShopCouponProductDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String productId;

    /**
     * 规格id
     */
    @ApiModelProperty(value = "规格id")
    private String skuId;

}
