package com.medusa.gruul.shops.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;

import com.medusa.gruul.shops.api.entity.Dict;
import com.medusa.gruul.shops.service.IDictService;
import lombok.extern.slf4j.Slf4j;
import com.medusa.gruul.common.core.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 枚举
 * @Author: qsx
 * @Date:   2022-03-09
 * @Version: V1.0
 */
@Slf4j
@Api(tags="枚举")
@RestController
@RequestMapping("/dict/dict")
public class DictController  {
	@Autowired
	private IDictService dictService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dict
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	/**@AutoLog(value = "枚举-分页列表查询")*/
	@ApiOperation(value="枚举-分页列表查询", notes="枚举-分页列表查询")
	@PostMapping(value = "/list")
	public Result<?> queryPageList(Dict dict,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
	    return Result.ok();
	}
	
	/**
	 * 添加
	 *
	 * @param dict
	 * @return
	 */
	/**@AutoLog(value = "枚举-添加")*/
	@ApiOperation(value="枚举-添加", notes="枚举-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody Dict dict) {
		dictService.save(dict);
		return Result.ok("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param dict
	 * @return
	 */
	/**@AutoLog(value = "枚举-编辑")*/
	@ApiOperation(value="枚举-编辑", notes="枚举-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody Dict dict) {
		dictService.updateById(dict);
		return Result.ok("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "枚举-通过id删除")*/
	@ApiOperation(value="枚举-通过id删除", notes="枚举-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		dictService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	/**@AutoLog(value = "枚举-批量删除")*/
	@ApiOperation(value="枚举-批量删除", notes="枚举-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dictService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "枚举-通过id查询")*/
	@ApiOperation(value="枚举-通过id查询", notes="枚举-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		Dict dict = dictService.getById(id);
		return Result.ok(dict);
	}


}
