package com.medusa.gruul.shops.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.model.TicketVo;
import com.medusa.gruul.shops.mapper.ShopPassTicketMapper;
import com.medusa.gruul.shops.mapper.ShopPassTicketRuleMapper;
import com.medusa.gruul.shops.model.dto.ShopPassTicketDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketPartnerDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketProductDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketRuleDto;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;
import com.medusa.gruul.shops.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/09/20
 */
@Service(value = "shopPassTicketRuleServiceImpl")
public class ShopPassTicketRuleServiceImpl extends ServiceImpl<ShopPassTicketRuleMapper, ShopPassTicketRule> implements IShopPassTicketRuleService {


    /**
     * 保存记录
     * @param shopPassTicketRuleDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopPassTicketRule save(ShopPassTicketRuleDto shopPassTicketRuleDto) {
        ShopPassTicketRule shopPassTicketRule;
        // 查询数据库有无记录
        List<ShopPassTicketRule> dbList = this.list();
        if(CollectionUtil.isNotEmpty(dbList)){
            shopPassTicketRule = dbList.get(0);
            shopPassTicketRule.setRule(shopPassTicketRuleDto.getRule());
            //保存主表信息
            this.updateById(shopPassTicketRule);
        }else{
            //主表id
            shopPassTicketRule = new ShopPassTicketRule();
            Long id= IDUtil.getId();
            shopPassTicketRule.setId(id);
            shopPassTicketRule.setRule(shopPassTicketRuleDto.getRule());
            //保存主表信息
            this.save(shopPassTicketRule);
        }

        return shopPassTicketRule;
    }

    /**
     * 查询一条记录
     * @return
     */
    @Override
    public ShopPassTicketRule queryOne() {
        List<ShopPassTicketRule> ruleList = this.list();
        ShopPassTicketRule rule = CollectionUtil.isEmpty(ruleList) ? null : ruleList.get(0);
        return rule;
    }

}
