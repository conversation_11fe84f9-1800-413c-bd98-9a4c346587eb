package com.medusa.gruul.shops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.shops.api.entity.ShopsSettled;
import com.medusa.gruul.shops.model.dto.ShopsSettledDto;
import com.medusa.gruul.common.dto.ApproveDataParam;
import com.medusa.gruul.shops.model.param.ShopsSettledParam;
import com.medusa.gruul.shops.model.vo.ShopsSettledVo;

/**
 * @Author: plh
 * @Description: 商家入驻服务类
 * @Date: Created in 16:42 2023/8/30
 */
public interface IShopsSettledService extends IService<ShopsSettled> {

    /**
     * 临时保存商家入驻信息
     * @param dto
     * @return
     */
    ShopsSettled add(ShopsSettledDto dto);

    /**
     * 提交审核商家入驻信息
     * @param dto
     * @return
     */
    ShopsSettled submit(ShopsSettledDto dto);


    /**
     * 审核数据
     * @param approveDataParam
     */
    void approve(ApproveDataParam approveDataParam);

    /**
     * 分页查询商家入驻信息
     * @param shopsSettledParam
     * @return
     */
    PageUtils<ShopsSettledVo>getShopsSettledVo(ShopsSettledParam shopsSettledParam);

    /**
     * 根据店铺用户id查询商家入驻信息
     * @return
     */
    ShopsSettledVo getShopsSettledVoByShopUserId();

}
