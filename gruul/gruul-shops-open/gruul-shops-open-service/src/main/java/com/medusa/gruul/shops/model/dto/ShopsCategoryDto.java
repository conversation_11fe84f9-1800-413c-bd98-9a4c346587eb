package com.medusa.gruul.shops.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.goods.api.entity.ShowCategory;
import com.medusa.gruul.shops.api.entity.ShopsCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: 商家一级分类dto
 * @Date: Created in 18:18 2023/9/12
 */
@Data
@ApiModel(value = "商家一级分类dto")
public class ShopsCategoryDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称",required = true)
    @NotNull(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "分类级别：0->1级；1->2级",required = true)
    @NotNull
    private Integer level;

    @ApiModelProperty(value = "上级分类的编号：0表示一级分类",required = true)
    @NotNull
    private Long parentId;

    @ApiModelProperty(value = "分类排序")
    private Integer sort;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "下级分类")
    private List<ShopsCategorySecondDto> list;

    public ShopsCategory coverShowCategory() {
        ShopsCategory shopsCategory = new ShopsCategory();
        BeanUtil.copyProperties(this, shopsCategory);
        return shopsCategory;
    }
}
