package com.medusa.gruul.shops.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.shops.api.entity.ShopCommissionRule;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.model.dto.ShopCommissionRuleDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketDto;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;

/**
 *佣金规则服务接口
 * <AUTHOR>
 */
public interface IShopCommissionRuleService extends IService<ShopCommissionRule> {


    /**
     * 添加记录
     * @param shopCommissionRuleDto
     * @return
     */
    ShopCommissionRule add(ShopCommissionRuleDto shopCommissionRuleDto);

    /**
     * 编辑记录
     * @param shopCommissionRuleDto
     * @return
     */
    ShopCommissionRule edit(ShopCommissionRuleDto shopCommissionRuleDto);

    /**
     * 保存记录
     * @param shopCommissionRuleDto
     * @return
     */
    ShopCommissionRule save(ShopCommissionRuleDto shopCommissionRuleDto);

    /**
     * 查询最新记录
     * @return
     */
    ShopCommissionRule getNewest();

    /**
     * 查询启用最新记录
     * @return
     */
    ShopCommissionRule getOpenNewest();

}
