package com.medusa.gruul.shops.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.PlatformRenovationPageAssembly;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePage;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePlugin;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.PlatformRenovationTemplateAllVo;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.model.RenovationTemplateDto;
import com.medusa.gruul.shops.mapper.ShopsRenovationTemMapper;
import com.medusa.gruul.shops.mapper.ShopsRenovationTemPageMapper;
import com.medusa.gruul.shops.model.param.ShopsRenovationAssemblyParam;
import com.medusa.gruul.shops.model.param.ShopsRenovationPageParam;
import com.medusa.gruul.shops.model.param.ShopsRenovationPluginParam;
import com.medusa.gruul.shops.model.param.ShopsRenovationTemplateParam;
import com.medusa.gruul.shops.model.vo.ShopsRenovationAssemblyVo;
import com.medusa.gruul.shops.model.vo.ShopsRenovationPageVo;
import com.medusa.gruul.shops.model.vo.ShopsRenovationTemplateVo;
import com.medusa.gruul.shops.properties.GlobalConstant;
import com.medusa.gruul.shops.properties.ShopsRenovationRedisTools;
import com.medusa.gruul.shops.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> by zq
 * @date created in 2020/01/14
 */
@Service(value = "shopsRenovationTemServiceImpl")
public class ShopsRenovationTemServiceImpl extends ServiceImpl<ShopsRenovationTemMapper, ShopsRenovationTemplate> implements ShopsRenovationTemService {


    @Autowired
    private ShopsRenovationTemPageService shopsRenovationTemPageService;

    @Autowired
    private ShopsRenovationPluginService shopsRenovationPluginService;

    @Autowired
    private ShopsRenovationTemPageAssService shopsRenovationTemPageAssService;

    @Autowired
    private ShopsPartnerService shopsPartnerService;

    @Autowired
    private ShopsRenovationTemPageMapper shopsRenovationTemPageMapper;

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;


    /**
     * 新增商铺装修模板
     *
     * @param param
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addTemplate(ShopsRenovationTemplateParam param) {
        ShopsRenovationTemplate template = new ShopsRenovationTemplate();
        BeanUtil.copyProperties(param, template);
        if (param.getId() == null) {
            if (this.save(template)) {
                return Result.ok(template);
            }
            return Result.failed();
        }

        /** 下线之前的模板 */
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(ShopsRenovationTemplate::getOnlineStatus, GlobalConstant.STRING_ONE)
                .eq(ShopsRenovationTemplate::getIsDevTemplate, GlobalConstant.STRING_ZERO)
                .set(ShopsRenovationTemplate::getOnlineStatus, GlobalConstant.STRING_ZERO)
                .update();

        /** 更新并上线当前模板 */
        if (this.updateById(template)) {

            ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
            redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_PAGE_PLUGIN);
            redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_TEMPLATE_KEY);

            return Result.ok(template);
        } else {
            log.error(String.format("update fail! %s ", template));
            throw new ServiceException("update fail!", SystemCode.DATA_UPDATE_FAILED_CODE);
        }
    }


    /**
     * 删除商铺装修模板 by ids
     *
     * @param ids
     * @return Result
     */
    @Override
    public Result delTemplate(String ids) {
        if (StringUtils.isBlank(ids)) {
            throw new ServiceException(SystemCode.PARAM_MISS.getMsg());
        }
        Arrays.asList(ids.split(GlobalConstant.STRING_COMMA)).stream().forEach(id -> {
            boolean update = new LambdaUpdateChainWrapper<>(this.baseMapper)
                    .eq(ShopsRenovationTemplate::getId, id)
                    .eq(ShopsRenovationTemplate::getOnlineStatus, GlobalConstant.STRING_ZERO)
                    .set(ShopsRenovationTemplate::getDeleted, GlobalConstant.STRING_ONE)
                    .update();
            if (!update) {
                throw new ServiceException("delete fail!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
        });

        ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_PAGE_PLUGIN);
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_TEMPLATE_KEY);

        return Result.ok();
    }


    /**
     * 获取商铺装修模板list
     *
     * @param param
     * @return Result
     */
    @Override
    public Result listTemplate(ShopsRenovationTemplateParam param) {
        List list = this.baseMapper.listTemplate(param);
        if (ArrayUtil.isEmpty(list) && GlobalConstant.STRING_ONE.equals(param.getIsAll())) {
            list = this.baseMapper.listDefTemplate(param);
        }
        return Result.ok(list);
    }


    /**
     * 删除默认装修模板 by ids
     *
     * @param ids
     * @return Result
     */
    @Override
    public Result delDefTemplate(String ids) {
        if (StringUtils.isBlank(ids)) {
            throw new ServiceException(SystemCode.PARAM_MISS.getMsg());
        }
        Arrays.asList(ids.split(GlobalConstant.STRING_COMMA)).stream().forEach(id -> {
            boolean update = new LambdaUpdateChainWrapper<>(this.baseMapper)
                    .eq(ShopsRenovationTemplate::getId, id)
                    .eq(ShopsRenovationTemplate::getOnlineStatus, GlobalConstant.STRING_ONE)
                    .eq(ShopsRenovationTemplate::getIsDevTemplate, GlobalConstant.STRING_ONE)
                    .set(ShopsRenovationTemplate::getDeleted, GlobalConstant.STRING_ONE)
                    .update();
            if (!update) {
                throw new ServiceException("delete del template fail!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
        });

        ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_PAGE_PLUGIN);
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_TEMPLATE_KEY);
        return Result.ok();
    }


    /**
     * 获取默认装修模板list
     *
     * @param param
     * @return Result
     */
    @Override
    public Result listDefTemplate(ShopsRenovationTemplateParam param) {
        return Result.ok(this.baseMapper.listDefTemplate(param));
    }


    /**
     * 复制模板 by id
     *
     * @param id
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result copyTemplateById(Long id) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            throw new ServiceException("获取请求参数失败 !");
        }

        HttpServletRequest request = requestAttributes.getRequest();
        final String tenantId = request.getHeader(GlobalConstant.STRING_TENANT_ID);
        final String shopId = request.getHeader(GlobalConstant.STRING_SHOP_ID);

        if (StringUtils.isBlank(shopId) || StringUtils.isBlank(tenantId)) {
            throw new ServiceException("租户店铺信息获取失败!");
        }
        ShopsRenovationTemplate byId = this.getById(id);
        if (null == byId) {
            throw new ServiceException(String.format("template is empty ! id : %s", id));
        }

        /** 根据模板id copy 模板, 新生成数据 */
        ShopsRenovationTemplate shopsRenovationTemplate = byId.setId(null);
        byId.setShopId(shopId);
        byId.setTenantId(tenantId);
        byId.setIsDevTemplate(GlobalConstant.STRING_ZERO);
        byId.setOnlineStatus(GlobalConstant.STRING_ZERO);
        if (!this.save(byId)) {
            throw new ServiceException(String.format("copy template fail ! id : %s", id));
        }

        /** 根据模板id 获取关联控件, 执行复制保存控件至新模板 */
        List<ShopsRenovationPlugin> plugins =
                shopsRenovationPluginService.getBaseMapper()
                        .selectList(new QueryWrapper<ShopsRenovationPlugin>().eq("template_id", id));
        plugins.stream().forEach(entity -> {
            entity.setId(null);
            entity.setShopId(shopId);
            entity.setTenantId(tenantId);
            entity.setTemplateId(shopsRenovationTemplate.getId());
            if (!shopsRenovationPluginService.save(entity)) {
                throw new ServiceException(String.format("copy template plugins fail ! id : %s, plugin : %s", id, entity));
            }
        });

        /** 根据模板id 获取关联页面, 执行复制保存页面至新模板 */
        List<ShopsRenovationPage> pages = shopsRenovationTemPageService.getBaseMapper()
                .selectList(new QueryWrapper<ShopsRenovationPage>().eq("template_id", id));
        for (int i = 0; i < pages.size(); i++) {
            ShopsRenovationPage entity = pages.get(i);
            if (i == 0) {
                entity.setIsDef(GlobalConstant.STRING_ONE);
            } else {
                entity.setIsDef(GlobalConstant.STRING_ZERO);
            }
            Long pageId = entity.getId();
            entity.setId(null);
            entity.setModelId("0");
            entity.setShopId(shopId);
            entity.setTenantId(tenantId);
            entity.setTemplateId(shopsRenovationTemplate.getId());
            if (!shopsRenovationTemPageService.save(entity)) {
                throw new ServiceException(String.format("copy template page fail ! id : %s, page : %s", id, entity));
            }
            /** 根据页面id 获取关联插件, 执行复制保存插件至新页面 */
            List<ShopsRenovationAssembly> assemblies = shopsRenovationTemPageAssService.listTemplatePageAssemblyByPageId(pageId);
            assemblies.stream().forEach(assembliesEntity -> {
                assembliesEntity.setId(null);
                assembliesEntity.setShopId(shopId);
                assembliesEntity.setTenantId(tenantId);
                assembliesEntity.setPageId(entity.getId());
                if (!shopsRenovationTemPageAssService.save(assembliesEntity)) {
                    throw new ServiceException(
                            String.format("copy template page assemblies entity fail ! id : %s, assembliesEntity : %s", id, assembliesEntity));
                }
            });

        }

        ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_PAGE_PLUGIN);
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_TEMPLATE_KEY);
        return Result.ok(shopsRenovationTemplate);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void init(RenovationTemplateDto templateSettingDto) {
        ShopsRenovationTemplate template = new ShopsRenovationTemplate();
        BeanUtil.copyProperties(templateSettingDto, template, "shopId", "tenantId");
        if (StringUtils.isNotBlank(template.getIsDevTemplate())) {
            log.debug("isDevStatus in " + template.getIsDevTemplate());
            template.setIsDevTemplate(GlobalConstant.STRING_ZERO);
        }

        List<ShopsRenovationTemplate> list = this.getBaseMapper().selectList(new QueryWrapper<ShopsRenovationTemplate>().eq("name", template.getName()));
        log.debug(String.format("根据name : %s, 店铺id : %s, 租户id : %s ,获取历史数据 : %s",
                template.getName(), template.getShopId(), template.getTenantId(), list.toString()));
        if (!list.isEmpty()) {
            return;
        }

        this.save(template);
        Long indexPageId = 0L;
        int i = 0;
        String shopRenovationPageId = null;
        for (RenovationTemplateDto.Pages pageVo : templateSettingDto.getPages()) {
            ShopsRenovationPage page = new ShopsRenovationPage();
            BeanUtil.copyProperties(pageVo, page);
            page.setTemplateId(template.getId());
            shopsRenovationTemPageService.save(page);
            shopRenovationPageId = page.getId().toString();
            for (RenovationTemplateDto.Pages.Assemblies assemblyVo : pageVo.getAssemblies()) {
                ShopsRenovationAssembly assembly = new ShopsRenovationAssembly();
                assembly.setPageId(page.getId());
                assembly.setProperties(assemblyVo.getProperties());
                shopsRenovationTemPageAssService.save(assembly);
            }
            if (i == 0) {
                indexPageId = page.getId();
            }
            i++;
        }
        for (RenovationTemplateDto.Plugins pluginVO : templateSettingDto.getPlugins()) {
            ShopsRenovationPlugin plugin = new ShopsRenovationPlugin();
            if (pluginVO.getPluginProperties().contains("底部导航")) {
                pluginVO.setPluginProperties(pluginVO.getPluginProperties().replace("306", indexPageId.toString()));
                //471替换为t_shops_renovation_page 表的id
                pluginVO.getPluginProperties().replace("471", shopRenovationPageId == null ? "471" : shopRenovationPageId);
            }
            BeanUtil.copyProperties(pluginVO, plugin);
            plugin.setTemplateId(template.getId());
            shopsRenovationPluginService.save(plugin);
        }

    }


    /**
     * 小程序装修默认聚合接口
     *
     * @return Result
     */
    @Override
    public Result templateDefOne() {
        ShopsRenovationTemplateVo vo = null;
        ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            throw new ServiceException("获取请求参数失败 !");
        }
        HttpServletRequest request = requestAttributes.getRequest();
        final String tenantId = request.getHeader(GlobalConstant.STRING_TENANT_ID);
        ShopsPartner shopsPartner = shopsPartnerService.getShopsPartnerMain();
        if(null == shopsPartner){
            throw new ServiceException("商家主店铺不能为空 !");
        }
        final String oldShopId = ShopContextHolder.getShopId();
        final String shopId = shopsPartner.getShopId();
        ShopContextHolder.setShopId(shopId);
        String json = redisTools.get(tenantId + ":" + shopId + ":" + GlobalConstant.STRING_SHOP_TEMPLATE_KEY);
        if (StringUtils.isBlank(json)) {
            vo = innerHandlerGetCache();
            redisTools.set(tenantId + ":" + shopId + ":" + GlobalConstant.STRING_SHOP_TEMPLATE_KEY, JSONObject.toJSONString(vo));
        } else {
            vo = JSONObject.parseObject(json, ShopsRenovationTemplateVo.class);
        }
        ShopContextHolder.setShopId(oldShopId);
        return Result.ok(vo);
    }

    /**
     * 复制平台装修模板生成店铺装修模板
     * @param platformTemplateId 平台装修模板id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ShopsRenovationTemplate> copyByPlatformTemplate(Long platformTemplateId) {
        Result<PlatformRenovationTemplateAllVo> platformAllResult = remoteMiniInfoService.getRenovationTemplateAllInfoById(platformTemplateId);
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            throw new ServiceException("获取请求参数失败 !");
        }

        HttpServletRequest request = requestAttributes.getRequest();
        final String tenantId = request.getHeader(GlobalConstant.STRING_TENANT_ID);
        final String shopId = request.getHeader(GlobalConstant.STRING_SHOP_ID);

        if (StringUtils.isBlank(shopId) || StringUtils.isBlank(tenantId)) {
            throw new ServiceException("租户店铺信息获取失败!");
        }
        if(platformAllResult.getCode() != CommonConstants.SUCCESS.intValue()){
            throw new ServiceException("平台模板信息获取失败!");
        }
        PlatformRenovationTemplateAllVo allVo = platformAllResult.getData();
        List<PlatformRenovationTemplatePlugin> pluginList = allVo.getPluginList();
        List<PlatformRenovationTemplatePage> pageList = allVo.getPageList();
        List<PlatformRenovationPageAssembly> assemblyList = allVo.getAssemblyList();
        // key:pageId
        Map<Long, List<PlatformRenovationPageAssembly>> assemblyMap = assemblyList.stream().collect(Collectors.groupingBy(PlatformRenovationPageAssembly::getPageId));
        // 先将原有数据删除
        this.remove(new QueryWrapper<>());
        this.shopsRenovationPluginService.remove(new QueryWrapper<>());
        this.shopsRenovationTemPageService.remove(new QueryWrapper<>());
        this.shopsRenovationTemPageAssService.remove(new QueryWrapper<>());
        // 复制平台模板数据到店铺模板
        ShopsRenovationTemplate shopsRenovationTemplate = new ShopsRenovationTemplate();
        BeanUtil.copyProperties(allVo, shopsRenovationTemplate);
        shopsRenovationTemplate.setId(null);
        shopsRenovationTemplate.setIsDevTemplate(GlobalConstant.STRING_ZERO);
        shopsRenovationTemplate.setOnlineStatus(GlobalConstant.STRING_ONE);
        if (!this.save(shopsRenovationTemplate)) {
            throw new ServiceException("保存模板失败！");
        }

        List<ShopsRenovationPlugin> shopPluginList = new ArrayList<>();
        pluginList.stream().forEach(entity -> {
            ShopsRenovationPlugin plugin = new ShopsRenovationPlugin();
            BeanUtil.copyProperties(entity, plugin);
            plugin.setId(null);
            plugin.setTemplateId(shopsRenovationTemplate.getId());
            shopPluginList.add(plugin);
        });
        if (!this.shopsRenovationPluginService.saveBatch(shopPluginList)){
            throw new ServiceException("保存控件失败！");
        }

        List<ShopsRenovationPage> shopPageList = new ArrayList<>();
        List<ShopsRenovationAssembly> shopAssemblyList = new ArrayList<>();
        pageList.stream().forEach(entity -> {
            ShopsRenovationPage page = new ShopsRenovationPage();
            BeanUtil.copyProperties(entity, page);
            Long pageId = IDUtil.getId();
            page.setId(pageId);
            page.setTemplateId(shopsRenovationTemplate.getId());
            shopPageList.add(page);
            //查询页面对应的组件属性记录
            List<PlatformRenovationPageAssembly> tempAssemblyList = assemblyMap.get(entity.getId());
            tempAssemblyList.stream().forEach(t -> {
                ShopsRenovationAssembly shopAssembly = new ShopsRenovationAssembly();
                BeanUtil.copyProperties(t, shopAssembly);
                shopAssembly.setId(null);
                shopAssembly.setPageId(pageId);
                shopAssemblyList.add(shopAssembly);
            });
        });

        if (!this.shopsRenovationTemPageService.saveBatch(shopPageList)){
            throw new ServiceException("保存页面失败！");
        }

        if (!this.shopsRenovationTemPageAssService.saveBatch(shopAssemblyList)){
            throw new ServiceException("保存页面组件失败！");
        }

        ShopsRenovationRedisTools redisTools = new ShopsRenovationRedisTools();
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_PAGE_PLUGIN);
        redisTools.innerRemoveCache(GlobalConstant.STRING_SHOP_TEMPLATE_KEY);
        return Result.ok(shopsRenovationTemplate);
    }


    private ShopsRenovationTemplateVo innerHandlerGetCache() {
        ShopsRenovationTemplateParam param = new ShopsRenovationTemplateParam();
        param.setOnlineStatus(GlobalConstant.STRING_ONE);
        param.setIsAll(GlobalConstant.STRING_ONE);
        List<ShopsRenovationTemplateVo> list = this.baseMapper.listTemplate(param);

        if (CollectionUtil.isEmpty(list)) {
            log.error("获取默认上线模板失败.");
            throw new ServiceException("默认模板为空，请先设置装修模板！");
        }

        ShopsRenovationTemplateVo vo = list.get(0);
        ShopsRenovationPluginParam pluginParam = new ShopsRenovationPluginParam();
        pluginParam.setTemplateId(vo.getId());
        Result<List> result = shopsRenovationPluginService.listPlugin(pluginParam);
        vo.setPlugins(result.getData());

        ShopsRenovationPageParam pageParam = new ShopsRenovationPageParam();
        pageParam.setTemplateId(vo.getId());
        Integer integer = shopsRenovationTemPageMapper.selectCount(new QueryWrapper<ShopsRenovationPage>().eq("template_id", vo.getId()));
        pageParam.setSize(integer);
        Result<PageUtils<ShopsRenovationPageVo>> listResult = shopsRenovationTemPageService.listTemplatePage(pageParam);
        PageUtils<ShopsRenovationPageVo> data1 = listResult.getData();
        List<ShopsRenovationPageVo> data = new ArrayList<>();
        if (null != data1 && CollectionUtil.isNotEmpty(data1.getList())) {
            data = data1.getList();
        }
        vo.setPages(data);

        data.parallelStream().forEach(entity -> {
            ShopsRenovationAssemblyParam assemblyParam = new ShopsRenovationAssemblyParam();
            assemblyParam.setPageId(entity.getId());
            Result<List<ShopsRenovationAssemblyVo>> assemblyVos = shopsRenovationTemPageAssService.listTemplatePageAssembly(assemblyParam);
            entity.setAssemblyVos(assemblyVos.getData());
        });

        return vo;
    }

}
//