package com.medusa.gruul.shops.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import com.medusa.gruul.shops.api.entity.ShopCouponPartner;
import com.medusa.gruul.shops.api.entity.ShopCouponProduct;
import com.medusa.gruul.shops.api.entity.ShopPassTicketPartner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:03 2024/8/23
 */
@Data
@ApiModel(value = "优惠券Vo")
public class ShopCouponVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 票名
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 满额
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;

    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    private Integer approvedStatus;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;


    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    private Date displayStartTime;

    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    private Date displayEndTime;


    /**
     * 新人券，0否1是
     */
    @ApiModelProperty(value = "新人券，0否1是")
    private Integer newPeopleFlag;

    /**
     * 领取次数
     */
    @ApiModelProperty(value = "领取次数")
    private Integer receiveTimes;

    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券")
    private Integer couponType;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    private Integer useDate;

    /**商家明细*/
    @ApiModelProperty(value = "商家明细")
    private List<ShopCouponPartner> partnerList;

    /**
     * 商品明细
     */
    @ApiModelProperty(value = "商品明细")
    private List<ShopCouponProductVo> products;

    /**
     * 品类明细
     */
    @ApiModelProperty(value = "品类明细")
    private List<ShopCouponCategoryVo> categorys;

    /**
     * 优惠券指定发券对象明细
     */
    @ApiModelProperty(value = "优惠券指定发券对象明细")
    private List<ShopCouponAccountVo> accounts;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id")
    private List<String>memberLevels;

    /**
     * 指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;
     */
    @ApiModelProperty(value = "指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;")
    private Integer grantType;
}
