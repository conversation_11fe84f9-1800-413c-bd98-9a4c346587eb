package com.medusa.gruul.shops.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 商家入驻 vo"
 * @Date: Created in 17:20 2023/8/30
 */
@Data
@ApiModel(value = "商家入驻 vo", description = "商家入驻 vo")
public class ShopsSettledVo {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String shopName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /**
     *省名
     */
    @ApiModelProperty(value = "省名")
    private String provinceName;

    /**
     * 市名
     */
    @ApiModelProperty(value = "市名")
    private String cityName;

    /**
     * 区域名
     */
    @ApiModelProperty(value = "区域名")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    private String mapX;

    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    private String mapY;


    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    private String cardIdUp;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")

    private String cardIdDown;
    /**
     * 审批状态：0.临时保存；100.待审核；101.审核通过；200.驳回
     */
    @ApiModelProperty(value = "审批状态：0.临时保存；100.待审核；101.审核通过；200.驳回")
    private String approvalStatus;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private String approvalReason;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;
}
