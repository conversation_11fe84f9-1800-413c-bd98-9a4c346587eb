package com.medusa.gruul.shops.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.conf.ShopsRedis;
import com.medusa.gruul.shops.api.constant.ShopsRedisKey;
import com.medusa.gruul.shops.api.enums.ExpiredTypeEnum;
import com.medusa.gruul.shops.mapper.ShopPassTicketMapper;
import com.medusa.gruul.shops.model.dto.ShopPassTicketDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketPartnerDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketProductDto;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;
import com.medusa.gruul.shops.api.model.TicketVo;
import com.medusa.gruul.shops.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/08/21
 */
@Service(value = "shopPassTicketServiceImpl")
public class ShopPassTicketServiceImpl extends ServiceImpl<ShopPassTicketMapper, ShopPassTicket> implements IShopPassTicketService {

    @Autowired
    private IShopPassTicketPartnerService shopPassTicketPartnerService;

    @Autowired
    private IShopPassTicketProductService shopPassTicketProductService;

    @Autowired
    private ShopsPartnerService shopsPartnerService;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    /**
     *  分页查询记录
     * @param shopPassTicketParam
     * @return
     */
    @Override
    public IPage<ShopPassTicketVo> pageList(ShopPassTicketParam shopPassTicketParam) {
        IPage<ShopPassTicketVo> resultPage = new Page<>();
        List<ShopPassTicketVo> ticketVoList = new ArrayList<>();
        IPage<ShopPassTicket> page = null;
        page = this.baseMapper.queryList(new Page<>(shopPassTicketParam.getCurrent(), shopPassTicketParam.getSize()), shopPassTicketParam);
        List<ShopPassTicket> ticketList = page.getRecords();
        if(CollectionUtil.isNotEmpty(ticketList)){
            List<Long> ticketIdList = ticketList.stream().map(ShopPassTicket::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ShopPassTicketPartner> partnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            partnerLambdaQueryWrapper.in(ShopPassTicketPartner::getPassTicketId, ticketIdList);
            List<ShopPassTicketPartner> partnerList = this.shopPassTicketPartnerService.list(partnerLambdaQueryWrapper);
            Map<Long, List<ShopPassTicketPartner>> partnerMap = partnerList.stream().collect(Collectors.groupingBy(ShopPassTicketPartner::getPassTicketId));

            LambdaQueryWrapper<ShopPassTicketProduct> productLambdaQueryWrapper = new LambdaQueryWrapper<>();
            productLambdaQueryWrapper.in(ShopPassTicketProduct::getPassTicketId, ticketIdList);
            List<ShopPassTicketProduct> productList = this.shopPassTicketProductService.list(productLambdaQueryWrapper);
            Map<Long, List<ShopPassTicketProduct>> productMap = productList.stream().collect(Collectors.groupingBy(ShopPassTicketProduct::getPassTicketId));


            ticketList.stream().forEach(e -> {
                Long id = e.getId();
                ShopPassTicketVo vo = new ShopPassTicketVo();
                BeanUtil.copyProperties(e, vo);
                vo.setPartnerList(partnerMap.get(id) == null ? new ArrayList<>() : partnerMap.get(id));
                vo.setProductList(productMap.get(id) == null ? new ArrayList<>() : productMap.get(id));
                ticketVoList.add(vo);
            });
        }
        BeanUtil.copyProperties(page, resultPage);
        resultPage.setRecords(ticketVoList);
        return resultPage;
    }


    /**
     * 添加记录
     * @param shopPassTicketDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopPassTicket add(ShopPassTicketDto shopPassTicketDto) {
        ShopPassTicket shopPassTicket=new ShopPassTicket();
        BeanUtils.copyProperties(shopPassTicketDto, shopPassTicket);
        shopPassTicket.setStatus(ApproveStatusEnum.AUDIT.getStatus());
        shopPassTicket.setApprovedStatus(ApproveStatusEnum.AUDIT.getStatus());
        //主表id
        Long id= IDUtil.getId();
        shopPassTicket.setId(id);
        List<ShopPassTicketPartnerDto> partnerDtoList = shopPassTicketDto.getPartnerList();
        List<ShopPassTicketPartner> partnerList = new ArrayList<ShopPassTicketPartner>();

        //将ShopPassTicketPartnerDto转成ShopPassTicketPartner
        List<Long> partnerIdList = new ArrayList<>();
        if(partnerDtoList!=null&&partnerDtoList.size()>0){
            for(ShopPassTicketPartnerDto partnerDto : partnerDtoList){

                if(partnerIdList.contains(partnerDto.getShopsPartnerId())){
                    ShopsPartner shopsPartner =  shopsPartnerService.getById(partnerDto.getShopsPartnerId());
                    throw new ServiceException("重复选择了商家[" + shopsPartner.getName() + "]");
                }else{
                    partnerIdList.add(partnerDto.getShopsPartnerId());
                }
                partnerDto.setPassTicketId(id);
                ShopPassTicketPartner partner = new ShopPassTicketPartner();
                BeanUtils.copyProperties(partnerDto, partner);
                partnerList.add(partner);
            }
        }


        List<ShopPassTicketProductDto> productDtoList = shopPassTicketDto.getProductList();
        List<ShopPassTicketProduct> productList = new ArrayList<ShopPassTicketProduct>();

        //将ShopPassTicketProductDto转成ShopPassTicketProduct
        List<Long> skuIdList = new ArrayList<>();

        if(productDtoList!=null&&productDtoList.size()>0){
            for(ShopPassTicketProductDto productDto : productDtoList){

                if(skuIdList.contains(productDto.getProductSkuId())){
                    ProductVo product =  remoteGoodsService.findProductById(productDto.getProductId());
                    throw new ServiceException("重复选择了商品[" + product.getName() + "]");
                }else{
                    skuIdList.add(productDto.getProductSkuId());
                }

                productDto.setPassTicketId(id);
                ShopPassTicketProduct product = new ShopPassTicketProduct();
                BeanUtils.copyProperties(productDto, product);
                productList.add(product);
            }
        }
        //保存主表信息
        this.save(shopPassTicket);
        shopPassTicketPartnerService.saveBatch(partnerList);
        shopPassTicketProductService.saveBatch(productList);
        return shopPassTicket;
    }

    /**
     * 编辑记录
     * @param shopPassTicketDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopPassTicket edit(ShopPassTicketDto shopPassTicketDto) {
        ShopPassTicket shopPassTicket = this.getById(shopPassTicketDto.getId());
        if(null == shopPassTicket){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(shopPassTicket.getApprovedStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            throw new ServiceException("已审核，不能再编辑", SystemCode.DATA_EXPIRED_CODE);
        }
        Long id = shopPassTicket.getId();
        BeanUtils.copyProperties(shopPassTicketDto, shopPassTicket);
        if(shopPassTicket.getExpiredType() == ExpiredTypeEnum.BUY_DATE.getType()){
            shopPassTicket.setStartTime(null);
            shopPassTicket.setEndTime(null);
        }
        //先删除子表信息
        LambdaQueryWrapper<ShopPassTicketPartner> partnerWrapper = new LambdaQueryWrapper<>();
        partnerWrapper.eq(ShopPassTicketPartner::getPassTicketId, id);
        this.shopPassTicketPartnerService.remove(partnerWrapper);

        LambdaQueryWrapper<ShopPassTicketProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(ShopPassTicketProduct::getPassTicketId, id);
        this.shopPassTicketProductService.remove(productWrapper);
        // 新增子表信息
        List<ShopPassTicketPartnerDto> partnerDtoList = shopPassTicketDto.getPartnerList();
        List<ShopPassTicketPartner> partnerList = new ArrayList<ShopPassTicketPartner>();

        //将ShopPassTicketPartnerDto转成ShopPassTicketPartner
        if(partnerDtoList!=null&&partnerDtoList.size()>0){
            for(ShopPassTicketPartnerDto partnerDto : partnerDtoList){

                partnerDto.setPassTicketId(id);
                partnerDto.setId(null);
                ShopPassTicketPartner partner = new ShopPassTicketPartner();
                BeanUtils.copyProperties(partnerDto, partner);
                partnerList.add(partner);
            }
        }


        List<ShopPassTicketProductDto> productDtoList = shopPassTicketDto.getProductList();
        List<ShopPassTicketProduct> productList = new ArrayList<ShopPassTicketProduct>();

        //将ShopPassTicketProductDto转成ShopPassTicketProduct
        if(productDtoList!=null&&productDtoList.size()>0){
            for(ShopPassTicketProductDto productDto : productDtoList){

                productDto.setPassTicketId(id);
                productDto.setId(null);
                ShopPassTicketProduct product = new ShopPassTicketProduct();
                BeanUtils.copyProperties(productDto, product);
                productList.add(product);
            }
        }

        //保存主表信息
        this.updateById(shopPassTicket);
        shopPassTicketPartnerService.saveBatch(partnerList);
        shopPassTicketProductService.saveBatch(productList);
        return shopPassTicket;
    }

    @Override
    public List<TicketVo> queryTicketItemVoByIds(List<Long> ticketIds) {
        if (ticketIds.isEmpty()) {
            return new ArrayList<>(CommonConstants.NUMBER_ZERO);
        } else {
            return this.baseMapper.queryTicketItemVoByIds(ticketIds);
        }
    }

    /**
     *  分页查询显示的通票记录
     * @param shopPassTicketParam
     * @return
     */
    @Override
    public IPage<ShopPassTicketVo> pageDisplayList(ShopPassTicketParam shopPassTicketParam) {
        IPage<ShopPassTicketVo> resultPage = new Page<>();
        List<ShopPassTicketVo> ticketVoList = new ArrayList<>();
        IPage<ShopPassTicket> page = null;
        page = this.baseMapper.selectDisplayList(new Page<>(shopPassTicketParam.getCurrent(), shopPassTicketParam.getSize()), shopPassTicketParam);
        BeanUtil.copyProperties(page, resultPage);
        page.getRecords().stream().forEach(e -> {
            ShopPassTicketVo vo = new ShopPassTicketVo();
            BeanUtil.copyProperties(e, vo);
            ticketVoList.add(vo);

        });
        resultPage.setRecords(ticketVoList);
        return resultPage;
    }

    /**
     * 通过时间更新通惠证的状态
     * @return
     */
    @Override
    public List<ShopPassTicket> autoUpdateStatusByTime(){
        //查询审核状态为已审核，状态未生效、已生效且开始时间、结束时间大于当前时间的记录
        LambdaQueryWrapper<ShopPassTicket> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopPassTicket::getApprovedStatus, ApproveStatusEnum.APPROVED.getStatus())
                .and(i -> i.eq(ShopPassTicket::getStatus, ApproveStatusEnum.AUDIT.getStatus()).or().eq(ShopPassTicket::getStatus, ApproveStatusEnum.APPROVED.getStatus()))
                .and(i -> i.le(ShopPassTicket::getStartTime, new Date()).or().lt(ShopPassTicket::getEndTime, new Date()).or().eq(ShopPassTicket::getExpiredType, ExpiredTypeEnum.BUY_DATE.getType()));
        List<ShopPassTicket> ticketList = this.list(wrapper);
        if(BeanUtil.isNotEmpty(ticketList)){
            ticketList.stream().forEach(e -> {
                if(e.getExpiredType() == ExpiredTypeEnum.BUY_DATE.getType()){
                    e.setStatus(ApproveStatusEnum.APPROVED.getStatus());
                }else if(e.getEndTime().before(new Date())){
                    // 结束时间小于当前时间，状态改为已结束
                    e.setStatus(ApproveStatusEnum.EXPIRED.getStatus());
                }else if(e.getStatus().intValue() == ApproveStatusEnum.AUDIT.getStatus()){
                    // 未生效，因为前面已经判断结束时间小于当前时间，所以这里一定是开始时间小于当前时间，状态改为已生效
                    e.setStatus(ApproveStatusEnum.APPROVED.getStatus());
                }
            });
            this.updateBatchById(ticketList);
        }
        return ticketList;
    }

    /**
     * 审核通惠证
     * @param id
     * @return
     */
    @Override
    public ShopPassTicket audit(Long id){
        ShopPassTicket shopPassTicket = this.getById(id);
        if(null == shopPassTicket){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(shopPassTicket.getApprovedStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            throw new ServiceException("已审核，不能重复审核", SystemCode.DATA_NOT_EXIST_CODE);
        }
        shopPassTicket.setApprovedStatus(ApproveStatusEnum.APPROVED.getStatus());
        shopPassTicket.setApprovedTime(DateUtil.toLocalDateTime(new Date()));
        shopPassTicket.setApprovedUserId(Long.parseLong(CurUserUtil.getPcRqeustAccountInfo().getUserId()));
        shopPassTicket.setApprovedUserName(CurUserUtil.getPcRqeustAccountInfo().getNikeName());
        boolean result = this.updateById(shopPassTicket);
        if(result){
            ShopsRedis shopsRedis = new ShopsRedis();
            String key = ShopsRedisKey.getPassTicketTotalNumKey(shopPassTicket.getId() + "");
            Integer totalNum = shopPassTicket.getTotalNum();
            if(null == totalNum || totalNum.intValue() == 0){
                totalNum = ********;
            }
            // 将发行量存缓存
            shopsRedis.set(key, totalNum.intValue() + "");
            // 过期毫秒数
            Long time = DateUtil.between(new Date(), shopPassTicket.getDisplayEndTime(), DateUnit.SECOND);
            shopsRedis.expire(key, time.intValue());
        }
        return shopPassTicket;

    }

    /**
     * 停用通惠证
     * @param id
     * @return
     */
    @Override
    public ShopPassTicket stop(Long id){
        ShopPassTicket shopPassTicket = this.getById(id);
        if(null == shopPassTicket){
            throw new ServiceException("数据错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        shopPassTicket.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
        this.updateById(shopPassTicket);
        return shopPassTicket;

    }

    /**
     * 通惠证减库存
     * @param id
     * @return
     */
    @Override
    public Long atomSubtractStock(Long id) {
        ShopsRedis shopsRedis = new ShopsRedis();
        String key = ShopsRedisKey.getPassTicketTotalNumKey(id + "");
        Long value = shopsRedis.decr(key);
        return value;
    }

    /**
     * 通惠证加库存
     * @param id
     * @return
     */
    @Override
    public Long atomAddStock(Long id) {
        ShopsRedis shopsRedis = new ShopsRedis();
        String key = ShopsRedisKey.getPassTicketTotalNumKey(id + "");
        Long value = shopsRedis.incr(key);
        return value;
    }

    /**
     * 通过id查询数据
     * @param id
     * @return
     */
    @Override
    public ShopPassTicketVo queryById(Long id) {
        ShopPassTicket shopPassTicket = this.getById(id);
        ShopPassTicketVo shopPassTicketVo = new ShopPassTicketVo();
        BeanUtils.copyProperties(shopPassTicket,shopPassTicketVo);
        LambdaQueryWrapper<ShopPassTicketPartner> partnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        partnerLambdaQueryWrapper.eq(ShopPassTicketPartner::getPassTicketId, id);
        List<ShopPassTicketPartner> partnerList = this.shopPassTicketPartnerService.list(partnerLambdaQueryWrapper);
        shopPassTicketVo.setPartnerList(partnerList);
        LambdaQueryWrapper<ShopPassTicketProduct> productLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productLambdaQueryWrapper.eq(ShopPassTicketProduct::getPassTicketId, id);
        List<ShopPassTicketProduct> productList = this.shopPassTicketProductService.list(productLambdaQueryWrapper);
        shopPassTicketVo.setProductList(productList);
        return shopPassTicketVo;
    }

    /**
     * 通过店铺主键id查询能用哪些通惠证
     * @param shopPartnerId
     * @return
     */
    @Override
    public List<ShopPassTicket> queryByShopPartnerId(Long shopPartnerId) {
        // 查询指定商家可用的通惠证
        LambdaQueryWrapper<ShopPassTicketPartner> ticketPartnerWrapper = new LambdaQueryWrapper<>();
        ticketPartnerWrapper.eq(ShopPassTicketPartner::getShopsPartnerId, shopPartnerId);
        List<ShopPassTicketPartner> ticketPartnerList = this.shopPassTicketPartnerService.list(ticketPartnerWrapper);
        List<Long> ticketIdList = ticketPartnerList.stream().map(ShopPassTicketPartner::getPassTicketId).collect(Collectors.toList());
        List<ShopPassTicket> ticketList = new ArrayList<>();
        if(ticketIdList!=null&&ticketIdList.size()>0){
            // 查询通惠证
            LambdaQueryWrapper<ShopPassTicket> ticketWrapper = new LambdaQueryWrapper<>();
            ticketWrapper.in(ShopPassTicket::getId, ticketIdList).or().eq(ShopPassTicket::getShopFlag, CommonConstants.NUMBER_ZERO);
            ticketList = this.list(ticketWrapper);
        }else{
            // 查询通惠证
            LambdaQueryWrapper<ShopPassTicket> ticketWrapper = new LambdaQueryWrapper<>();
            ticketWrapper.eq(ShopPassTicket::getShopFlag, CommonConstants.NUMBER_ZERO);
            ticketList = this.list(ticketWrapper);
        }
        return ticketList;
    }

    @Override
    public Integer countShopFlag() {
        Integer count = this.baseMapper.countShopFlag();
        return count;
    }

    @Override
    public Integer countShopFlagByShopsPartnerId(Long shopsPartnerId) {
        Integer count = this.baseMapper.countShopFlagByShopsPartnerId(shopsPartnerId);
        return count;
    }

}
