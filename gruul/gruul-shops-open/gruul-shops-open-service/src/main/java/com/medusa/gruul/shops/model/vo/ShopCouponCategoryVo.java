package com.medusa.gruul.shops.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:26 2025/4/2
 */
@Data
@ApiModel(value = "优惠券品类Vo")
public class ShopCouponCategoryVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    @ApiModelProperty(value = "品类id")
    private String categoryId;

    @ApiModelProperty(value = "商品专区")
    private String modeName;

    @ApiModelProperty(value = "商品一级分类")
    private String categoryParentName;

    @ApiModelProperty(value = "商品二级分类")
    private String categoryName;

}
