package com.medusa.gruul.shops.controller.api;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.shops.api.entity.ShopPassTicketRule;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.service.IShopPassTicketRuleService;
import com.medusa.gruul.shops.service.IShopPassTicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 *小程序-通行票控制器
 */
@RestController
@Api(tags = "小程序-通行票控制器")
@RequestMapping("/api/shopPassTicket")
public class ApiShopPassTicketController {

    @Autowired
    private IShopPassTicketService shopPassTicketService;
    @Autowired
    private IShopPassTicketRuleService shopPassTicketRuleService;

    /**
     * 分页查询显示的通票记录
     * @param shopPassTicketParam
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询通行票", tags = "接口")
    public Result<PageUtils<ShopPassTicket>> pageList(@RequestBody ShopPassTicketParam shopPassTicketParam){
        shopPassTicketParam.setApprovedStatus(ApproveStatusEnum.APPROVED.getStatus());
        List<Integer> displayStatusList = new ArrayList<>();
        displayStatusList.add(ApproveStatusEnum.AUDIT.getStatus());
        displayStatusList.add(ApproveStatusEnum.APPROVED.getStatus());
        shopPassTicketParam.setDisplayStatus(displayStatusList);
        String nowDate = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        shopPassTicketParam.setDisplayDate(nowDate);
        PageUtils<ShopPassTicket> pageUtils = new PageUtils(shopPassTicketService.pageDisplayList(shopPassTicketParam));
        return Result.ok(pageUtils);
    }

    @GetMapping("/getPassTicketFlag")
    @ApiOperation(value = "是否启用通惠证", tags = "接口")
    public Result getPassTicketFlag(){
        Map result = new HashMap<>();
        int count = shopPassTicketService.count();
        if(count>0){
            result.put("passTicketFlag",true);
        }else{
            result.put("passTicketFlag",false);
        }
        return Result.ok(result);
    }

    @PostMapping("/getPassTicketRule")
    @ApiOperation(value = "获取通惠证规则", tags = "接口")
    public Result<ShopPassTicketRule> getPassTicketRule(){
        ShopPassTicketRule rule = shopPassTicketRuleService.queryOne();
        return Result.ok(rule);
    }

}
