package com.medusa.gruul.shops.controller.api;

import cn.hutool.core.date.DateUtil;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.CouponTypeEnum;
import com.medusa.gruul.shops.model.param.ShopCouponParam;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopCouponVo;
import com.medusa.gruul.shops.service.IShopCouponService;
import com.medusa.gruul.shops.service.IShopPassTicketService;
import com.medusa.gruul.shops.service.ShopsPartnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Author: plh
 * @Description: 小程序-优惠券控制器
 * @Date: Created in 8:55 2024/8/27
 */
@RestController
@Api(tags = "小程序-优惠券控制器")
@RequestMapping("/api/shopCoupon")
public class ApiShopCouponController {

    @Autowired
    private IShopCouponService shopCouponService;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private ShopsPartnerService shopsPartnerService;
    /**
     * 查询优惠券数据
     * @param shopCouponParam
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询优惠券数据", tags = "接口")
    public Result<List<ShopCouponVo>> pageList(@RequestBody ShopCouponParam shopCouponParam){
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            MiniAccount miniAccount = accountInfoDto.getMiniAccountunt();
            System.out.println(miniAccount);
            LocalDateTime createTime = miniAccount.getCreateTime();
            LocalDateTime now = LocalDateTime.now();
            long days = ChronoUnit.DAYS.between(createTime, now);
            //获取主店铺
            ShopsPartner shopsPartner = shopsPartnerService.getShopsPartnerMain();
            //获取主店铺特殊配置
            List<SpecialSetting> SpecialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());
            if(SpecialSettingList!=null&&SpecialSettingList.size()>0){
                SpecialSetting specialSetting = SpecialSettingList.get(0);
                Integer newPeopleCouponDays = specialSetting.getNewPeopleCouponDays();
                if(days>newPeopleCouponDays){
                    shopCouponParam.setCouponType(CouponTypeEnum.NEW_PEOPLE.getType());
                }
            }
        }
        shopCouponParam.setApprovedStatus(ApproveStatusEnum.APPROVED.getStatus());
        List<Integer> displayStatusList = new ArrayList<>();
        displayStatusList.add(ApproveStatusEnum.AUDIT.getStatus());
        displayStatusList.add(ApproveStatusEnum.APPROVED.getStatus());



        shopCouponParam.setDisplayStatus(displayStatusList);
        String nowDate = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        shopCouponParam.setDisplayDate(nowDate);
        List<ShopCouponVo> list = shopCouponService.getList(shopCouponParam);
        return Result.ok(list);
    }

    /**
     * 是否启用优惠券
     * @return
     */
    @GetMapping("/getCouponFlag")
    @ApiOperation(value = "是否启用优惠券", tags = "接口")
    public Result getCouponFlag(){
        Map result = new HashMap<>();
        int count = shopCouponService.count();
        if(count>0){
            result.put("couponFlag",true);
        }else{
            result.put("couponFlag",false);
        }
        return Result.ok(result);
    }

    @GetMapping("/getCouponUserFlag")
    @ApiOperation(value = "用户优惠券弹窗", tags = "接口")
    public Result getCouponUserFlag(){
        Map dataMap = shopCouponService.getCouponUserFlag();
        return Result.ok(dataMap);
    }

    /**
     * 用户优惠券弹窗当天不显示
     * @return
     */
    @PostMapping("/setShowCouponTime")
    @ApiOperation(value = "用户优惠券弹窗当天不显示", tags = "接口")
    public Result setShowCouponTime(){
        shopCouponService.setShowCouponTime();
        return Result.ok();
    }
}
