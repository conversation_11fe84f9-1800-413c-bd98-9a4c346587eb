package com.medusa.gruul.shops.controller;

import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.shops.api.entity.ShopCommissionRule;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.model.dto.ShopCommissionRuleDto;
import com.medusa.gruul.shops.model.dto.ShopPassTicketDto;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopPassTicketVo;
import com.medusa.gruul.shops.service.IShopCommissionRuleService;
import com.medusa.gruul.shops.service.IShopPassTicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 *佣金规则控制器
 */
@RestController
@Api(tags = "商家-佣金规则相关接口")
@RequestMapping("/shop-commission-rule")
public class ShopCommissionRuleController {

    @Autowired
    private IShopCommissionRuleService shopCommissionRuleService;


    /**
     * 保存佣金规则
     * @param shopCommissionRuleDto
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存佣金规则")
    public Result<ShopCommissionRule> save(@RequestBody @Validated ShopCommissionRuleDto shopCommissionRuleDto){
        ShopCommissionRule shopCommissionRule = this.shopCommissionRuleService.save(shopCommissionRuleDto);
        return Result.ok(shopCommissionRule, "保存成功");
    }

    /**
     * 查询佣金规则
     * @return
     */
    @GetMapping("/get")
    @ApiOperation(value = "查询佣金规则")
    public Result<ShopCommissionRule> get(){
        ShopCommissionRule shopCommissionRule = this.shopCommissionRuleService.getNewest();
        return Result.ok(shopCommissionRule);
    }



}
