package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.shops.api.entity.ShopCoupon;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.model.param.ShopCouponParam;
import com.medusa.gruul.shops.model.param.ShopPassTicketParam;
import com.medusa.gruul.shops.model.vo.ShopCouponVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:23 2024/8/23
 */
@Repository
public interface ShopCouponMapper extends BaseMapper<ShopCoupon> {

    /**
     * 分页查询优惠券记录
     * @param page
     * @param shopCouponParam
     * @return
     */
    IPage<ShopCouponVo> queryList(IPage page , @Param("params") ShopCouponParam shopCouponParam);
    /**
     * 查询显示的优惠券
     * @param shopCouponParam
     * @return
     */
    List<ShopCouponVo> selectDisplayList(@Param("shopCouponParam")ShopCouponParam shopCouponParam);

    /**
     * 查询可以使用的优惠券
     * @return
     */
    List<ShopCouponVo> getShopCouponVoList();
}
