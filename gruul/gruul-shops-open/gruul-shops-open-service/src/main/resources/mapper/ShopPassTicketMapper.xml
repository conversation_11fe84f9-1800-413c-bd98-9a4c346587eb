<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.ShopPassTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.shops.api.entity.ShopPassTicket">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="ticket_name" property="ticketName"/>
        <result column="price" property="price"/>
        <result column="useable_times" property="useableTimes"/>
        <result column="ticket_type" property="ticketType"/>
        <result column="full_amount" property="fullAmount"/>
        <result column="promotion" property="promotion"/>
        <result column="start_time" property="startTime"/>
        <result column="expired_type" property="expiredType"/>
        <result column="end_time" property="endTime"/>
        <result column="expired_days" property="expiredDays"/>
        <result column="status" property="status"/>
        <result column="shop_Flag" property="shopFlag"/>
        <result column="product_flag" property="productFlag"/>
        <result column="approved_time" property="approvedTime"/>
        <result column="approved_user_name" property="approvedUserName"/>
        <result column="approved_user_id" property="approvedUserId"/>
        <result column="remark" property="remark"/>
        <result column="back_pic" property="backPic"/>
        <result column="back_color" property="backColor"/>
        <result column="rule" property="rule"/>
        <result column="total_Num" property="totalNum"/>
        <result column="display_Start_Time" property="displayStartTime"/>
        <result column="display_end_time" property="displayEndTime"/>
        <result column="after_days_valid" property="afterDaysValid"/>
        <result column="approved_status" property="approvedStatus"/>
    </resultMap>
    <resultMap id="TicketVoMap" type="com.medusa.gruul.shops.api.model.TicketVo">
        <result column="product_id" property="productId"/>
        <result column="back_pic" property="productPic"/>
        <result column="ticket_name" property="productName"/>
        <result column="price" property="productPrice"/>
        <result column="full_amount" property="fullAmount"/>
        <result column="promotion" property="promotion"/>
        <result column="ticket_type" property="ticketType"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,is_deleted,update_time, ticket_name,price,useable_times, ticket_type, full_amount, promotion, start_time, expired_type, end_time, expired_days,
        status, shop_Flag, product_flag, approved_time, approved_user_name, approved_user_id, remark, back_color, back_pic, rule, total_Num, display_Start_Time, display_end_time, after_days_valid,
        approved_status
    </sql>

    <select id="queryList" parameterType="Object"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_shop_pass_ticket t where t.is_deleted = 0
        <if test="shopPassTicketParam.status!=null">
            and t.status=#{shopPassTicketParam.status}
        </if>
        <if test="shopPassTicketParam.approvedStatus!=null">
            and t.approved_status=#{shopPassTicketParam.approvedStatus}
        </if>
        <if test="shopPassTicketParam.ticketType!=null ">
            and t.ticket_type=#{shopPassTicketParam.ticketType}
        </if>
        <if test="shopPassTicketParam.useableTimes!=null ">
            and t.useable_times=#{shopPassTicketParam.useableTimes}
        </if>
        <if test="shopPassTicketParam.ticketName!=null and shopPassTicketParam.ticketName!='' ">
            and t.ticket_name like CONCAT('%',#{shopPassTicketParam.ticketName},'%')
        </if>
        <if test="shopPassTicketParam.startTimeBegin!=null ">
            and t.start_time >=#{shopPassTicketParam.startTimeBegin}
        </if>
        <if test="shopPassTicketParam.startTimeEnd!=null ">
            and t.start_time &gt;=#{shopPassTicketParam.startTimeEnd}
        </if>
        <if test="shopPassTicketParam.endTimeBegin!=null ">
            and t.end_time >=#{shopPassTicketParam.endTimeBegin}
        </if>
        <if test="shopPassTicketParam.endTimeEnd!=null ">
            and t.end_time &gt;=#{shopPassTicketParam.endTimeEnd}
        </if>
        <if test="shopPassTicketParam.startTimeSort!=null and shopPassTicketParam.startTimeSort == 1">
            order by t.start_time asc
        </if>
        <if test="shopPassTicketParam.startTimeSort!=null and shopPassTicketParam.startTimeSort == 2">
            order by t.start_time desc
        </if>
        <if test="shopPassTicketParam.endTimeSort!=null and shopPassTicketParam.endTimeSort == 1">
            order by t.end_time asc
        </if>
        <if test="shopPassTicketParam.endTimeSort!=null and shopPassTicketParam.endTimeSort == 2">
            order by t.end_time desc
        </if>
        <if test="shopPassTicketParam.priceSort!=null and shopPassTicketParam.priceSort == 1">
            order by t.price asc
        </if>
        <if test="shopPassTicketParam.priceSort!=null and shopPassTicketParam.priceSort == 2">
            order by t.price desc
        </if>
        <if test="shopPassTicketParam.useableTimesSort!=null and shopPassTicketParam.useableTimesSort == 1">
            order by t.useable_times asc
        </if>
        <if test="shopPassTicketParam.useableTimesSort!=null and shopPassTicketParam.useableTimesSort == 2">
            order by t.useable_times desc
        </if>

    </select>

    <select id="selectDisplayList" parameterType="Object"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_shop_pass_ticket t where t.is_deleted = 0
        <if test="shopPassTicketParam.approvedStatus!=null">
            and t.approved_Status=#{shopPassTicketParam.approvedStatus}
        </if>
        <if test="shopPassTicketParam.ticketType!=null ">
            and t.ticket_type=#{shopPassTicketParam.ticketType}
        </if>
        <if test="shopPassTicketParam.ticketName!=null and shopPassTicketParam.ticketName!='' ">
            and t.ticket_name like CONCAT('%',#{shopPassTicketParam.ticketName},'%')
        </if>
        <if test="shopPassTicketParam.displayDate!=null">
            AND t.display_start_time &lt;= #{shopPassTicketParam.displayDate} AND t.display_end_time >= #{shopPassTicketParam.displayDate}
        </if>

        <if test="shopPassTicketParam.displayStatus!=null">
            and t.status in
            <foreach collection="shopPassTicketParam.displayStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY t.display_start_time, t.start_time DESC
    </select>
    <select id="queryTicketItemVoByIds" resultMap="TicketVoMap">
        select t1.id as product_id,t1.back_pic,t1.ticket_name,t1.price,t1.full_amount,t1.promotion,t1.ticket_type, t1.approved_status, t1.status
            from t_shop_pass_ticket t1
            where  t1.is_deleted = 0
              and  t1.id in
        <foreach collection="ticketIds" item="ticketId" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
    </select>

    <select id="countShopFlag" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_shop_pass_ticket
        WHERE
            shop_flag = 0 AND is_deleted = 0
            AND approved_status = 101
            AND (
                (display_start_time &gt;= NOW() AND display_end_time &lt;= NOW())
                OR (start_time &gt;= NOW() AND end_time &lt;= NOW())
                )
    </select>
    <select id="countShopFlagByShopsPartnerId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_shop_pass_ticket t1
            LEFT JOIN t_shop_pass_ticket_partner t2 ON t1.id = t2.pass_ticket_id
            AND t2.is_deleted = 0
        WHERE
            t1.shop_flag = 1
            AND t1.is_deleted = 0
            AND t1.approved_status = 101
            AND ((
                t1.display_start_time &gt;= NOW()
            AND t1.display_end_time &lt;= NOW()) OR ( t1.start_time &gt;= NOW()
            AND t1.end_time &lt;= NOW()))
            AND t2.shops_partner_id = #{shopsPartnerId}
    </select>
</mapper>
