<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shops.mapper.ShopCouponPartnerMapper">
    <select id="getShopIds" resultType="java.lang.String">
        select
            t2.shop_id
        from
             t_shop_coupon_partner t1
        left join t_shops_partner t2 on t2.id = t1.shops_partner_id
        where t1.coupon_id = #{couponId} and t1.is_deleted = 0 and t2.is_deleted = 0
    </select>
</mapper>
