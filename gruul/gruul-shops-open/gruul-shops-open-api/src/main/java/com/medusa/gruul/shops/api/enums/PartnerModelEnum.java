package com.medusa.gruul.shops.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:23 2023/9/13
 */
@Getter
public enum PartnerModelEnum {

    JOIN("0", "加盟"),
    SUBSIDIARY("1", "子公司");

    @EnumValue
    /**
     * 值
     */
    private final String type;

    /**
     * 描述
     */
    private final String desc;

    PartnerModelEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
