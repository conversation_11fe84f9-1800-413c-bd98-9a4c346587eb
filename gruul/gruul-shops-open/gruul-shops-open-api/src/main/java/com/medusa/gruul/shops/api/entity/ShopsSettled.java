package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 商家入驻
 * @Date: Created in 16:03 2023/8/30
 */
@Data
@ApiModel(value = "shops settled 实体", description = "商家入驻表")
@TableName("t_shops_settled")
public class ShopsSettled extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 店铺用户id
     */
    @ApiModelProperty(value = "店铺用户id")
    @TableField("shop_user_id")
    private String shopUserId;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    @TableField("shop_name")
    private String shopName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @TableField("shop_code")
    private String shopCode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField("contacts")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("phone")
    private String phone;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    @TableField("province_code")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    @TableField("city_code")
    private String cityCode;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @TableField("area_code")
    private String areaCode;

    /**
     *省名
     */
    @ApiModelProperty(value = "省名")
    @TableField("province_name")
    private String provinceName;

    /**
     * 市名
     */
    @ApiModelProperty(value = "市名")
    @TableField("city_name")
    private String cityName;

    /**
     * 区域名
     */
    @ApiModelProperty(value = "区域名")
    @TableField("area_name")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @TableField("address")
    private String address;

    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    @TableField("map_x")
    private String mapX;

    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    @TableField("map_y")
    private String mapY;


    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    @TableField("business_license")
    private String businessLicense;

    /**
     * 身份证正面
     */
    @ApiModelProperty(value = "身份证正面")
    @TableField("card_id_up")
    private String cardIdUp;

    /**
     * 身份证反面
     */
    @ApiModelProperty(value = "身份证反面")
    @TableField("card_id_down")
    private String cardIdDown;

    /**
     * 审批状态 0临时保存 审批状态 100 待审核 101 审核通过 200 驳回
     */
    @ApiModelProperty(value = "审批状态 0临时保存 审批状态 100 待审核 101 审核通过 200 驳回")
    @TableField("approval_status")
    private String approvalStatus;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    @TableField("approval_reason")
    private String approvalReason;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
    @TableField("house_number")
    private String houseNumber;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @TableField("apply_time")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核时间")
    @TableField("approval_time")
    private LocalDateTime approvalTime;
}
