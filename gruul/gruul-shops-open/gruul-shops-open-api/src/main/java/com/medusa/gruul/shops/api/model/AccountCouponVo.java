package com.medusa.gruul.shops.api.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:01 2024/8/28
 */
@Data
@ApiModel(value = "AccountCouponVo对象", description = "获取用户可使用优惠券数据")
public class AccountCouponVo {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    private String lastModifyUserName;

    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 购买单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 每个店铺使用次数
     */
    @ApiModelProperty(value = "每个店铺使用次数")
    private Integer useableTimes;

    /**
     * 票类型:100->满减;101->折扣;
     */
    @ApiModelProperty(value = "票类型:100->满减;101->折扣;")
    private Integer ticketType;

    /**
     * 满额（存满100减20的100值）
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 到期类型:100->指定时间;101->购买之日起计算;
     */
    @ApiModelProperty(value = "到期类型:100->指定时间;101->购买之日起计算;")
    private Integer expiredType;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 过期天数，购买之日起，多少天过期
     */
    @ApiModelProperty(value = "过期天数，购买之日起，多少天过期")
    private Integer expiredDays;

    /**
     * 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
     */
    @ApiModelProperty(value = "状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止")
    private Integer status;

    /**
     * 指定商家，0否1是
     */
    @ApiModelProperty(value = "指定商家，0否1是")
    private Boolean shopFlag;

    /**
     * 指定商品，0否1是
     */
    @ApiModelProperty(value = "指定商品，0否1是")
    private Boolean productFlag;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime approvedTime;


    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String approvedUserName;

    /**
     * 审核人id
     */
    @ApiModelProperty(value = "审核人id")
    private Long approvedUserId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 背景图片
     */
    @ApiModelProperty(value = "背景图片")
    private String backPic;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backColor;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 发行量
     */
    @ApiModelProperty(value = "发行量，0表示不限量")
    private Integer totalNum;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 购买后多少天再生效
     */
    @ApiModelProperty(value = "购买后多少天再生效")
    private Integer afterDaysValid;

    /**
     * 审核状态:100->待审核;101->审核通过;200->驳回
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->审核通过;200->驳回")
    private Integer approvedStatus;

    /**
     * 新人券，0否1是
     */
    @ApiModelProperty(value = "新人券，0否1是")
    private Integer newPeopleFlag;

    /**
     * 领取次数
     */
    @ApiModelProperty(value = "领取次数")
    private Integer receiveTimes;
    /**
     * 优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券
     */
    @ApiModelProperty(value = "优惠券类型->0:普通券；1：新人券；2.商品优惠券；3.品类优惠券")
    private Integer couponType;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    private Integer useDate;

    /**
     * 指定商家id
     */
    @ApiModelProperty(value = "指定商家id")
    private List<String> shopIds;

    /**
     * 指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;
     */
    @ApiModelProperty(value = "指定发券对象:0->全部;1->标签客户;2->会员等级;3->指定客户;")
    private Integer grantType;

    /**
     * 是否发送：0.否；1.是
     */
    @ApiModelProperty(value = "是否发送：0.否；1.是")
    private Integer sendFlag;
}
