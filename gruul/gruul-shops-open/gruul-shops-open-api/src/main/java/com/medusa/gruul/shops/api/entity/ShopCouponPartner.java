package com.medusa.gruul.shops.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: 优惠券-商家记录
 * @Date: Created in 16:15 2024/8/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_shop_coupon_partner")
@ApiModel(value = "ShopCouponPartner对象", description = "优惠券-商家记录")
public class ShopCouponPartner extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 优惠证id
     */
    @ApiModelProperty(value = "优惠证id")
    @TableField("coupon_id")
    private Long couponId;

    /**
     * 商家(店铺)id
     */
    @ApiModelProperty(value = "商家(店铺)id")
    @TableField("shops_partner_id")
    private Long shopsPartnerId;

}
