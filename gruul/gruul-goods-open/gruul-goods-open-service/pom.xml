<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.medusa</groupId>
        <artifactId>gruul-goods-open</artifactId>
        <version>0.1</version>
    </parent>

    <artifactId>gruul-goods-open-service</artifactId>
    <packaging>jar</packaging>

    <description>gruul 商品模块</description>

    <dependencies>
        <!--api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-goods-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-order-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-logistics-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-oss-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-platform-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!-- 店铺api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-shops-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mp.version}</version>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--RocketMQ-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter-test</artifactId>
            <version>3.4.0</version>
        </dependency>
        <!--h2 单元测试-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-shops-open-service</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-shops-open-service</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-service</artifactId>
            <version>0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!--<plugin>-->
            <!--<groupId>com.spotify</groupId>-->
            <!--<artifactId>docker-maven-plugin</artifactId>-->
            <!--</plugin>-->
        </plugins>
    </build>


</project>