package com.medusa.gruul.goods.mapper.manager;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.model.dto.manager.ProductStockDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * @Description: 商品库存
 * @Author: qsx
 * @Date:   2022-02-28
 * @Version: V1.0
 */
@Repository
public interface ProductStockMapper extends BaseMapper<ProductStock> {
    /**
     * PC端获取仓库列表
     *
     * @param productStockListPage 分页数据
     * @param productStockDto        查询条件参数
     * @return com.medusa.gruul.goods.api.entity.ProductStock
     */
    IPage<ProductStock> selectProductStockList(Page<ProductStock> productStockListPage, @Param("productStockDto") ProductStockDto productStockDto);



}
