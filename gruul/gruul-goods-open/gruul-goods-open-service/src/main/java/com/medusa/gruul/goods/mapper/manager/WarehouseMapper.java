package com.medusa.gruul.goods.mapper.manager;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.model.dto.manager.WarehouseDto;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * @Description: 仓库信息
 * @Author: jeecg-boot
 * @Date:   2022-02-25
 * @Version: V1.0
 */
@Repository
public interface WarehouseMapper extends BaseMapper<Warehouse> {
    /**
     * PC端获取仓库列表
     *
     * @param page 分页数据
     * @param warehouseDto        查询条件参数
     * @return com.medusa.gruul.goods.api.entity.Warehouse
     */
    IPage<Warehouse> selectWarehouseList(IPage page ,@Param("warehouse") WarehouseDto warehouseDto);

    /**
     * 批量修改仓库发送状态
     * @param warehouseIds
     * @param sendStatus
     */
    void updateSendStatus(@Param("warehouseIds")List<Long> warehouseIds, @Param("sendStatus")String sendStatus);
}
