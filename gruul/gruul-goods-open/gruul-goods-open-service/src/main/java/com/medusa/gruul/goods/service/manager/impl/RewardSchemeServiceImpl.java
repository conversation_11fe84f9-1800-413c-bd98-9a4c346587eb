package com.medusa.gruul.goods.service.manager.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.constant.enums.RewardSchemeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.RewardScheme;
import com.medusa.gruul.goods.api.entity.RewardSchemeDet;
import com.medusa.gruul.goods.api.model.dto.manager.*;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeParam;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeShowParam;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDataVo;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDetVo;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeShowVo;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeVo;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeDetMapper;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeMapper;
import com.medusa.gruul.goods.service.manager.IRewardSchemeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:31 2025/3/10
 */
@Service
public class RewardSchemeServiceImpl extends ServiceImpl<RewardSchemeMapper, RewardScheme> implements IRewardSchemeService {


    @Autowired
    private RewardSchemeDetMapper rewardSchemeDetMapper;


    @Override
    @Transactional
    public void add(RewardSchemeDto rewardSchemeDto) {


        String billNo = rewardSchemeDto.getBillNo();
        LambdaQueryWrapper<RewardScheme>billNoWrapper = new LambdaQueryWrapper<>();
        billNoWrapper.eq(RewardScheme::getBillNo,billNo);
        Integer billNoNumber = baseMapper.selectCount(billNoWrapper);
        if(billNoNumber>0){
            throw new ServiceException("单据编号已存在！");
        }

        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        List<RewardSchemeDetDto> list = rewardSchemeDto.getList();
        LocalDateTime startTime = rewardSchemeDto.getStartTime();
        LocalDateTime endTime = rewardSchemeDto.getEndTime();
        //判断是否有重复值
        if(rewardSchemeDto.getStatus()==RewardSchemeEnum.NO.getStatus()){
            LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RewardScheme::getDeleted, CommonConstants.NUMBER_ZERO);
            wrapper.ne(RewardScheme::getStatus,0);
            wrapper.ne(RewardScheme::getStatus,-2);
            wrapper.and(e->e.ge(RewardScheme::getStartTime,startTime).le(RewardScheme::getStartTime,endTime).
                    or(e2->e2.ge(RewardScheme::getEndTime,startTime).le(RewardScheme::getEndTime,endTime)).
                    or(e3->e3.le(RewardScheme::getStartTime,startTime).ge(RewardScheme::getEndTime,endTime)));
            List<RewardScheme> rewardSchemeList = baseMapper.selectList(wrapper);
            if(rewardSchemeList.size()>0){
                for (RewardScheme rewardScheme : rewardSchemeList) {

                    for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                        LambdaQueryWrapper<RewardSchemeDet>queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(RewardSchemeDet::getDeleted,CommonConstants.NUMBER_ZERO);
                        queryWrapper.eq(RewardSchemeDet::getRewardId,rewardScheme.getId());

                        if(rewardSchemeDetDto.getRewardType()!=null){
                            queryWrapper.eq(RewardSchemeDet::getRewardType,rewardSchemeDetDto.getRewardType());
                        }else{
                            queryWrapper.isNull(RewardSchemeDet::getRewardType);
                        }

                        queryWrapper.eq(RewardSchemeDet::getProductId,rewardSchemeDetDto.getProductId());
                        queryWrapper.eq(RewardSchemeDet::getSkuId,rewardSchemeDetDto.getSkuId());

                        if(rewardSchemeDetDto.getPriceType()!=null){
                            queryWrapper.eq(RewardSchemeDet::getPriceType,rewardSchemeDetDto.getPriceType());
                        }else{
                            queryWrapper.isNull(RewardSchemeDet::getPriceType);
                        }

                        queryWrapper.eq(RewardSchemeDet::getMemberLevelId,rewardSchemeDetDto.getMemberLevelId());
                        Integer count = rewardSchemeDetMapper.selectCount(queryWrapper);
                        if (count > 0) {
                            throw new ServiceException("奖励类型，商品名称，商品规格，价格类型，会员等级相同的数据已存在！");
                        }
                    }
                }
            }
        }

        RewardScheme rewardScheme = new RewardScheme();
        BeanUtils.copyProperties(rewardSchemeDto,rewardScheme);
        rewardScheme.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
        rewardScheme.setCreateUserName(curUserDto.getNikeName());
        baseMapper.insert(rewardScheme);
        if(list!=null&&list.size()>0){
            for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                RewardSchemeDet rewardSchemeDet = new RewardSchemeDet();
                BeanUtils.copyProperties(rewardSchemeDetDto,rewardSchemeDet);
                rewardSchemeDet.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                rewardSchemeDet.setCreateUserName(curUserDto.getNikeName());
                rewardSchemeDet.setRewardId(String.valueOf(rewardScheme.getId()));
                rewardSchemeDetMapper.insert(rewardSchemeDet);
            }
        }

    }

    @Override
    @Transactional
    public void edit(RewardSchemeDto rewardSchemeDto) {

        Long id = rewardSchemeDto.getId();
        List<RewardSchemeDetDto> list = rewardSchemeDto.getList();
        if(id == null ){
            throw new ServiceException("奖励活动id不能为空");
        }
        RewardScheme rewardScheme = baseMapper.selectById(id);
        if(rewardScheme == null ){
            throw new ServiceException("奖励活动不存在");
        }

        String billNo = rewardSchemeDto.getBillNo();
        LambdaQueryWrapper<RewardScheme>billNoWrapper = new LambdaQueryWrapper<>();
        billNoWrapper.eq(RewardScheme::getBillNo,billNo);
        billNoWrapper.ne(RewardScheme::getId,id);
        Integer billNoNumber = baseMapper.selectCount(billNoWrapper);
        if(billNoNumber>0){
            throw new ServiceException("单据编号已存在！");
        }

        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        LocalDateTime startTime = rewardSchemeDto.getStartTime();
        LocalDateTime endTime = rewardSchemeDto.getEndTime();
        //判断是否有重复值
        if(rewardSchemeDto.getStatus()==RewardSchemeEnum.NO.getStatus()){
            LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RewardScheme::getDeleted, CommonConstants.NUMBER_ZERO);
            wrapper.ne(RewardScheme::getId,id);
            //过滤状态为0-草稿；-2-停止数据
            wrapper.ne(RewardScheme::getStatus,0);
            wrapper.ne(RewardScheme::getStatus,-2);
            wrapper.and(e->e.ge(RewardScheme::getStartTime,startTime).le(RewardScheme::getStartTime,endTime).
                    or(e2->e2.ge(RewardScheme::getEndTime,startTime).le(RewardScheme::getEndTime,endTime)).
                    or(e3->e3.le(RewardScheme::getStartTime,startTime).ge(RewardScheme::getEndTime,endTime)));
            List<RewardScheme> rewardSchemeList = baseMapper.selectList(wrapper);

            if(rewardSchemeList.size()>0){

                for (RewardScheme scheme : rewardSchemeList) {

                    for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                        LambdaQueryWrapper<RewardSchemeDet>queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.ne(RewardSchemeDet::getId,rewardSchemeDetDto.getId());
                        queryWrapper.eq(RewardSchemeDet::getDeleted,CommonConstants.NUMBER_ZERO);
                        queryWrapper.eq(RewardSchemeDet::getRewardId,scheme.getId());
                        if(rewardSchemeDetDto.getRewardType()!=null){
                            queryWrapper.eq(RewardSchemeDet::getRewardType,rewardSchemeDetDto.getRewardType());
                        }else{
                            queryWrapper.isNull(RewardSchemeDet::getRewardType);
                        }
                        queryWrapper.eq(RewardSchemeDet::getProductId,rewardSchemeDetDto.getProductId());
                        queryWrapper.eq(RewardSchemeDet::getSkuId,rewardSchemeDetDto.getSkuId());

                        if(rewardSchemeDetDto.getPriceType()!=null){
                            queryWrapper.eq(RewardSchemeDet::getPriceType,rewardSchemeDetDto.getPriceType());
                        }else{
                            queryWrapper.isNull(RewardSchemeDet::getPriceType);
                        }
                        queryWrapper.eq(RewardSchemeDet::getMemberLevelId,rewardSchemeDetDto.getMemberLevelId());
                        Integer count = rewardSchemeDetMapper.selectCount(queryWrapper);
                        if (count > 0) {
                            throw new ServiceException("奖励类型，商品名称，商品规格，价格类型，会员等级相同的数据【"+scheme.getBillNo()+"】已存在！");
                        }
                    }
                }
            }
        }
        LambdaQueryWrapper<RewardSchemeDet>wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(RewardSchemeDet::getRewardId,id);
        List<RewardSchemeDet> rewardSchemeDetList = rewardSchemeDetMapper.selectList(wrapper2);
        if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
            for (RewardSchemeDet rewardSchemeDet : rewardSchemeDetList) {
                rewardSchemeDetMapper.deleteById(rewardSchemeDet.getId());
            }
        }

        BeanUtils.copyProperties(rewardSchemeDto,rewardScheme);

        rewardScheme.setLastModifyUserId(Long.valueOf(curUserDto.getUserId()));
        rewardScheme.setLastModifyUserName(curUserDto.getNikeName());

        baseMapper.updateById(rewardScheme);

        if(list!=null&&list.size()>0){
            for (RewardSchemeDetDto rewardSchemeDetDto : list) {
                RewardSchemeDet rewardSchemeDet = new RewardSchemeDet();
                BeanUtils.copyProperties(rewardSchemeDetDto,rewardSchemeDet);
                rewardSchemeDet.setId(null);
                rewardSchemeDet.setCreateUserId(Long.valueOf(curUserDto.getUserId()));
                rewardSchemeDet.setCreateUserName(curUserDto.getNikeName());
                rewardSchemeDet.setRewardId(String.valueOf(rewardScheme.getId()));
                rewardSchemeDetMapper.insert(rewardSchemeDet);
            }
        }

    }

    @Override
    public Map<String, Object> getReceiptNoAndUser() {
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        Map<String,Object> map=new HashMap<String,Object>(4);
        SimpleDateFormat bartDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat bartDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String time="JLFA-"+bartDateFormat.format(date);
        String maxBuyNo = baseMapper.getReceiptNo(time);
        String billNo="";
        if(StrUtil.isNotEmpty(maxBuyNo)){
            String code=maxBuyNo.substring(14);
            Long number=Long.parseLong(code);
            number=number+1;
            String str=number.toString();
            int num=str.length();
            for (int i=0;i<(5-num);i++){
                str="0"+str;
            }
            billNo=time+"-"+(str);
        }else{
            billNo=time+"-"+"00001";
        }
        map.put("billNo",billNo);
        map.put("billDate",bartDateFormat2.format(date));
        map.put("userId",curUserDto.getUserId());
        map.put("userName",curUserDto.getNikeName());
        return map;
    }

    @Override
    @Transactional
    public PageUtils<RewardSchemeVo> searchRewardScheme(RewardSchemeParam rewardSchemeParam) {
        updateStatus();
        IPage<RewardSchemeVo> page = this.baseMapper.searchRewardScheme(new Page<>(rewardSchemeParam.getCurrent(), rewardSchemeParam.getSize()), rewardSchemeParam);
        return new PageUtils<>(page);
    }

    @Override
    public RewardSchemeVo getDetail(Long id) {

        RewardSchemeVo rewardSchemeVo = this.baseMapper.getRewardScheme(id);
        List<RewardSchemeDetVo>list = rewardSchemeDetMapper.getRewardSchemeDet(id);
        rewardSchemeVo.setList(list);
        return rewardSchemeVo;
    }

    @Override
    @Transactional
    public void approval(RewardSchemeApprovalDto rewardSchemeApprovalDto) {

        Integer approvalStatus = rewardSchemeApprovalDto.getApprovalStatus();
        String approvalReason = rewardSchemeApprovalDto.getApprovalReason();
        String ids = rewardSchemeApprovalDto.getIds();
        if(StringUtils.isEmpty(ids)){
            throw new ServiceException("奖励方案id不能为空");
        }

        if(approvalStatus== ApproveStatusEnum.REJECT.getStatus()&&StringUtils.isEmpty(approvalReason)){
            throw new ServiceException("拒绝审核，审核原因不能为空");
        }

        for (String id : ids.split(",")) {
            RewardScheme rewardScheme = baseMapper.selectById(id);
            if(rewardScheme == null){
                throw new ServiceException("奖励方案不存在");
            }
            //状态为失效，并且审核状态为待审核才能审核
            if(rewardScheme.getApprovalStatus() == ApproveStatusEnum.AUDIT.getStatus()&&rewardScheme.getStatus() == RewardSchemeEnum.NO.getStatus()){
                rewardScheme.setApprovalStatus(approvalStatus);
                rewardScheme.setApprovalReason(approvalReason);
                rewardScheme.setApprovalTime(LocalDateTime.now());
                baseMapper.updateById(rewardScheme);
            }
        }

    }

    @Override
    @Transactional
    public void stop(RewardSchemeDto rewardSchemeDto) {
        Long id = rewardSchemeDto.getId();
        Integer status = rewardSchemeDto.getStatus();
        if(id == null){
            throw new ServiceException("奖励方案id不能为空");
        }

        if(status == null){
            throw new ServiceException("奖励方案状态不能为空");
        }
        RewardScheme rewardScheme = baseMapper.selectById(id);

        if(rewardScheme == null){
            throw new ServiceException("奖励方案不存在");
        }
        rewardScheme.setStatus(status);
        baseMapper.updateById(rewardScheme);
    }

    @Override
    @Transactional
    public void copy(RewardSchemeCopyDto rewardSchemeCopyDto) {
        String ids = rewardSchemeCopyDto.getIds();
        if(StringUtils.isEmpty(ids)){
            throw new ServiceException("奖励方案id不能为空");
        }
        String billNo = "";
        SimpleDateFormat bartDateFormat = new SimpleDateFormat("yyyyMMdd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String time="JLFA-"+bartDateFormat.format(date);
        for (String id : ids.split(",")) {
            RewardSchemeVo rewardSchemeVo = getDetail(Long.valueOf(id));
            if(rewardSchemeVo == null){
                throw new ServiceException("奖励方案不存在");
            }
            if(StringUtils.isEmpty(billNo)){
                Map<String, Object> receiptNoAndUser = getReceiptNoAndUser();
                billNo = String.valueOf(receiptNoAndUser.get("billNo"));
            }else {
                String code=billNo.substring(14);
                Long number=Long.parseLong(code);
                number=number+1;
                String str=number.toString();
                int num=str.length();
                for (int i=0;i<(5-num);i++){
                    str="0"+str;
                }
                billNo=time+"-"+(str);
            }
            RewardSchemeDto rewardSchemeDto = new RewardSchemeDto();
            BeanUtils.copyProperties(rewardSchemeVo,rewardSchemeDto);
            rewardSchemeDto.setBillNo(billNo);
            rewardSchemeDto.setId(null);
            rewardSchemeDto.setApprovalStatus(ApproveStatusEnum.AUDIT.getStatus());
            rewardSchemeDto.setStatus(RewardSchemeEnum.DRAFT.getStatus());
            if(StringUtils.isNotEmpty(rewardSchemeVo.getBillDate())){
                String str = rewardSchemeVo.getBillDate()+" 00:00:00";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setBillDate(dateTime);
            }
            if(StringUtils.isNotEmpty(rewardSchemeVo.getStartTime())){
                String str = rewardSchemeVo.getStartTime()+" 00:00:00";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setStartTime(dateTime);
            }
            if(StringUtils.isNotEmpty(rewardSchemeVo.getEndTime())){
                String str = rewardSchemeVo.getEndTime()+" 23:59:59";
                LocalDateTime dateTime = LocalDateTime.parse(str, formatter);
                rewardSchemeDto.setEndTime(dateTime);
            }
            List<RewardSchemeDetDto>dataList = new ArrayList<>();
            List<RewardSchemeDetVo> list = rewardSchemeDetMapper.getRewardSchemeDet(rewardSchemeVo.getId());
            for (RewardSchemeDetVo rewardSchemeDetVo : list) {
                RewardSchemeDetDto rewardSchemeDetDto = new RewardSchemeDetDto();
                BeanUtils.copyProperties(rewardSchemeDetVo,rewardSchemeDetDto);
                rewardSchemeDetDto.setId(null);
                dataList.add(rewardSchemeDetDto);
            }
            rewardSchemeDto.setList(dataList);
            add(rewardSchemeDto);
        }
    }

    @Override
    @Transactional
    public void updateStatus() {
        LambdaQueryWrapper<RewardScheme>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RewardScheme::getApprovalStatus,ApproveStatusEnum.APPROVED.getStatus());
        wrapper.eq(RewardScheme::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.ne(RewardScheme::getStatus,RewardSchemeEnum.STOP.getStatus());
        List<RewardScheme> list = baseMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (RewardScheme rewardScheme : list) {
                LocalDateTime startTime = rewardScheme.getStartTime();
                LocalDateTime endTime = rewardScheme.getEndTime();
                LocalDateTime now = LocalDateTime.now();

                int result1 = startTime.compareTo(now);
                int result2 = endTime.compareTo(now);

                if(result1<0&&result2>0){
                    rewardScheme.setStatus(RewardSchemeEnum.YES.getStatus());
                    baseMapper.updateById(rewardScheme);
                }else{
                    rewardScheme.setStatus(RewardSchemeEnum.NO.getStatus());
                    baseMapper.updateById(rewardScheme);
                }
            }
        }
    }
    @Override
    @Transactional
    public PageUtils<RewardSchemeShowVo> searchRewardSchemeDet(RewardSchemeShowParam rewardSchemeShowParam) {
        updateStatus();
        IPage<RewardSchemeShowVo> page = this.baseMapper.searchRewardSchemeDet(new Page<>(rewardSchemeShowParam.getCurrent(), rewardSchemeShowParam.getSize()), rewardSchemeShowParam);
        return new PageUtils<>(page);
    }

}
