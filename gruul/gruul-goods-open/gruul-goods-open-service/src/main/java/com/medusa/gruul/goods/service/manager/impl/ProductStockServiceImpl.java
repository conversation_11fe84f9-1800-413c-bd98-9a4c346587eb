package com.medusa.gruul.goods.service.manager.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.model.dto.manager.ProductStockDto;
import com.medusa.gruul.goods.api.model.dto.manager.SkuStockDto;
import com.medusa.gruul.goods.api.model.dto.manager.SkuStockWtoDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductStockVo;
import com.medusa.gruul.goods.api.model.vo.manager.SkuStockVo;
import com.medusa.gruul.goods.mapper.manager.ProductShowCategoryMapper;
import com.medusa.gruul.goods.mapper.manager.ProductStockMapper;
import com.medusa.gruul.goods.mapper.manager.SkuStockMapper;
import com.medusa.gruul.goods.service.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 商品库存
 * @Author: qsx
 * @Date:   2022-02-28
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProductStockServiceImpl extends ServiceImpl<ProductStockMapper, ProductStock> implements IProductStockService {
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private ISkuStockService skuStockService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IProductUnitService productUnitService;
    @Autowired
    private ProductShowCategoryMapper productShowCategoryMapper;
    @Autowired
    private SkuStockMapper skuStockMapper;



    @Override
    public IPage<ProductStockVo> productStockList( ProductStockDto productStockDto) {
        IPage<ProductStockVo> page = new Page<>(productStockDto.getCurrent(), productStockDto.getSize());
        IPage<ProductStock> iPage=this.baseMapper.selectProductStockList(new Page<>(productStockDto.getCurrent(), productStockDto.getSize()),productStockDto);
        List<ProductStock> productStockList= iPage.getRecords();
        List<ProductStockVo> vos=new ArrayList<>();
        //获取仓库、规格库存、商品Id
        List<Long> warehouseIdList = productStockList.stream().filter(s-> s.getWarehouseId() != null).map(ProductStock::getWarehouseId).collect(Collectors.toList());
        List<Long> skuIdList = productStockList.stream().filter(s-> s.getSkuId() != null).map(ProductStock::getSkuId).collect(Collectors.toList());
        List<Long> productIdList = productStockList.stream().filter(s-> s.getProductId() != null).map(ProductStock::getProductId).collect(Collectors.toList());
        //查询仓库
        //根据仓库Id获取仓库的信息
        List<Warehouse> warehouseList=new ArrayList<>();
        if(warehouseIdList!=null && warehouseIdList.size()>0){
            warehouseList=warehouseService.getByIdList(warehouseIdList);
        }

        Map<Long, Warehouse> warehouseMap = MapUtil.newHashMap(warehouseIdList.size());
        if (CollectionUtil.isNotEmpty(warehouseList)) {
            warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
        }
        //根据Id获取规格库存表的信息
        List<SkuStock> skuStockList=new ArrayList<>();
        if(skuIdList!=null && skuIdList.size()>0){
            skuStockList=skuStockService.getBySkuStockListId(skuIdList);
        }
        Map<Long, SkuStock> skuStockMap = MapUtil.newHashMap(skuIdList.size());
        if (CollectionUtil.isNotEmpty(skuStockList)) {
            skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        }
        //根据Id获取商品表的信息
        List<Product> productList=new ArrayList<>();
        if(productIdList!=null&&productIdList.size()>0){
            productList=productService.getByProductListId(productIdList);
        }

        Map<Long, Product> productMap = MapUtil.newHashMap(warehouseIdList.size());
        if (CollectionUtil.isNotEmpty(productList)) {
            productMap = productList.stream().collect(Collectors.toMap(Product::getId, v -> v));
        }
        //获取基本单位id
        List<Long> unitIdList = productList.stream().filter(s-> s.getUnitId()!= null && s.getUnitId() != 0).map(Product::getUnitId).collect(Collectors.toList());
        // 根据Id获取基本单位的信息
        List<ProductUnit> unitList=new ArrayList<>();
        if(unitIdList!=null&&unitIdList.size()>0){
            unitList=productUnitService.listByIds(unitIdList);
        }

        Map<Long, ProductUnit> unitMap = MapUtil.newHashMap(productIdList.size());
        if (CollectionUtil.isNotEmpty(unitList)) {
            unitMap = unitList.stream().collect(Collectors.toMap(ProductUnit::getId, v -> v));
        }
        setProductStockListVos(productStockList,vos,warehouseMap,skuStockMap,productMap,unitMap);
        page.setTotal(iPage.getTotal());
        return page.setRecords(vos);
    }



    /**
     * @param records 库存数据
     * @param vos     库存数据
     * @param warehouseMap 仓库信息
     *  @param skuStockMap 规格库存信息
     *   @param productMap 商品信息
     */
    private void setProductStockListVos(List<ProductStock> records, List<ProductStockVo> vos, Map<Long, Warehouse> warehouseMap,Map<Long, SkuStock> skuStockMap,Map<Long, Product> productMap,Map<Long, ProductUnit> unitMap) {
        for (ProductStock record : records) {
            ProductStockVo vo = new ProductStockVo();
            BeanUtils.copyProperties(record, vo);
            Warehouse warehouse=warehouseMap.get(record.getWarehouseId());
            SkuStock skuStock=skuStockMap.get(record.getSkuId());
            Product product=productMap.get(record.getProductId());
            ProductUnit unit=unitMap.get(product.getUnitId());
            BeanUtils.copyProperties(record, vo);
            if(warehouse!=null){
                vo.setWarehouseFullName(warehouse.getWarehouseFullName());
            }
           if(skuStock!=null){
               vo.setPic(skuStock.getPic());
               vo.setSpecs(skuStock.getSpecs());
               vo.setSpecs2(skuStock.getSpecs2());
               if(skuStock.getLinkProductId() != null){
                   Product linkProduct = this.productService.getById(skuStock.getLinkProductId());
                   if(null != linkProduct){
                       vo.setLinkGoodsName(linkProduct.getName());
                   }
               }
           }
           if(product!=null){
               vo.setGoodsName(product.getName());
               vo.setGoodsCode(product.getGoodsCode());
           }
            if(unit!=null){
                vo.setUnit(unit.getUnit());
            }

            vos.add(vo);
        }
    }


    @Override
    public IPage<SkuStockVo> skuStockList( SkuStockWtoDto skuStockWtoDto) {
        IPage<SkuStockVo> page = new Page<>(skuStockWtoDto.getCurrent(), skuStockWtoDto.getSize());
        List<Long> skuIdParamList=new ArrayList<>();
        List<Long> goodsIdList=new ArrayList<>();
        //仓库Id
        if (skuStockWtoDto.getWarehouseId() != null) {
            List<ProductStock> productStocks=this.baseMapper.selectList(new LambdaQueryWrapper<ProductStock>().in(ProductStock::getWarehouseId, skuStockWtoDto.getWarehouseId()));
            List<Long> skuIds= productStocks.stream().map(ProductStock::getSkuId).collect(Collectors.toList());
            //如果这个仓库中没有数据则返回空
            if(skuIds==null || skuIds.size()==0){
                return page.setRecords(null);
            }
            skuIdParamList.addAll(skuIds);
        }
        //商品名字
        if (StrUtil.isNotBlank(skuStockWtoDto.getGoodsName())) {
            List<Product> products=productService.list(new LambdaQueryWrapper<Product>().like(Product::getName, skuStockWtoDto.getGoodsName()));
            goodsIdList= products.stream().map(Product::getId).collect(Collectors.toList());
            //如果查无此商品则返回空
            if(products==null || products.size()==0){
                return page.setRecords(null);
            }
        }
        //商品编码
        if (StrUtil.isNotBlank(skuStockWtoDto.getGoodsCode())) {
            List<Product> products=productService.list(new LambdaQueryWrapper<Product>().like(Product::getGoodsCode, skuStockWtoDto.getGoodsCode()));
            List<Long> goodsIdsWto= products.stream().map(Product::getId).collect(Collectors.toList());
            //如果查无此商品编码则返回空
            if(goodsIdsWto==null || goodsIdsWto.size()==0){
                return page.setRecords(null);
            }
            goodsIdList.addAll(goodsIdsWto);
        }
        //分类
        if (skuStockWtoDto.getShowCategoryId() != null) {
            List<ProductShowCategory> productShowCategorys=productShowCategoryMapper.selectList(new LambdaQueryWrapper<ProductShowCategory>().in(ProductShowCategory::getShowCategoryId, skuStockWtoDto.getShowCategoryId()));
            List<Long> productIds= productShowCategorys.stream().map(ProductShowCategory::getProductId).collect(Collectors.toList());
            //如果没有该分类则返回空
            if(productIds==null || productIds.size()==0){
                return page.setRecords(null);
            }
            goodsIdList.addAll(productIds);
        }
        //去除商品Id重复值
        if(goodsIdList!=null &&goodsIdList.size()>0) {
            goodsIdList = goodsIdList.stream().distinct().collect(Collectors.toList());
        }
        //去除规格库存Id重复值
        if(skuIdParamList!=null &&skuIdParamList.size()>0) {
            skuIdParamList = skuIdParamList.stream().distinct().collect(Collectors.toList());
        }
        LambdaQueryWrapper<SkuStock> lambdaQueryWrapper=new LambdaQueryWrapper<SkuStock>();
        if(goodsIdList!=null && goodsIdList.size()>0) {
            lambdaQueryWrapper.in(SkuStock::getProductId,goodsIdList);
        }
        if(skuIdParamList!=null && skuIdParamList.size()>0) {
            lambdaQueryWrapper.in(SkuStock::getId,skuIdParamList);
        }
        if(StrUtil.isNotBlank(skuStockWtoDto.getSpecs())){
            lambdaQueryWrapper.like(SkuStock::getSpecs,skuStockWtoDto.getSpecs());
        }
        IPage<SkuStock> iPage =skuStockService.page((new Page<>(skuStockWtoDto.getCurrent(), skuStockWtoDto.getSize())),lambdaQueryWrapper);
        List<SkuStock> records = iPage.getRecords();
        List<SkuStockVo> vos =new ArrayList<SkuStockVo>();
        if(records!=null&&records.size()>0){
            vos=getSkSkuStockVoList(records);
        }
        page.setTotal(iPage.getTotal());
        return page.setRecords(vos);
    }

    @Override
    public IPage<SkuStockVo> queryByProductId(SkuStockWtoDto skuStockWtoDto) {
        IPage<SkuStockVo> page = new Page<>(skuStockWtoDto.getCurrent(), skuStockWtoDto.getSize());
        IPage<SkuStock> iPage =skuStockService.page((new Page<>(skuStockWtoDto.getCurrent(), skuStockWtoDto.getSize())),new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getProductId, skuStockWtoDto.getProductId()));
        List<SkuStock> records = iPage.getRecords();
        List<SkuStockVo> vos =new ArrayList<SkuStockVo>();
        if(records!=null&&records.size()>0){
            vos=getSkSkuStockVoList(records);
        }
        return page.setRecords(vos);
    }

    @Override
    public void updateProductStock(List<ProductStock> productStockList) {
        //现在不需要库存明细表 先注释掉

//        //获取商品库存Id
//        List<Long> productStockIdList = productStockList.stream().map(ProductStock::getId).collect(Collectors.toList());
//        //获取仓库Id
//        List<Long> warehouseIdList = productStockList.stream().map(ProductStock::getWarehouseId).collect(Collectors.toList());
//        Map<Long, ProductStock> productStockMap = MapUtil.newHashMap(productStockIdList.size());
//        //根据仓库id、商品库存id查询商品库存
//        List<ProductStock> productStocks= productStockService.list(new LambdaQueryWrapper<ProductStock>().in(ProductStock::getWarehouseId,warehouseIdList).in(ProductStock::getId, productStockIdList));
//        if (CollectionUtil.isNotEmpty(productStocks)) {
//            productStockMap = productStocks.stream().collect(Collectors.toMap(ProductStock::getId, v -> v));
//        }
//        for(ProductStock productStock:productStockList){
//            //仓库不为空时候才往库存表加记录或者修改
//            if(productStock.getWarehouseId()!=null&&productStock.getStock()!=null){
//                //获取上一次同一个商品规格同一个仓库的库存信息
//                ProductStock newPproductStock =productStockMap.get(productStock.getId());
//                ProductStockDetailed productStockDetailed=new ProductStockDetailed();
//                if(productStock.getId()!=null) {
//                    productStockService.updateById(productStock);
//                    if(newPproductStock!=null){
//                        productStockDetailed.setLastQuantity(newPproductStock.getStock());
//                        productStockDetailed.setChangeQuantity(productStock.getStock().subtract(newPproductStock.getStock()));
//                    }else{
//                        productStockDetailed.setChangeQuantity(productStock.getStock());
//                    }
//                }else{
//                    productStockService.save(productStock);
//                    productStockDetailed.setChangeQuantity(productStock.getStock());
//                }
//                //库存不为空，并且不等于上次的库存数量则添加库存变更明细或者是新增则添加明细记录
//                boolean fl=productStock.getStock()!=null&&(newPproductStock==null || (newPproductStock!=null && !Objects.equals( productStock.getStock(),newPproductStock.getStock())));
//                if(fl){
//                    productStockDetailed.setSkuId(productStock.getSkuId());
//                    productStockDetailed.setProductId(productStock.getProductId());
//                    productStockDetailed.setWarehouseId(productStock.getWarehouseId());
//                    productStockDetailed.setTotalQuantity(productStock.getStock());
//                    productStockDetailedService.save(productStockDetailed);
//                }
//            }
//        }
    }

    /**
     * @param records 规格库存数据
     * @param vos     规格库存数据
     * @param warehouseMap 仓库信息
     *  @param productStockMap 库存信息
     *   @param productMap 商品信息
     */
    private void setSkuStockListVos(List<SkuStock> records, List<SkuStockVo> vos, Map<Long, Warehouse> warehouseMap, Map<Long, ProductStock> productStockMap, Map<Long, Product> productMap, Map<Long, ProductUnit> unitMap) {
        for (SkuStock record : records) {
            SkuStockVo vo = new SkuStockVo();
            BeanUtils.copyProperties(record, vo);
            //库存表
            ProductStock productStock= productStockMap.get(record.getId());
            if(productStock!=null){
                Warehouse warehouse=warehouseMap.get(productStock.getWarehouseId());
                vo.setWarehouseFullName(warehouse.getWarehouseFullName());
                vo.setWarehouseId(warehouse.getId());
                vo.setProductStock(productStock.getStock());
                vo.setProductStockId(productStock.getId());
            }
           //商品表
            Product product=productMap.get(record.getProductId());
            vo.setGoodsName(product.getName());
            vo.setGoodsCode(product.getGoodsCode());
            //辅助单位表
            ProductUnit unit=unitMap.get(product.getUnitId());
            if(unit!=null){
                vo.setUnit(unit.getUnit());
            }
            vos.add(vo);
        }
    }

    public List<SkuStockVo> getSkSkuStockVoList(List<SkuStock> records) {
        List<SkuStockVo> vos = new LinkedList<>();
        //获取库存表信息
        //获取规格库存Id
        List<Long> skuIdList = records.stream().map(SkuStock::getId).collect(Collectors.toList());
        //根据规格库存Id获取库存表
        List<ProductStock> productStockList=this.getBaseMapper().selectList(new LambdaQueryWrapper<ProductStock>().in(ProductStock::getSkuId, skuIdList));
        Map<Long, ProductStock> productStockMap = MapUtil.newHashMap(productStockList.size());
        if (CollectionUtil.isNotEmpty(productStockList)) {
            productStockMap = productStockList.stream().collect(Collectors.toMap(ProductStock::getSkuId, v -> v));
        }
        //获取仓库表信息
        //获取仓库Id
        List<Long> warehouseIdList = productStockList.stream().map(ProductStock::getWarehouseId).collect(Collectors.toList());
        //根据仓库Id获取仓库的信息
        List<Warehouse> warehouseList=new ArrayList<>();
        if(warehouseIdList!=null && warehouseIdList.size()>0){
            warehouseList=warehouseService.getByIdList(warehouseIdList);
        }
        Map<Long, Warehouse> warehouseMap = MapUtil.newHashMap(warehouseIdList.size());
        if (CollectionUtil.isNotEmpty(warehouseList)) {
            warehouseMap = warehouseList.stream().collect(Collectors.toMap(Warehouse::getId, v -> v));
        }
        //获取商品表信息
        //获取商品Id
        List<Long> productIdList = records.stream().map(SkuStock::getProductId).collect(Collectors.toList());
        //根据Id获取商品表的信息
        List<Product> productList=productService.getByProductListId(productIdList);
        Map<Long, Product> productMap = MapUtil.newHashMap(productIdList.size());
        if (CollectionUtil.isNotEmpty(productList)) {
            productMap = productList.stream().collect(Collectors.toMap(Product::getId, v -> v));
        }
        //获取基本单位id
        List<Long> unitIdList = productList.stream().map(Product::getUnitId).collect(Collectors.toList());
        // 根据Id获取基本单位的信息
        List<ProductUnit> unitList=productUnitService.listByIds(unitIdList);
        Map<Long, ProductUnit> unitMap = MapUtil.newHashMap(productIdList.size());
        if (CollectionUtil.isNotEmpty(unitList)) {
            unitMap = unitList.stream().collect(Collectors.toMap(ProductUnit::getId, v -> v));
        }
        setSkuStockListVos(records,vos,warehouseMap,productStockMap,productMap,unitMap);
        return vos;
    }

    /**
     * 与外部系统对接商品库存
     * @param productStockDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long outProductStock(ProductStockDto productStockDto){
        SkuStock skuStock = null;
//        Long productId = productStockDto.getProductId();
//        if(productId == null){
//            throw new ServiceException("商品id不能为空");
//        }
        log.info("对接库存入参：{}", JSON.toJSONString(productStockDto));
        String goodsCode = productStockDto.getGoodsCode();
        if(StrUtil.isEmpty(goodsCode)){
            throw new ServiceException("商品标识不能为空");
        }
        LambdaQueryWrapper<Product>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getClassCode,goodsCode);
        List<Product> list = productService.list(wrapper);
        Product product = null;
        if(list!=null&&list.size()>0){
            product = list.get(0);
        }
        if(product == null){
            log.error("商品不存在");
            return 0L;
        }
        productStockDto.setProductId(product.getId());
        //1、没传仓库过来，按照总库存进行对接；2、传仓库过来，按照分仓库存对接
        //1、没传仓库过来，传过来的库存即为规格库存，也是仓库库存
        if(StrUtil.isEmpty(productStockDto.getStockCode())){
            // -- 未做规格2-颜色的处理
            //查出规格总库存
            String spec = productStockDto.getSpecs();
            spec = spec == null ? "" : spec;
            LambdaQueryWrapper<SkuStock> skuStockWrapper = new LambdaQueryWrapper<>();
            if(StrUtil.isNotBlank(spec)){
                skuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, spec);
            }else{
                skuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, "");
            }

            List<SkuStock> skuStockList = this.skuStockMapper.selectList(skuStockWrapper);
            if(CollectionUtil.isEmpty(skuStockList)){
                log.error("商品对应的规格库存记录不存在");
                return 0L;
            }
            skuStock = skuStockList.get(0);
            //修改总库存
            skuStock.setStock(productStockDto.getStock());


            //查询规格商品仓库库存
            LambdaQueryWrapper<ProductStock> productStockWrapper = new LambdaQueryWrapper<>();
            productStockWrapper.eq(ProductStock::getProductId, productStockDto.getProductId()).eq(ProductStock::getSkuId, skuStock.getId());
            List<ProductStock> productStockList = this.baseMapper.selectList(productStockWrapper);

            ProductStock productStock = null;
            if(CollectionUtil.isEmpty(productStockList)){
                //记录不存在
                productStock = new ProductStock();
                productStock.setStock(productStockDto.getStock())
                        .setProductId(productStockDto.getProductId())
                        .setSkuId(skuStock.getId());
            }else{
                productStock = productStockList.get(0);
            }

            //productStock = productStockList.get(0);
            //修改规格商品仓库库存
            productStock.setStock(productStockDto.getStock());

            skuStockService.updateById(skuStock);

            List<SkuStockDto> skuStockDtos = new ArrayList<>(1);
            SkuStockDto skuStockDto = new SkuStockDto();
            BeanUtil.copyProperties(skuStock, skuStockDto);
            skuStockDtos.add(skuStockDto);
            //修改sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
            this.productService.updateSkuStock(skuStockDtos, productStockDto.getProductId());
            if(productStock.getId() == null){
                this.baseMapper.insert(productStock);
            }else{
                this.baseMapper.updateById(productStock);
            }
        }else{

            LambdaQueryWrapper<Warehouse>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Warehouse::getClassCode,productStockDto.getStockCode());
            List<Warehouse> warehouseList = warehouseService.list(queryWrapper);
            if(warehouseList!=null&&warehouseList.size()>0){
                Warehouse warehouse = warehouseList.get(0);
                productStockDto.setWarehouseId(warehouse.getId());
            }else{
                log.error("仓库不存在");
                return 0L;
            }

            //传仓库过来，按照分仓库存对接
            //1.先查出规格总库存
            String spec = productStockDto.getSpecs();
            spec = spec == null ? "" : spec;
            String spec2 = productStockDto.getSpecs2();
            spec2 = spec2 == null ? "" : spec2;
            LambdaQueryWrapper<SkuStock> skuStockWrapper = new LambdaQueryWrapper<>();
            if(StrUtil.isNotBlank(spec)){
                skuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, spec);
            }else{
                skuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, "");
            }
            if(StrUtil.isNotBlank(spec2)){
                skuStockWrapper.eq(SkuStock::getSpecs2, spec2);
            }else{
                skuStockWrapper.eq(SkuStock::getSpecs2, "");
            }
            List<SkuStock> skuStockList = this.skuStockMapper.selectList(skuStockWrapper);
            if(CollectionUtil.isEmpty(skuStockList)){
                log.info("{}商品对应的规格库存记录不存在，插入新规格记录。", productStockDto.getProductId());
                // 如果存在一条规格1的值一样，但是规格2却为空的规格记录，则用此记录来更新规格2的值
                // （通信行业从易达传输过来的商品规格1、规格2都为空，这时候从库存里查出一个黑色的商品，如果直接新增一个规格，会导致商城里多一条两个规格都为空的记录）
                LambdaQueryWrapper<SkuStock> dbSkuStockWrapper = new LambdaQueryWrapper<>();
                if(StrUtil.isNotBlank(spec)){
                    dbSkuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, spec)
                            .and( i ->  i.eq(SkuStock::getSpecs2, "").or().isNull(SkuStock::getSpecs2));
                }else{
                    dbSkuStockWrapper.eq(SkuStock::getProductId, productStockDto.getProductId()).eq(SkuStock::getSpecs, "")
                            .and( i ->  i.eq(SkuStock::getSpecs2, "").or().isNull(SkuStock::getSpecs2));
                }
                List<SkuStock> dbSkuStockList = this.skuStockMapper.selectList(dbSkuStockWrapper);
                if(CollectionUtil.isNotEmpty(dbSkuStockList)){
                    SkuStock dbSkuStock = dbSkuStockList.get(0);
                    dbSkuStock.setSpecs2(spec2);
                    this.skuStockMapper.updateById(dbSkuStock);
                }else{
                    //规格不存在，新增规格
                    //商品sku信息新增
                    List<SkuStockDto> skuStockDtos = new ArrayList<>();

                    SkuStockDto skuStockDto = new SkuStockDto();
                    skuStockDto.setSpecs(StrUtil.isNotEmpty(productStockDto.getSpecs()) ? productStockDto.getSpecs() : "");
                    skuStockDto.setSpecs2(StrUtil.isNotEmpty(productStockDto.getSpecs2()) ? productStockDto.getSpecs2() : "");
                    skuStockDtos.add(skuStockDto);
                    if (CollectionUtil.isNotEmpty(skuStockDtos)) {
                        this.productService.addSkuStock(skuStockDtos, product.getId());
                    }
                }

            }
            skuStockList = this.skuStockMapper.selectList(skuStockWrapper);
            if(CollectionUtil.isEmpty(skuStockList)){
                log.info("{}商品对应的规格库存记录不存在", productStockDto.getProductId());
                return 0L;
            }
            skuStock = skuStockList.get(0);

            //2.查询规格商品仓库库存
            LambdaQueryWrapper<ProductStock> productStockWrapper = new LambdaQueryWrapper<>();
            productStockWrapper.eq(ProductStock::getProductId, productStockDto.getProductId()).eq(ProductStock::getWarehouseId, productStockDto.getWarehouseId())
                    .eq(ProductStock::getSkuId, skuStock.getId());
            List<ProductStock> productStockList = this.baseMapper.selectList(productStockWrapper);
            ProductStock productStock = null;
            if(CollectionUtil.isEmpty(productStockList)){
                //记录不存在
                productStock = new ProductStock();
                productStock.setStock(BigDecimal.ZERO)
                        .setProductId(productStockDto.getProductId())
                        .setSkuId(skuStock.getId())
                        .setWarehouseId(productStockDto.getWarehouseId());
            }else{
                productStock = productStockList.get(0);
            }

            BigDecimal dbStock = productStock.getStock();
            productStock.setStock(productStockDto.getStock());

            BigDecimal newSkuStock = skuStock.getStock().subtract(dbStock).add(productStockDto.getStock());
            skuStock.setStock(newSkuStock);

            List<SkuStockDto> skuStockDtos = new ArrayList<>(1);
            SkuStockDto skuStockDto = new SkuStockDto();
            BeanUtil.copyProperties(skuStock, skuStockDto);
            skuStockDtos.add(skuStockDto);
            //修改sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
            this.productService.updateSkuStock(skuStockDtos, productStockDto.getProductId());

            if(productStock.getId() == null){
                this.baseMapper.insert(productStock);
            }else{
                this.baseMapper.updateById(productStock);
            }
            // 同步更新关联了此商品对应商品的库存
            // 获取规格id对应的被关联的规格库存记录
            List<SkuStock> linkedSkuStockList = skuStockService.list(new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getLinkSkuId,skuStock.getId()));
            // 将关联商品的库存信息组装成规格id-库存对象的map记录
            Map<Long, SkuStock> linkedSkuStockMap=new HashMap<>();
            // 将商品规格记录组装成被关联规格id-规格id的map记录
            Map<Long, List<Long>> linkedSkuIdMap=new HashMap<>();
            if(CollectionUtil.isNotEmpty(linkedSkuStockList)){
                linkedSkuStockMap = linkedSkuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
                linkedSkuIdMap = linkedSkuStockList.stream()
                        .collect(Collectors.groupingBy(SkuStock::getLinkSkuId, Collectors.mapping(SkuStock::getId, Collectors.toList())));
            }
            //修改关联了此规格的商品规格库存记录的库存
            if(!linkedSkuIdMap.isEmpty()){
                List<Long> linkedSkuIdList2=linkedSkuIdMap.get(skuStock.getId());
                for(Long linkedSkuId:linkedSkuIdList2){
                    SkuStock linkedSkuStock=linkedSkuStockMap.get(linkedSkuId);
                    //规格库存的数量
                    linkedSkuStock.setStock(skuStock.getStock());

                    List<SkuStockDto> skuStockDtos2 = new ArrayList<>(1);
                    SkuStockDto skuStockDto2 = new SkuStockDto();
                    BeanUtil.copyProperties(linkedSkuStock, skuStockDto2);
                    skuStockDtos2.add(skuStockDto2);
                    //修改sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
                    this.productService.updateSkuStock(skuStockDtos2, linkedSkuStock.getProductId());

                    // 修改仓库商品规格库存
                    LambdaQueryWrapper<ProductStock> productStockWrapper2 = new LambdaQueryWrapper<>();
                    productStockWrapper2.eq(ProductStock::getProductId, linkedSkuStock.getProductId()).eq(ProductStock::getWarehouseId, productStockDto.getWarehouseId())
                            .eq(ProductStock::getSkuId, linkedSkuStock.getId());
                    List<ProductStock> productStockList2 = this.baseMapper.selectList(productStockWrapper2);
                    ProductStock productStock2 = null;
                    if(CollectionUtil.isEmpty(productStockList2)){
                        //记录不存在
                        productStock2 = new ProductStock();
                        productStock2.setStock(BigDecimal.ZERO)
                                .setProductId(linkedSkuStock.getProductId())
                                .setSkuId(linkedSkuStock.getId())
                                .setWarehouseId(productStockDto.getWarehouseId());
                    }else{
                        productStock2 = productStockList2.get(0);
                    }
                    productStock2.setStock(productStockDto.getStock());
                    if(productStock2.getId() == null){
                        this.baseMapper.insert(productStock2);
                    }else{
                        this.baseMapper.updateById(productStock2);
                    }
                }
            }

        }
        // 上述过程中，如果添加了规格库存记录（skuStock）,则需要判断如果此商品有多条规格库存记录，且商品标识还是统一规格，则要将商品的标识改为统一限购
        if(0 == product.getLimitType()){
            // 统一规格，判断是否有多条规格库存记录
            LambdaQueryWrapper<SkuStock> skuStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
            skuStockLambdaQueryWrapper.eq(SkuStock::getProductId, product.getId());
            List<SkuStock> skuStockList = skuStockService.list(skuStockLambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(skuStockList) && skuStockList.size() > 1){
                // 多条规格库存记录，将商品标识改为多规格
                product.setLimitType(1);
                this.productService.updateById(product);
            }
        }

        return skuStock.getId();
    }

}
