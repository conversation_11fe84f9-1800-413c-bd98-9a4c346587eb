package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.enums.EnableStatusEnum;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.enums.RuleTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.goods.api.entity.IntegralRule;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralRuleDto;
import com.medusa.gruul.goods.api.model.vo.manager.IntegralRuleVo;
import com.medusa.gruul.goods.mapper.manager.IntegralRuleMapper;
import com.medusa.gruul.goods.service.manager.IIntegralRuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: plh
 * @Description: 积分规则服务实现类
 * @Date: Created in 15:43 2023/8/18
 */
@Service
public class IntegralRuleServiceImpl extends ServiceImpl<IntegralRuleMapper, IntegralRule>implements IIntegralRuleService {


    @Override
    public List<IntegralRuleVo> getList() {

        return this.baseMapper.getList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(List<IntegralRuleDto> list) {
        for (IntegralRuleDto integralRuleDto : list) {
            Long id = integralRuleDto.getId();
            Integer ruleType = integralRuleDto.getRuleType();
            if(ruleType==null){
                throw new ServiceException("积分规则类型不能为空");
            }
            if(integralRuleDto.getEnableStatus()==null){
                throw new ServiceException("积分规则启用状态不能为空");
            }

            if(ruleType== RuleTypeEnum.PAY_ORDER.getSaleMode()){//消费获积分
                if(integralRuleDto.getIntegral()==null){
                    throw new ServiceException("消费获积分积分不能为空");
                }
                if(integralRuleDto.getAmount()==null){
                    throw new ServiceException("消费获积分金额不能为空");
                }
                integralRuleDto.setRuleName(RuleTypeEnum.PAY_ORDER.getDesc());
            }

            if(ruleType== RuleTypeEnum.LOGIN_USER.getSaleMode()){//发展下级
                if(integralRuleDto.getParentIntegral()==null){
                    throw new ServiceException("发展下级二级积分不能为空");
                }
                if(integralRuleDto.getAboveParentIntegral()==null){
                    throw new ServiceException("发展下级一级积分不能为空");
                }
                integralRuleDto.setRuleName(RuleTypeEnum.LOGIN_USER.getDesc());
            }
            if(ruleType== RuleTypeEnum.NEW_USER.getSaleMode()){//新用户注册获取积分
                if(integralRuleDto.getIntegral()==null){
                    throw new ServiceException("新用户注册积分不能为空");
                }
                integralRuleDto.setRuleName(RuleTypeEnum.NEW_USER.getDesc());
            }
            if(ruleType== RuleTypeEnum.LOGIN_DAY.getSaleMode()){//每天登录获取积分
                if(integralRuleDto.getIntegral()==null){
                    throw new ServiceException("每天登录积分不能为空");
                }
                integralRuleDto.setRuleName(RuleTypeEnum.LOGIN_DAY.getDesc());
            }
            if(ruleType== RuleTypeEnum.PAY_TICKET.getSaleMode()){//购买通惠证获取积分
                if(integralRuleDto.getIntegral()==null){
                    throw new ServiceException("购买通惠证积分不能为空");
                }
                if(integralRuleDto.getAmount()==null){
                    throw new ServiceException("购买通惠证金额不能为空");
                }
                integralRuleDto.setRuleName(RuleTypeEnum.PAY_TICKET.getDesc());
            }
            if(ruleType== RuleTypeEnum.DESCRIPTION.getSaleMode()){
                if(integralRuleDto.getDescription()==null){
                    throw new ServiceException("积分规则说明不能为空");
                }
                integralRuleDto.setEnableStatus(EnableStatusEnum.YES.getStatus());
            }

            if(id!=null){//编辑
                IntegralRule integralRule = this.getById(id);
                if(integralRule==null){
                    throw new ServiceException("积分规则不存在！");
                }
                BeanUtils.copyProperties(integralRuleDto,integralRule);
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                integralRule.setLastModifyUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                integralRule.setLastModifyUserName(pcUserInfoDto.getNikeName());
                this.baseMapper.updateById(integralRule);
            }else{//新增
                //删除记录
                LambdaQueryWrapper<IntegralRule>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(IntegralRule::getRuleType,ruleType);
                IntegralRule integralRuleOld = this.getOne(wrapper);
                if(integralRuleOld != null){
                    if(ruleType==RuleTypeEnum.DESCRIPTION.getSaleMode()){
                        throw new ServiceException("积分规则说明已存在！");
                    }
                    if(ruleType==RuleTypeEnum.LOGIN_USER.getSaleMode()){
                        throw new ServiceException("发展下级规则已存在！");
                    }
                    if(ruleType==RuleTypeEnum.PAY_ORDER.getSaleMode()){
                        throw new ServiceException("消费获积分规则已存在！");
                    }
                    if(ruleType==RuleTypeEnum.NEW_USER.getSaleMode()){
                        throw new ServiceException("新用户注册获取积分规则已存在！");
                    }
                    if(ruleType==RuleTypeEnum.LOGIN_DAY.getSaleMode()){
                        throw new ServiceException("每天登录获取积分规则已存在！");
                    }
                    if(ruleType==RuleTypeEnum.PAY_TICKET.getSaleMode()){
                        throw new ServiceException("购买通惠证获取积分规则已存在！");
                    }
                }
                IntegralRule integralRule = new IntegralRule();
                BeanUtils.copyProperties(integralRuleDto,integralRule);
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                integralRule.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                integralRule.setCreateUserName(pcUserInfoDto.getNikeName());
                this.baseMapper.insert(integralRule);
            }
        }
    }

    @Override
    public IntegralRuleVo getIntegralRuleVoByRuleType(Integer ruleType,String tenantId) {
        TenantContextHolder.setTenantId(tenantId);
        IntegralRuleVo integralRuleVo = this.baseMapper.getIntegralRuleVo(ruleType);
        if(integralRuleVo!=null){
            Integer enableStatus = integralRuleVo.getEnableStatus();
            if(enableStatus==null||enableStatus== EnableStatusEnum.NO.getStatus()){
                throw new ServiceException("积分规则没有启用！");
            }
        }else{
            throw new ServiceException("积分规则不存在！");
        }
        return this.baseMapper.getIntegralRuleVo(ruleType);
    }
}
