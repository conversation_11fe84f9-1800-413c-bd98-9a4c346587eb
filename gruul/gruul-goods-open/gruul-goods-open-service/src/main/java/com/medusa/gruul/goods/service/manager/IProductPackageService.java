package com.medusa.gruul.goods.service.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.goods.api.entity.ProductPackage;
import com.medusa.gruul.goods.api.model.param.manager.ProductPackageDetailParam;
import com.medusa.gruul.goods.api.model.vo.manager.ProductAllPackageVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductPackageDetailVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:03 2024/9/3
 */
public interface IProductPackageService extends IService<ProductPackage> {

    /**
     * 获取权益包包含商品信息
     * @param packageId
     * @return
     */
    List<ProductAllPackageVo> findProductPackageByPackageId(Long packageId);

    /**
     * 分页获取权益包明细
     * @param param
     * @return
     */
    PageUtils<ProductPackageDetailVo> getProductPackageDetailVo(ProductPackageDetailParam param);

    /**
     * 导出权益包明细
     * @param param
     */
    void exportProductPackageDetailVo(ProductPackageDetailParam param);
}
