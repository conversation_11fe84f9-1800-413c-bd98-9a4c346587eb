package com.medusa.gruul.goods.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.vo.MemberLevelVo;
import com.medusa.gruul.account.api.enums.ExternalAccountEnum;
import com.medusa.gruul.afs.api.feign.RemoteAfsService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.SendStatusEnum;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.encrypt.AESUtil;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.goods.api.constant.GoodsProductRedisKey;
import com.medusa.gruul.goods.api.constant.GoodsSkuStockRedisKey;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.model.dto.manager.*;
import com.medusa.gruul.goods.api.model.param.manager.*;
import com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartProductVo;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.mapper.api.ApiMemberGoodsAgainPriceMapper;
import com.medusa.gruul.goods.mapper.api.ApiMemberGoodsPriceMapper;
import com.medusa.gruul.goods.mapper.manager.*;
import com.medusa.gruul.goods.service.manager.*;
import com.medusa.gruul.goods.util.CsvFileUtil;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.goods.web.enums.SaleModeEnum;
import com.medusa.gruul.order.api.entity.OrderSetting;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.ProductRateVo;
import com.medusa.gruul.oss.api.feign.RemoteSysOssService;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.enums.MiniShowProductTypeEnum;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.ShopsPartnerCategoryVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 商品信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private CsvProductMapper csvProductMapper;

    @Autowired
    private ProductAttributeMapper productAttributeMapper;

    @Autowired
    private ShowCategoryMapper showCategoryMapper;

    @Autowired
    private SkuStockMapper skuStockMapper;

    @Autowired
    private ProductShowCategoryMapper productShowCategoryMapper;

    @Autowired
    private DiscountProductMapper discountProductMapper;

    @Autowired
    private ApiMemberGoodsPriceMapper apiMemberGoodsPriceMapper;

    @Autowired
    private ApiMemberGoodsAgainPriceMapper apiMemberGoodsAgainPriceMapper;

    @Resource
    private RemoteOrderService remoteOrderService;

    @Resource
    private RemoteSysOssService remoteSysOssService;

    @Resource
    private RemoteAfsService remoteAfsService;

    @Autowired
    private ProductSecUnitServiceImpl productSecUnitService;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private ISaleModeService saleModeService;
    @Autowired
    private ProductUnitMapper productUnitMapper;
    @Autowired
    private ProductStockMapper productStockMapper;
    @Autowired
    private IIntegralActivityService integralActivityService;
    @Autowired
    private IIntegralProductService integralProductService;
    @Autowired
    private RemoteShopsService remoteShopsService;

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private ProductPackageMapper productPackageMapper;

    @Autowired
    private PackageCouponMapper packageCouponMapper;

    @Override
    public void exportProduct(ProductParam productParam) {
        // 设置导出最大数量
        Page page = HuToolExcelUtils.exportParamToMax(productParam);
        List<ProductExcelVo> list = this.baseMapper.exportProductList(page, productParam);
        HuToolExcelUtils.exportData(list, "商品列表",null);
    }
    @Override
    public void exportProductPackageList(ProductParam productParam) {
        // 设置导出最大数量
        Page page = HuToolExcelUtils.exportParamToMax(productParam);
        List<ProductPackageExcelVo> list = this.baseMapper.exportProductPackageList(page, productParam);
        HuToolExcelUtils.exportData(list, "权益包列表",source->{
            if (source.getPackageStartTime()!=null && source.getPackageEndTime()!=null){
                source.setDurationTime(source.getPackageStartTime()+"-"+source.getPackageEndTime());
            }
            if (source.getStatus()!= null){
                source.setStatusDict(source.getStatus()==1?"上架":"下架");
            }
            return source;
        });
    }
    /**
     * 获取商品分页信息
     *
     * @param productParam
     * @return 商品分页对象
     */
    @Override
    public IPage<ProductVo> getProductList(ProductParam productParam) {
        IPage<ProductVo> page = new Page<>(productParam.getCurrent(), productParam.getSize());
        List<ProductVo> productVos = this.baseMapper.queryProductList(page, productParam);
        List<ProductVo> productVoList = new ArrayList<>();
        //获取启用的会员等级
        List<MemberLevelVo> memberLevelList = remoteMiniAccountService.getMemberLevelList();
        Map<String,Object> map=new HashMap();
        //给商品规格绑定会员等级(用于会员价)
        for(ProductVo productVo:productVos){
            ProductVo newProductVo=new ProductVo();
            List<SkuStockMemberPriceVo> memberLevePriceVos=productVo.getSkuStockMemberPriceVos();
            if(StringUtil.isNotEmpty(productVo.getPackageStartTime())){
                productVo.setPackageStartTime(productVo.getPackageStartTime().substring(0,10));
            }
            if(StringUtil.isNotEmpty(productVo.getPackageEndTime())){
                productVo.setPackageEndTime(productVo.getPackageEndTime().substring(0,10));
            }
            boolean bool=false;
            for(SkuStockMemberPriceVo skuStockMemberPriceVo:memberLevePriceVos){

                //获取会员等价价格
                List<MemberLevelGoodsPrice> memberPriceList=skuStockMemberPriceVo.getMemberLevelGoodsPrices();
                //获取会员id
                List<String> memberLevelIdList =new ArrayList<>();
                if(CollectionUtil.isNotEmpty(memberPriceList)){
                    memberLevelIdList = memberPriceList.stream().map(MemberLevelGoodsPrice::getMemberLevelId).distinct().collect(Collectors.toList());
                }

                //规格关联会员
                if(CollectionUtil.isNotEmpty(memberLevelList)){
                    for(MemberLevelVo memberLevelVo:memberLevelList){
                        //如果原来添加的商品多规格会员等级价格中已经有此会员,则跳出循环。
                        bool=memberLevelIdList.contains(memberLevelVo.getId());
                        if(bool){
                            continue;
                        }
                        MemberLevelGoodsPrice memberLevelGoodsPrice=new MemberLevelGoodsPrice();
                        memberLevelGoodsPrice.setMemberLevelId(memberLevelVo.getId());
                        memberLevelGoodsPrice.setProductId(skuStockMemberPriceVo.getProductId());
                        memberLevelGoodsPrice.setSkuId(skuStockMemberPriceVo.getId());
                        memberPriceList.add(memberLevelGoodsPrice);
                    }
                }
                skuStockMemberPriceVo.setMemberLevelGoodsPrices(memberPriceList);

                //获取会员等级复购价格
                List<MemberLevelGoodsAgainPrice> memberAgainPriceList = skuStockMemberPriceVo.getMemberLevelGoodsAgainPrices();
                //获取会员id
                List<String> memberLevelAgainIdList =new ArrayList<>();
                if(CollectionUtil.isNotEmpty(memberAgainPriceList)){
                    memberLevelAgainIdList = memberAgainPriceList.stream().map(MemberLevelGoodsAgainPrice::getMemberLevelId).distinct().collect(Collectors.toList());
                }
                if(CollectionUtil.isNotEmpty(memberLevelList)){
                    for(MemberLevelVo memberLevelVo:memberLevelList){
                        bool=memberLevelAgainIdList.contains(memberLevelVo.getId());
                        if(bool){
                            continue;
                        }
                        MemberLevelGoodsAgainPrice MemberLevelGoodsAgainPrice=new MemberLevelGoodsAgainPrice();
                        MemberLevelGoodsAgainPrice.setMemberLevelId(memberLevelVo.getId());
                        MemberLevelGoodsAgainPrice.setProductId(skuStockMemberPriceVo.getProductId());
                        MemberLevelGoodsAgainPrice.setSkuId(skuStockMemberPriceVo.getId());
                        memberAgainPriceList.add(MemberLevelGoodsAgainPrice);
                    }

                }
                skuStockMemberPriceVo.setMemberLevelGoodsAgainPrices(memberAgainPriceList);
            }
            productVo.setSkuStockMemberPriceVos(memberLevePriceVos);
            productVoList.add(productVo);
        }

        return page.setRecords(productVoList);
    }

    /**
     * 获取供应商商品分页信息
     *
     * @param productParam
     * @return 商品分页对象
     */
    @Override
    public IPage<ProductVo> getSupplierProductList(ProductParam productParam) {
        IPage<ProductVo> page = new Page<>(productParam.getCurrent(), productParam.getSize());
        List<ProductVo> productVos = this.baseMapper.querySupplierProductList(page, productParam);
        return page.setRecords(productVos);
    }



    /**
     * 设置商品评分字段公共方法
     *
     * @param productVo
     * @param productRateVos
     */
    public void setProductRate(ProductVo productVo, List<ProductRateVo> productRateVos) {
        if (CollectionUtil.isNotEmpty(productRateVos)) {
            productRateVos.stream().forEach(productRateVo -> {
                if (productVo.getId().equals(productRateVo.getProductId())) {
                    if (productRateVo.getRate() != null) {
                        productVo.setScore(productRateVo.getRate());
                    }
                }
            });
        }
    }



    /**
     * 获取单个商品基础信息
     *
     * @param id
     * @return 商品基础信息对象
     */
    @Override
    public ProductVo getProductById(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        ProductVo productVo = this.baseMapper.getProductById(id);
        if (BeanUtil.isEmpty(productVo)) {
            throw new ServiceException("商品不存在！", SystemCode.DATA_EXISTED.getCode());
        }
        //判断权益包展示时间是否存在，初始化编辑数据
        if(StringUtil.isNotEmpty(productVo.getPackageShowStartTime())&&StringUtil.isNotEmpty(productVo.getPackageShowEndTime())){
            String packageShowStartTime = productVo.getPackageShowStartTime().substring(0, 10);
            String packageShowEndTime = productVo.getPackageShowEndTime().substring(0, 10);
            productVo.setPackageShowStartTime(packageShowStartTime);
            productVo.setPackageShowEndTime(packageShowEndTime);
        }
        if(StringUtil.isNotEmpty(productVo.getPackageStartTime())){
            productVo.setPackageStartTime(productVo.getPackageStartTime().substring(0,10));
        }
        if(StringUtil.isNotEmpty(productVo.getPackageEndTime())){
            productVo.setPackageEndTime(productVo.getPackageEndTime().substring(0,10));
        }


        List<ProductPackageVo>packagePruducts = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(productVo.getProductType()==ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            LambdaQueryWrapper<ProductPackage>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductPackage::getPackageId,id);
            List<ProductPackage> list = productPackageMapper.selectList(wrapper);
            if(list!=null&&list.size()>0){
                for (ProductPackage productPackage : list) {
                    ProductPackageVo productPackageVo = new ProductPackageVo();
                    productPackageVo.setId(productPackage.getPackageId());
                    productPackageVo.setProductId(productPackage.getProductId());
                    //获取产品
                    Product product = this.getById(productPackage.getProductId());
                    productPackageVo.setProductName(product.getName());
                    productPackageVo.setGoodsCode(product.getGoodsCode());
                    //获取产品规格
                    SkuStock skuStock = skuStockMapper.selectById(productPackage.getSkuId());
                    productPackageVo.setSpecs(skuStock.getSpecs());
                    productPackageVo.setPrice(productPackage.getPrice());//实售价
                    productPackageVo.setOriginalPrice(productPackage.getOriginalPrice());//指导价
                    productPackageVo.setAmount(productPackage.getAmount());//金额

                    //获取专区
                    SaleMode saleMode = saleModeService.getById(product.getSaleMode());
                    productPackageVo.setModeName(saleMode.getModeName());
                    productPackageVo.setSkuId(productPackage.getSkuId());
                    LocalDateTime startTime = productPackage.getStartTime();
                    LocalDateTime endTime = productPackage.getEndTime();
                    productPackageVo.setStartTime(startTime.format(formatter));
                    productPackageVo.setEndTime(endTime.format(formatter));
                    productPackageVo.setMutexGoodId(productPackage.getMutexGoodId());
                    productPackageVo.setUseNumber(productPackage.getUseNumber());
                    //权益包兑换商品是否无期限，不限次数
                    productPackageVo.setNotTerm(false);
                    productPackageVo.setNotTime(false);
                    if(productPackage.getNotTerm()!=null&&productPackage.getNotTerm()==1){
                        productPackageVo.setNotTerm(true);
                    }
                    if(productPackage.getNotTime()!=null&&productPackage.getNotTime()==1){
                        productPackageVo.setNotTime(true);
                    }
                    productPackageVo.setCostPrice(productPackage.getCostPrice());
                    productPackageVo.setAdjustmentPrice(productPackage.getAdjustmentPrice());
                    productPackageVo.setUseDays(productPackage.getUseDays());
                    productPackageVo.setDaysRate(productPackage.getDaysRate());
                    packagePruducts.add(productPackageVo);
                }
            }

        }
        productVo.setPackageProducts(packagePruducts);
        List<String>couponIds = new ArrayList<>();
        //获取赠送优惠券id
        LambdaQueryWrapper<PackageCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageCoupon::getPackageId,productVo.getId());
        List<PackageCoupon> packageCoupons = packageCouponMapper.selectList(wrapper);
        if(packageCoupons!=null&&packageCoupons.size()>0){
            for (PackageCoupon packageCoupon : packageCoupons) {
                couponIds.add(packageCoupon.getCouponId());
            }
        }
        productVo.setCouponIds(couponIds);
        ShopContextHolder.setShopId(shopId);
        return productVo;
    }



    /**
     * 根据商品id获取商品sku与会员价信息
     *
     * @param id
     * @return 商品sku list信息
     */
    @Override
    public List<SkuStockVo> getSkuStockAndMemberPriceById(Long id) {
        return productMapper.querySkuStock(id);
    }

    /**
     * 商品信息的发布 新增商品信息同时插入运费模版、sku、商品属性、商品辅助单位、展示分类信息，同时清除缓存数据
     *
     * @param productDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issueProduct(ProductDto productDto) {
        if(productDto.getSaleMode()==null){
            LambdaQueryWrapper<SaleMode> lambdaQueryWrapper =new LambdaQueryWrapper<SaleMode>();
            lambdaQueryWrapper.eq(SaleMode::getDefaultSale, SaleModeEnum.DEFAULT_VALUE.getSaleMode());
           List <SaleMode> saleModeList= saleModeService.list(lambdaQueryWrapper);
            if(CollectionUtils.isNotEmpty(saleModeList)){
                productDto.setSaleMode(saleModeList.get(0).getId());
                //默认状态下架
                productDto.setStatus(ProductStatusEnum.SELL_OFF.getStatus());
            }
        }
        if(productDto.getPlace()==null){
            //默认线上
            productDto.setPlace(0);
        }
        //调用生成货号方法生成商品货号
        String productSn = getProductSn();
        productDto.setProductSn(productSn);
        //状态默认上架
        if (productDto.getStatus() == null) {
            productDto.setStatus(ProductStatusEnum.SELL_ON.getStatus());
        }
        Product product = productDto.coverProduct();
        //评分默认5.0
        if (product.getScore() == null) {
            product.setScore(BigDecimal.valueOf(5.0));
        }

        //判断一个被关联的商品只能被关联一次
        List<SkuStockDto> skuStockDtos = productDto.getSkuStocks();
        if (CollectionUtil.isNotEmpty(skuStockDtos)) {
            if(product.getLimitType() == 1 || product.getLimitType() == 2){
                // 多规格
                skuStockDtos.forEach(e -> {
                    // 判断sku关联的商品不能是自身，且关联的商品本身不能又是关联商品
                    if(e.getLinkProductId() != null ){
                        Product linkProduct = productMapper.selectById(e.getLinkProductId());
                        if(linkProduct.getProductType() != null && linkProduct.getProductType() == ProductTypeEnum.GROUP_PRODUCT.getStatus()){
                            throw new ServiceException("保存失败，关联的商品不能又关联其他商品！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        }
                        // 同一个商品的不同规格还是会关联同一商品的同一规格，考虑预售，所以不限制这一逻辑，只限制不能被不同商品关联即可
                        List<SkuStock> linkedSkuStockList = this.skuStockMapper.selectList(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getLinkSkuId,e.getLinkSkuId()));
                        if(CollectionUtil.isNotEmpty(linkedSkuStockList)){
                            throw new ServiceException("保存失败，关联的商品已经被其他商品关联，不允许同时被多个商品关联！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        }

                        // 设置商品类型为组合商品
                        product.setProductType(ProductTypeEnum.GROUP_PRODUCT.getStatus());
                    }
                });
            }
        }

        if(ProductStatusEnum.SELL_ON.getStatus() == product.getStatus()){
            // 增加区分是普通商品还是权益包商品
            if(productDto.getProductType() != null &&  productDto.getProductType() == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                // 权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishPackageMaxNum = shopInfoDto.getPublishPackageMaxNum();
                    if(StrUtil.isNotEmpty(publishPackageMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishPackageMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取权益包上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus()).eq("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()));
                // 获取传输过来的未上架的商品数量
                int unCount = 1;
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }else{
                // 非权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishProductMaxNum = shopInfoDto.getPublishProductMaxNum();
                    if(StrUtil.isNotEmpty(publishProductMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishProductMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus()).ne("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()));
                // 获取传输过来的未上架的商品数量
                int unCount = 1;
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }

        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(productDto.getPackageStartTime()!=null){
            product.setPackageStartTime(LocalDateTime.parse(productDto.getPackageStartTime()+" 00:00:00", formatter));
        }
        if(productDto.getPackageEndTime()!=null){
            product.setPackageEndTime(LocalDateTime.parse(productDto.getPackageEndTime()+" 23:59:59", formatter));
        }
        if(productDto.getPackageShowStartTime()!=null){
            product.setPackageShowStartTime(LocalDateTime.parse(productDto.getPackageShowStartTime()+" 00:00:00", formatter));
        }
        if(productDto.getPackageShowEndTime()!=null){
            product.setPackageShowEndTime(LocalDateTime.parse(productDto.getPackageShowEndTime()+" 23:59:59", formatter));
        }
        //商品基础信息新增
        int insert = productMapper.insert(product);
        if (insert == 0) {
            throw new ServiceException("发布失败！", SystemCode.DATA_ADD_FAILED.getCode());
        }
        productDto.setId(product.getId());
        //商品属性信息新增
        List<ProductAttributeDto> productAttributeDtos = productDto.getProductAttributes();
        if (CollectionUtil.isNotEmpty(productAttributeDtos)) {
            addProductAttributeList(productAttributeDtos, product.getId());
        }

        //商品辅助单位信息新增
        List<ProductSecUnitDto> productSecUnitDtos = productDto.getProductSecUnits();
        if (CollectionUtil.isNotEmpty(productSecUnitDtos)) {
            //商品id赋值
            ProductSecUnit productSecUnit = productSecUnitDtos.get(0).coverProductSecUnit();
            productSecUnit.setProductId(product.getId());
            //辅助单位比值默认就是1
            productSecUnit.setSecUnitd(1);
            productSecUnitService.save(productSecUnit);
        }

        //新增商品展示分类信息
        List<ProductShowCategoryDto> productShowCategoryDtos = productDto.getProductShowCategorys();
        if (CollectionUtil.isNotEmpty(productShowCategoryDtos)) {
            addProductShowCategoryList(productShowCategoryDtos, product.getId());
        }

        //商品sku信息新增
        if (CollectionUtil.isNotEmpty(skuStockDtos)) {
            addSkuStock(skuStockDtos, product.getId());
        }
        //权益包商品
        if(productDto.getProductType() != null && ProductTypeEnum.PACKAGE_PRODUCT.getStatus() == productDto.getProductType()){
            List<ProductPackageDto> packageProducts = productDto.getPackageProducts();

            //根据权益包互斥商品唯一id分组
            Map<String, List<ProductPackageDto>> dataMap = packageProducts.stream().collect(Collectors.groupingBy(ProductPackageDto::getMutexGoodId));
            List<ProductPackageDto>dataList = new ArrayList<>();
            for (Map.Entry<String, List<ProductPackageDto>> entry : dataMap.entrySet()) {
                List<ProductPackageDto> productPackageDtoList = entry.getValue();

                if(productPackageDtoList!=null&&productPackageDtoList.size()==1){
                    for (ProductPackageDto productPackageDto : productPackageDtoList) {
                        productPackageDto.setMutexFlag(0);
                        dataList.add(productPackageDto);
                    }
                }
                if(productPackageDtoList!=null&&productPackageDtoList.size()>1){
                    for (ProductPackageDto productPackageDto : productPackageDtoList) {
                        productPackageDto.setMutexFlag(1);
                        dataList.add(productPackageDto);
                    }
                }
            }

            if(dataList!=null&&dataList.size()>0){
                for (ProductPackageDto productPackageDto : dataList) {
                    ProductPackage productPackage = new ProductPackage();
                    productPackage.setPackageId(productDto.getId());
                    productPackage.setProductId(productPackageDto.getProductId());
                    productPackage.setSkuId(productPackageDto.getSkuId());
                    productPackage.setMutexGoodId(productPackageDto.getMutexGoodId());
                    productPackage.setStartTime(LocalDateTime.parse(productDto.getPackageStartTime()+" 00:00:00", formatter));
                    productPackage.setEndTime(LocalDateTime.parse(productDto.getPackageEndTime()+" 23:59:59", formatter));
                    productPackage.setUseNumber(productPackageDto.getUseNumber());
                    productPackage.setPrice(productPackageDto.getPrice());
                    productPackage.setOriginalPrice(productPackageDto.getOriginalPrice());
                    productPackage.setAmount(productPackageDto.getAmount());
                    productPackage.setMutexFlag(productPackageDto.getMutexFlag());
                    //权益包兑换商品是否无期限，不限次数
                    productPackage.setNotTerm(0);
                    productPackage.setNotTime(0);
                    productPackage.setCostPrice(productPackageDto.getCostPrice());
                    productPackage.setAdjustmentPrice(productPackageDto.getAdjustmentPrice());
                    productPackage.setUseDays(productPackageDto.getUseDays());
                    productPackage.setDaysRate(productPackageDto.getDaysRate());
                    if(productPackageDto.getNotTerm()!=null&&productPackageDto.getNotTerm()){
                        productPackage.setNotTerm(1);
                    }
                    if(productPackageDto.getNotTime()!=null&&productPackageDto.getNotTime()){
                        productPackage.setNotTime(1);
                    }
                    productPackageMapper.insert(productPackage);
                }
            }
        }

        //新增后商品id赋值
        productDto.setId(product.getId());
        productDto.setSkuStocks(skuStockDtos);
        //新增赠送优惠券
        LambdaQueryWrapper<PackageCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageCoupon::getPackageId,product.getId());
        List<PackageCoupon> packageCoupons = packageCouponMapper.selectList(wrapper);
        if(packageCoupons!=null&&packageCoupons.size()>0){
            for (PackageCoupon packageCoupon : packageCoupons) {
                packageCouponMapper.deleteById(packageCoupon.getId());
            }
        }
        List<String> couponIds = productDto.getCouponIds();
        if(couponIds!=null&&couponIds.size()>0){
            for (String couponId : couponIds) {
                PackageCoupon packageCoupon = new PackageCoupon();
                packageCoupon.setCouponId(couponId);
                packageCoupon.setPackageId(String.valueOf(product.getId()));
                packageCouponMapper.insert(packageCoupon);
            }
        }
        //更新缓存商品基础信息
        updateCacheShoppingCartProduct(productDto);
    }

    /**
     * 商品展示分类批量变更
     *
     * @param productShowCategoryDtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductShowCategory(List<ProductShowCategoryDto> productShowCategoryDtos, Long[] productIds) {
        Arrays.stream(productIds).forEach(productId -> {
            //删除展示分类信息
            productShowCategoryMapper.delete(new QueryWrapper<ProductShowCategory>().eq("product_id", productId));
            addProductShowCategoryList(productShowCategoryDtos, productId);
        });
    }

    /**
     * 商品专区变更 同时商品状态更新成已下架
     * 判断变更的专区是否有原先选择的展示分类
     * 如果一级分类不存在则直接新增一个一级分类再关联 有二级就再关联二级
     * 如果一级分类存在则直接关联 再看二级分类是否存在 存在则直接关联 不存在则新增一个二级分类在关联
     * <p>
     * 分拣品类跟商品一一对应 所以直接判断新专区是否有该分拣品类 有就直接关联，没有就插入一条再关联
     *
     * @param ids
     * @param saleMode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductSaleMode(Long[] ids, Long saleMode) {
        List<Long> list = Arrays.asList(ids);
        list.stream().forEach(id -> {
            ProductVo productVo = productMapper.querySaleModeChangeProduct(id);
            if (BeanUtil.isEmpty(productVo)) {
                throw new ServiceException("有商品不存在或已删除,请重新选择！", SystemCode.DATA_EXISTED.getCode());
            }
            //展示分类关联
            if (CollectionUtil.isNotEmpty(productVo.getProductShowCategorys())) {
                //先删除商品关联的展示分类
                productShowCategoryMapper.delete(new QueryWrapper<ProductShowCategory>().eq("product_id", id));
                List<ProductShowCategoryVo> productShowCategoryVos = productVo.getProductShowCategorys();
                for (ProductShowCategoryVo productShowCategoryVo : productShowCategoryVos) {
                    ShowCategory showCategory = showCategoryMapper.selectOne(new QueryWrapper<ShowCategory>().eq("name", productShowCategoryVo.getName()).eq("sale_mode", saleMode));
                    if (BeanUtil.isEmpty(showCategory)) {
                        //说明新专区不存在该展示分类 则先添加新的一级与二级展示分类再关联
                        showCategory = new ShowCategory();
                        showCategory.setParentId(0L);
                        showCategory.setSort(CommonConstants.NUMBER_ZERO);
                        showCategory.setName(productShowCategoryVo.getName());
                        showCategory.setSaleMode(saleMode);
                        int showCategoryInsert = showCategoryMapper.insert(showCategory);
                        if (showCategoryInsert == 0) {
                            throw new ServiceException("一级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                        ProductShowCategory productShowCategory = new ProductShowCategory();
                        productShowCategory.setProductId(id);
                        productShowCategory.setParentId(0L);
                        productShowCategory.setShowCategoryId(showCategory.getId());
                        int productShowCategoryInsert = productShowCategoryMapper.insert(productShowCategory);
                        if (productShowCategoryInsert == 0) {
                            throw new ServiceException("商品一级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                        List<ProductShowCategorySecondVo> productShowCategorySeconds = productShowCategoryVo.getProductShowCategorySeconds();
                        if (CollectionUtil.isEmpty(productShowCategorySeconds)) {
                            throw new ServiceException("一级展示分类下暂无二级分类, 请先完善该商品分类信息！", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                        for (ProductShowCategorySecondVo productShowCategorySecondVo : productShowCategorySeconds) {
                            //新增展示分类
                            changeShowCategoryBySaleMode(showCategory, saleMode, productShowCategorySecondVo, id, productShowCategory);
                        }
                    } else {
                        //说明新专区存在该分类 再判断二级分类是否存在 存在直接关联 不存在新增后再插入
                        ProductShowCategory productShowCategory = new ProductShowCategory();
                        productShowCategory.setProductId(id);
                        productShowCategory.setParentId(0L);
                        productShowCategory.setShowCategoryId(showCategory.getId());
                        int productShowCategoryInsert = productShowCategoryMapper.insert(productShowCategory);
                        if (productShowCategoryInsert == 0) {
                            throw new ServiceException("商品一级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                        List<ProductShowCategorySecondVo> productShowCategorySeconds = productShowCategoryVo.getProductShowCategorySeconds();
                        if (CollectionUtil.isEmpty(productShowCategorySeconds)) {
                            throw new ServiceException("一级展示分类下暂无二级分类, 请先完善该商品分类信息！", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                        for (ProductShowCategorySecondVo productShowCategorySecondVo : productShowCategorySeconds) {
                            ShowCategory searchSecondShowCategory = showCategoryMapper.selectOne(new QueryWrapper<ShowCategory>().eq("name", productShowCategorySecondVo.getName()).eq("parent_id", showCategory.getId()).eq("sale_mode", saleMode));
                            if (BeanUtil.isEmpty(searchSecondShowCategory)) {
                                //新增展示分类
                                changeShowCategoryBySaleMode(showCategory, saleMode, productShowCategorySecondVo, id, productShowCategory);
                            } else {
                                //新增商品二级展示分类 记得关联上面商品一级展示分类id
                                ProductShowCategory secondProductShowCategory = new ProductShowCategory();
                                secondProductShowCategory.setProductId(id);
                                secondProductShowCategory.setParentId(productShowCategory.getId());
                                secondProductShowCategory.setShowCategoryId(searchSecondShowCategory.getId());
                                int secondProductShowCategoryInsert = productShowCategoryMapper.insert(secondProductShowCategory);
                                if (secondProductShowCategoryInsert == 0) {
                                    throw new ServiceException("商品二级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
                                }
                            }
                        }
                    }
                }
            }
            boolean sign = new LambdaUpdateChainWrapper<>(productMapper)
                    .eq(Product::getId, id)
                    .set(Product::getSaleMode, saleMode)
                    .set(Product::getStatus, ProductStatusEnum.SELL_OFF.getStatus()).update();
            if (!sign) {
                throw new ServiceException("商品专区变更失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        });
        //更新购物车缓存商品状态与专区
        updateCacheShoppingCartProductSaleMode(ids, saleMode);
    }

    /**
     * 变更的专区没有的展示分类新增
     *
     * @param showCategory
     * @param saleMode                    专区
     * @param productShowCategorySecondVo
     * @param id                          商品id
     * @param productShowCategory
     */
    public void changeShowCategoryBySaleMode(ShowCategory showCategory, Long saleMode, ProductShowCategorySecondVo productShowCategorySecondVo, Long id, ProductShowCategory productShowCategory) {
        //新增二级展示分类 记得关联上面一级展示分类id
        ShowCategory secondShowCategory = new ShowCategory();
        secondShowCategory.setParentId(showCategory.getId());
        secondShowCategory.setSort(CommonConstants.NUMBER_ZERO);
        secondShowCategory.setName(productShowCategorySecondVo.getName());
        secondShowCategory.setSaleMode(saleMode);
        int secondShowCategoryInsert = showCategoryMapper.insert(secondShowCategory);
        if (secondShowCategoryInsert == 0) {
            throw new ServiceException("二级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
        }
        //新增商品二级展示分类 记得关联上面商品一级展示分类id
        ProductShowCategory secondProductShowCategory = new ProductShowCategory();
        secondProductShowCategory.setProductId(id);
        secondProductShowCategory.setParentId(productShowCategory.getId());
        secondProductShowCategory.setShowCategoryId(secondShowCategory.getId());
        int secondProductShowCategoryInsert = productShowCategoryMapper.insert(secondProductShowCategory);
        if (secondProductShowCategoryInsert == 0) {
            throw new ServiceException("商品二级展示分类新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
        }
    }



    /**
     * 商品信息的修改（只有下架状态的商品才可以修改）
     * 修改商品信息，先清除商品原先的运费模版、sku、商品属性、展示分类信息，再执行插入操作
     *
     * @param productDto dto对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProduct(ProductDto productDto) {
        //判断商品是否处于下架状态
        Product productSearch = productMapper.selectById(productDto.getId());
        if (BeanUtil.isEmpty(productSearch)) {
            throw new ServiceException("商品不存在！", SystemCode.DATA_EXISTED.getCode());
        }

        if(ProductStatusEnum.SELL_ON.getStatus() == productDto.getStatus()){
            // 增加区分是普通商品还是权益包商品
            if(productDto.getProductType() == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                // 权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishPackageMaxNum = shopInfoDto.getPublishPackageMaxNum();
                    if(StrUtil.isNotEmpty(publishPackageMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishPackageMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取权益包上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus())
                        .eq("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()).ne("id", productDto.getId()));
                // 获取传输过来的未上架的商品数量
                int unCount = 1;
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }else{
                // 非权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishProductMaxNum = shopInfoDto.getPublishProductMaxNum();
                    if(StrUtil.isNotEmpty(publishProductMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishProductMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus())
                        .ne("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()).ne("id", productDto.getId()));
                // 获取传输过来的未上架的商品数量
                int unCount = 1;
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }

        }

        //商品基础信息修改
        Product product = productDto.coverProduct();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(productDto.getPackageStartTime()!=null){
            product.setPackageStartTime(LocalDateTime.parse(productDto.getPackageStartTime()+" 00:00:00", formatter));
        }
        if(productDto.getPackageEndTime()!=null){
            product.setPackageEndTime(LocalDateTime.parse(productDto.getPackageEndTime()+" 23:59:59", formatter));
        }
        if(productDto.getPackageShowStartTime()!=null){
            product.setPackageShowStartTime(LocalDateTime.parse(productDto.getPackageShowStartTime()+" 00:00:00", formatter));
        }
        if(productDto.getPackageShowEndTime()!=null){
            product.setPackageShowEndTime(LocalDateTime.parse(productDto.getPackageShowEndTime()+" 23:59:59", formatter));
        }

        List<SkuStockDto> skuStockDtos = productDto.getSkuStocks();
        if (CollectionUtil.isNotEmpty(skuStockDtos)) {
            if(product.getLimitType() == 0 && StrUtil.isNotEmpty(product.getPic())){
                //统一规格，则将商品的主图片覆盖sku的图片，否则sku的图片还是旧的
                skuStockDtos.forEach(e -> {
                    e.setPic(product.getPic());
                });
            }else if(product.getLimitType() == 1 || product.getLimitType() == 2){
                // 多规格
                skuStockDtos.forEach(e -> {
                    // 判断sku关联的商品不能是自身，且关联的商品本身不能又是关联商品
                    if(e.getLinkProductId() != null ){
                        if(e.getLinkProductId().longValue() == product.getId().longValue()){
                            throw new ServiceException("修改失败，关联的商品本身不能是自身！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        }
                        Product linkProduct = productMapper.selectById(e.getLinkProductId());
                        if(linkProduct.getProductType() != null && linkProduct.getProductType() == ProductTypeEnum.GROUP_PRODUCT.getStatus()){
                            throw new ServiceException("修改失败，关联的商品不能又关联其他商品！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        }

                        // 同一个商品的不同规格还是会关联同一商品的同一规格，考虑预售，所以不限制这一逻辑，只限制不能被不同商品关联即可
                        List<SkuStock> linkedSkuStockList = this.skuStockMapper.selectList(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getLinkSkuId,e.getLinkSkuId())
                                .ne(SkuStock::getId,e.getId()).ne(SkuStock::getProductId,e.getProductId()));
                        if(CollectionUtil.isNotEmpty(linkedSkuStockList)){
                            throw new ServiceException("保存失败，关联的商品已经被其他商品关联，不允许同时被多个商品关联！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        }

                        product.setProductType(ProductTypeEnum.GROUP_PRODUCT.getStatus());
                    }
                });
            }
        }

        int update = productMapper.updateById(product);
        if (update == 0) {
            throw new ServiceException("修改失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
        }

        //商品属性信息修改(先删除再新增)
        productAttributeMapper.delete(new QueryWrapper<ProductAttribute>().eq("product_id", productDto.getId()));
        List<ProductAttributeDto> productAttributeDtos = productDto.getProductAttributes();
        if (CollectionUtil.isNotEmpty(productAttributeDtos)) {
            addProductAttributeList(productAttributeDtos, productDto.getId());
        }

        //更新商品辅助单位信息
        List<ProductSecUnitDto> productSecUnits = productDto.getProductSecUnits();
        if (CollectionUtil.isNotEmpty(productSecUnits)) {
            ProductSecUnit productSecUnit = productSecUnits.get(0).coverProductSecUnit();
            if(productSecUnits.get(0).getId()!=null){
                productSecUnitService.updateById(productSecUnit);
            }else{
                productSecUnit.setProductId(productDto.getId());
                productSecUnitService.save(productSecUnit);
            }
        }


        //商品展示分类删除
        productShowCategoryMapper.delete(new QueryWrapper<ProductShowCategory>().eq("product_id", productDto.getId()));
        //商品展示分类信息新增
        List<ProductShowCategoryDto> productShowCategoryDtos = productDto.getProductShowCategorys();
        if (CollectionUtil.isNotEmpty(productShowCategoryDtos)) {
            //新增展示分类信息
            addProductShowCategoryList(productShowCategoryDtos, productDto.getId());
        }

        //更新sku信息(
        if (CollectionUtil.isNotEmpty(skuStockDtos)) {
            updateSkuStockByProduct(skuStockDtos, product.getId());
        }
        if(product.getLimitType()!=productSearch.getLimitType()){//商品规格类型变化，修改积分商品变更状态
            LambdaUpdateWrapper<IntegralProduct>wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(IntegralProduct::getChangeFlag,1);
            wrapper.eq(IntegralProduct::getProductId,product.getId());
            integralProductService.update(wrapper);
        }
        //权益包商品处理权益包商品表
        if(product.getProductType()==ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            productPackageMapper.delete(new LambdaQueryWrapper<ProductPackage>().eq(ProductPackage::getPackageId,product.getId()));
            List<ProductPackageDto> packageProducts = productDto.getPackageProducts();


            //根据权益包互斥商品唯一id分组
            Map<String, List<ProductPackageDto>> dataMap = packageProducts.stream().collect(Collectors.groupingBy(ProductPackageDto::getMutexGoodId));
            List<ProductPackageDto>dataList = new ArrayList<>();
            for (Map.Entry<String, List<ProductPackageDto>> entry : dataMap.entrySet()) {
                List<ProductPackageDto> productPackageDtoList = entry.getValue();

                if(productPackageDtoList!=null&&productPackageDtoList.size()==1){
                    for (ProductPackageDto productPackageDto : productPackageDtoList) {
                        productPackageDto.setMutexFlag(0);
                        dataList.add(productPackageDto);
                    }
                }
                if(productPackageDtoList!=null&&productPackageDtoList.size()>1){
                    for (ProductPackageDto productPackageDto : productPackageDtoList) {
                        productPackageDto.setMutexFlag(1);
                        dataList.add(productPackageDto);
                    }
                }
            }

            if(dataList!=null&&dataList.size()>0){
                for (ProductPackageDto productPackageDto : dataList) {
                    ProductPackage productPackage = new ProductPackage();
                    productPackage.setPackageId(productDto.getId());
                    productPackage.setProductId(productPackageDto.getProductId());
                    productPackage.setSkuId(productPackageDto.getSkuId());
                    productPackage.setMutexGoodId(productPackageDto.getMutexGoodId());
                    productPackage.setStartTime(LocalDateTime.parse(productDto.getPackageStartTime()+" 00:00:00", formatter));
                    productPackage.setEndTime(LocalDateTime.parse(productDto.getPackageEndTime()+" 23:59:59", formatter));
                    productPackage.setUseNumber(productPackageDto.getUseNumber());
                    productPackage.setPrice(productPackageDto.getPrice());
                    productPackage.setOriginalPrice(productPackageDto.getOriginalPrice());
                    productPackage.setAmount(productPackageDto.getAmount());
                    productPackage.setMutexFlag(productPackageDto.getMutexFlag());
                    //权益包兑换商品是否无期限，不限次数
                    productPackage.setNotTerm(0);
                    productPackage.setNotTime(0);
                    productPackage.setCostPrice(productPackageDto.getCostPrice());
                    productPackage.setAdjustmentPrice(productPackageDto.getAdjustmentPrice());
                    productPackage.setUseDays(productPackageDto.getUseDays());
                    productPackage.setDaysRate(productPackageDto.getDaysRate());
                    if(productPackageDto.getNotTerm()!=null&&productPackageDto.getNotTerm()){
                        productPackage.setNotTerm(1);
                    }
                    if(productPackageDto.getNotTime()!=null&&productPackageDto.getNotTime()){
                        productPackage.setNotTime(1);
                    }
                    productPackageMapper.insert(productPackage);
                }
            }
        }
        //新增赠送优惠券
        LambdaQueryWrapper<PackageCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageCoupon::getPackageId,product.getId());
        List<PackageCoupon> packageCoupons = packageCouponMapper.selectList(wrapper);
        if(packageCoupons!=null&&packageCoupons.size()>0){
            for (PackageCoupon packageCoupon : packageCoupons) {
                packageCouponMapper.deleteById(packageCoupon.getId());
            }
        }
        List<String> couponIds = productDto.getCouponIds();
        if(couponIds!=null&&couponIds.size()>0){
            for (String couponId : couponIds) {
                PackageCoupon packageCoupon = new PackageCoupon();
                packageCoupon.setCouponId(couponId);
                packageCoupon.setPackageId(String.valueOf(product.getId()));
                packageCouponMapper.insert(packageCoupon);
            }
        }
        //更新缓存商品基础信息
        updateCacheShoppingCartProduct(productDto);
    }

    /**
     * 商品上下架
     *
     * @param ids
     * @param status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStatus(Long[] ids, Integer status, Integer productType) {
        List<Long> list = Arrays.asList(ids);
        if(ProductStatusEnum.SELL_ON.getStatus() == status){
            // 增加区分是普通商品还是权益包商品
            if(productType == ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
                // 权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishPackageMaxNum = shopInfoDto.getPublishPackageMaxNum();
                    if(StrUtil.isNotEmpty(publishPackageMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishPackageMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取权益包上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus())
                        .eq("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()));
                // 获取传输过来的未上架的商品数量
                int unCount = productMapper.selectCount(new QueryWrapper<Product>().ne("status", ProductStatusEnum.SELL_ON.getStatus()).in("id", list));
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_UPDATE_FAILED.getCode());
                }
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }else{
                // 非权益包商品
                // 如果是上架，增加判断上架数量是否符合个数限制
                int productMaxNum = 0;
                Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String publishProductMaxNum = shopInfoDto.getPublishProductMaxNum();
                    if(StrUtil.isNotEmpty(publishProductMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(publishProductMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("上架失败，获取上架限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }

                // 获取数据库种现有已上架的商品数量
                int dbCount = productMapper.selectCount(new QueryWrapper<Product>().eq("status", ProductStatusEnum.SELL_ON.getStatus())
                        .ne("product_type", ProductTypeEnum.PACKAGE_PRODUCT.getStatus()));
                // 获取传输过来的未上架的商品数量
                int unCount = productMapper.selectCount(new QueryWrapper<Product>().ne("status", ProductStatusEnum.SELL_ON.getStatus()).in("id", list));
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_UPDATE_FAILED.getCode());
                }
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("上架失败，上架数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }
        }
        list.stream().forEach(id -> {
            boolean sign = new LambdaUpdateChainWrapper<>(productMapper)
                    .eq(Product::getId, id)
                    .set(Product::getStatus, status).update();
            if (!sign) {
                throw new ServiceException("商品状态更新失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        });
        //更新缓存商品基础信息中的状态
        updateCacheShoppingCartProductStatus(ids, status);
    }

    /**
     * 商品属性批量新增
     *
     * @param productAttributeDtos
     * @param id
     */
    private void addProductAttributeList(List<ProductAttributeDto> productAttributeDtos, Long id) {
        productAttributeDtos.stream().forEach(bean -> {
            ProductAttribute productAttribute = bean.coverProductAttribute();
            productAttribute.setProductId(id);
            int insertProductAttribute = productAttributeMapper.insert(productAttribute);
            if (insertProductAttribute == 0) {
                throw new ServiceException("商品属性信息新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
        });
    }

    /**
     * 商品信息的删除
     *
     * @param ids  删除的商品对象集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductList(Long[] ids) throws Exception {

        //判断仓库是否有商品
        QueryWrapper<ProductStock> productStockQueryWrapper = new QueryWrapper<>();
        productStockQueryWrapper.lambda().in(ProductStock::getProductId,ids);
        Integer count = productStockMapper.selectCount(productStockQueryWrapper);
        if(count>0){
            throw new Exception("商品不能删除，已有仓库有该商品！");
        }

        //判断是否有该商品订单
        Boolean isOrder = remoteOrderService.getIsOrderByProductId(ids);
        if(isOrder){
            throw new Exception("商品不能删除，该商品已有订单！");
        }
        //判断是否有售后单
        Boolean isAfsOrder = remoteAfsService.getIsAfsOrder(ids);
        if(isAfsOrder){
            throw new Exception("商品不能删除，该商品已有售后单！");
        }
        //判断是否有评论
        Boolean isEvaluate= remoteOrderService.getIsEvaluateByProductId(ids);
        if(isEvaluate){
            throw new Exception("商品不能删除，该商品已有评论！");
        }


        productAttributeMapper.delete(new QueryWrapper<ProductAttribute>().in("product_id", Arrays.asList(ids)));
        //需要清除sku缓存
        List<SkuStock> skuStocks = skuStockMapper.selectList(new QueryWrapper<SkuStock>().in("product_id", Arrays.asList(ids)));
        if (CollectionUtil.isNotEmpty(skuStocks)) {
            deleteSkuStock(skuStocks);
        }
        productShowCategoryMapper.delete(new QueryWrapper<ProductShowCategory>().in("product_id", Arrays.asList(ids)));
        removeByIds(Arrays.asList(ids));
        //删除缓存里面的商品信息
        deleteCacheShoppingCartProduct(ids);
    }

    /**
     * 商品展示分类大类批量新增
     *
     * @param productShowCategoryDtos
     * @param id
     */
    private void addProductShowCategoryList(List<ProductShowCategoryDto> productShowCategoryDtos, Long id) {
        productShowCategoryDtos.stream().forEach(bean -> {
            ProductShowCategory productShowCategory = bean.coverProductShowCategory();
            productShowCategory.setProductId(id);
            int insertSkuStock = productShowCategoryMapper.insert(productShowCategory);
            if (insertSkuStock == 0) {
                throw new ServiceException("商品展示分类信息新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
            //新增会员价信息
            if (CollectionUtil.isNotEmpty(bean.getProductShowCategorySeconds())) {
                addProductShowCategorySecondList(bean.getProductShowCategorySeconds(), productShowCategory.getId(), id);
            }
        });
    }

    /**
     * 商品展示分类二级分类批量新增
     *
     * @param productShowCategorySecondDtos
     * @param id
     */
    private void addProductShowCategorySecondList(List<ProductShowCategorySecondDto> productShowCategorySecondDtos, Long parentId, Long id) {
        productShowCategorySecondDtos.stream().forEach(bean -> {
            ProductShowCategory productShowCategory = bean.coverProductShowCategory();
            productShowCategory.setProductId(id);
            productShowCategory.setParentId(parentId);
            int insertSkuStock = productShowCategoryMapper.insert(productShowCategory);
            if (insertSkuStock == 0) {
                throw new ServiceException("商品展示二级分类信息新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
        });
    }





    /**
     * 根据商品数组匹配未删除的商品
     *
     * @param ids
     * @return List<DiscountProductVo>
     */
    @Override
    public List<DiscountProductVo> getAliveProductList(Long[] ids) {

        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<DiscountProductVo>newList= new ArrayList<>();
        List<DiscountProductVo> dbVoList = discountProductMapper.querySaveProductList(Arrays.asList(ids));
        if(dbVoList!=null&&dbVoList.size()>0){
            // 店铺id
            List<Long> partnerIdList = dbVoList.stream().map(s -> Long.parseLong(s.getShopsPartnerId())).collect(Collectors.toList());
            List<ShopsPartnerCategoryVo> partnerCategoryVoList = this.remoteShopsService.getManyShopsCategoryName(partnerIdList);
            Map<Long, List<ShopsPartnerCategoryVo>> partnerCategoryVoMap = partnerCategoryVoList.stream().collect(Collectors.groupingBy(ShopsPartnerCategoryVo::getShopsPartnerId));
            dbVoList.stream().forEach(e -> {
                List<ShopsPartnerCategoryVo> tempList = partnerCategoryVoMap.get(Long.parseLong(e.getShopsPartnerId()));
                if(CollectionUtil.isNotEmpty(tempList)){
                    String names = tempList.stream().map(ShopsPartnerCategoryVo::getName).collect(Collectors.joining(","));
                    e.setShopsCategoryName(names);
                }
            });
            //根据特殊配置判断显示商品类型
            dbVoList.stream().forEach(e -> {
                String newShopId = e.getShopId();
                Integer productType = e.getProductType();
                //获取特殊配置
                List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(newShopId);
                //小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;
                Integer miniShowProductType = 0;
                if(specialSettingList!=null&&specialSettingList.size()>0){
                    SpecialSetting specialSetting = specialSettingList.get(0);
                    miniShowProductType = specialSetting.getMiniShowProductType();
                }

                if(productType==ProductTypeEnum.BASIC_PRODUCT.getStatus()){//普通商品
                    if(miniShowProductType== MiniShowProductTypeEnum.BASIC_PRODUCT.getStatus()||
                            miniShowProductType==MiniShowProductTypeEnum.BASIC_AND_PACKAGE_PRODUCT.getStatus()){
                        newList.add(e);
                    }
                }
                if(productType==ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){//权益包商品
                    if(miniShowProductType== MiniShowProductTypeEnum.PACKAGE_PRODUCT.getStatus()||
                            miniShowProductType==MiniShowProductTypeEnum.BASIC_AND_PACKAGE_PRODUCT.getStatus()){
                        newList.add(e);
                    }
                }

            });
        }
        ShopContextHolder.setShopId(shopId);
        return newList;
    }

    @Override
    public Boolean getProductShowFlag(Long id) {
        Boolean result = false;
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Product product = this.getById(id);
        String productShopId = product.getShopId();
        Integer productType = product.getProductType();
        //获取特殊配置
        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(productShopId);
        //小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;
        Integer miniShowProductType = 0;
        if(specialSettingList!=null&&specialSettingList.size()>0){
            SpecialSetting specialSetting = specialSettingList.get(0);
            miniShowProductType = specialSetting.getMiniShowProductType();
        }

        if(productType==ProductTypeEnum.BASIC_PRODUCT.getStatus()){//普通商品
            if(miniShowProductType== MiniShowProductTypeEnum.BASIC_PRODUCT.getStatus()||
                    miniShowProductType==MiniShowProductTypeEnum.BASIC_AND_PACKAGE_PRODUCT.getStatus()){
                result = true;
            }
        }
        if(productType==ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){//权益包商品
            if(miniShowProductType== MiniShowProductTypeEnum.PACKAGE_PRODUCT.getStatus()||
                    miniShowProductType==MiniShowProductTypeEnum.BASIC_AND_PACKAGE_PRODUCT.getStatus()){
                result = true;
            }
        }
        ShopContextHolder.setShopId(shopId);
        return result;
    }


    /**
     * 批量查询多个商品详情
     *
     * @param productIds
     * @return List<DiscountProductVo>
     */
    @Override
    public List<DiscountProductVo> getDiscountProductTypeList(List<Long> productIds) {
        return discountProductMapper.queryDiscountProductTypeList(productIds);
    }

    /**
     * 查询上架商品的数量
     *
     * @return Integer
     */
    @Override
    public Integer getProductCount() {
        return discountProductMapper.selectCount(new QueryWrapper<Product>().eq("status", CommonConstants.NUMBER_ONE).eq("is_deleted", CommonConstants.NUMBER_ZERO));
    }


    /**
     * 查询运费模版id是否被商品使用
     *
     * @param templateId
     * @return Boolean
     */
    @Override
    public Boolean checkProductByTemplateId(Long templateId) {
        int count = productMapper.selectCount(new QueryWrapper<Product>().eq("freight_template_id", templateId).eq("is_deleted", CommonConstants.NUMBER_ZERO));
        return count > 0;
    }

    /**
     * 组件查询所有商品列表
     * discountProductParam
     *
     * @param discountProductParam
     * @return 商品list对象
     */
    @Override
    public IPage<DiscountProductVo> getDiscountProductList(DiscountProductParam discountProductParam) {
        String shopId = ShopContextHolder.getShopId();
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        if(shopsPartner.getMainFlag()!=null&&shopsPartner.getMainFlag()==1){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        if(discountProductParam.getShopId()!=null){
            ShopContextHolder.setShopId(discountProductParam.getShopId()+"");
        }
        IPage<DiscountProductVo> page = new Page<>(discountProductParam.getCurrent(), discountProductParam.getSize());

        //获取特殊配置
        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
        //小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;
        Integer miniShowProductType = 0;
        if(specialSettingList!=null&&specialSettingList.size()>0){
            SpecialSetting specialSetting = specialSettingList.get(0);
            miniShowProductType = specialSetting.getMiniShowProductType();
        }

        discountProductParam.setMiniShowProductType(miniShowProductType);




        List<DiscountProductVo> list = discountProductMapper.queryDiscountProductList(page, discountProductParam);
        // 店铺id
        List<Long> partnerIdList = list.stream().map(s -> Long.parseLong(s.getShopsPartnerId())).collect(Collectors.toList());
        List<ShopsPartnerCategoryVo> partnerCategoryVoList = this.remoteShopsService.getManyShopsCategoryName(partnerIdList);
        if(null != partnerCategoryVoList){
            Map<Long, List<ShopsPartnerCategoryVo>> partnerCategoryVoMap = partnerCategoryVoList.stream().collect(Collectors.groupingBy(ShopsPartnerCategoryVo::getShopsPartnerId));
            list.stream().forEach(e -> {
                List<ShopsPartnerCategoryVo> tempList = partnerCategoryVoMap.get(Long.parseLong(e.getShopsPartnerId()));
                if(CollectionUtil.isNotEmpty(tempList)){
                    String names = tempList.stream().map(ShopsPartnerCategoryVo::getName).collect(Collectors.joining(","));
                    e.setShopsCategoryName(names);
                }
            });
        }

        page.setRecords(list);
        ShopContextHolder.setShopId(shopId);
        return page;
    }


    /**
     * 获取单个sku关联商品会员价信息
     *
     * @param skuIds
     * @return ItemVo
     */
    @Override
    public List<ItemVo> findItemVoByIds(List<Long> skuIds) {
        if (skuIds.isEmpty()) {
            return new ArrayList<>(CommonConstants.NUMBER_ZERO);
        } else {
            return skuStockMapper.queryItemVoByIds(skuIds);
        }
    }

    @Override
    public List<ItemVo> findItemVoByItemVo(String jsonStr) {

        List<Map<String, String>>dataList= (List)JSONArray.parseArray(jsonStr,Map.class);

        if (dataList.isEmpty()) {
            return new ArrayList<>(CommonConstants.NUMBER_ZERO);
        } else {
            List<ItemVo> list = new ArrayList<>();
            for (Map<String, String> map : dataList) {
                String skuId = map.get("skuId");
                String priceType = map.get("priceType");
                List<Long>skuIds = new ArrayList<>();
                skuIds.add(Long.valueOf(skuId));
                List<ItemVo> itemVos = skuStockMapper.queryItemVoByIds(skuIds);
                if(itemVos!=null&&itemVos.size()>0){
                    ItemVo itemVo = itemVos.get(0);
                    itemVo.setPriceType(Integer.valueOf(priceType));
                    list.add(itemVo);
                }
            }
            return list;
        }
    }
    /**
     * 查询会员价格表的商品信息
     *
     * @param memberId
     * @return MemberGoodsPrice
     */
    @Override
    public List<MemberLevelGoodsPrice> selectMemberGoodsPrice(String memberId) {
        LambdaQueryWrapper<MemberLevelGoodsPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberLevelGoodsPrice::getMemberLevelId,memberId);
        queryWrapper.eq(MemberLevelGoodsPrice::getDeleted,CommonConstants.NUMBER_ZERO);
        return apiMemberGoodsPriceMapper.selectList(queryWrapper);
    }

    @Override
    public  List<MemberLevelGoodsAgainPrice> selectMemberGoodsAgainPrice(String memberId) {
        LambdaQueryWrapper<MemberLevelGoodsAgainPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberLevelGoodsAgainPrice::getMemberLevelId,memberId);
        queryWrapper.eq(MemberLevelGoodsAgainPrice::getDeleted,CommonConstants.NUMBER_ZERO);
        return apiMemberGoodsAgainPriceMapper.selectList(queryWrapper);
    }

    /**
     * 产品修改调用更新sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
     *
     * @param skuStockDtos
     * @param productId
     */
    public void updateSkuStockByProduct(List<SkuStockDto> skuStockDtos, Long productId) {
        LambdaQueryWrapper<SkuStock> skuStockWrapper = new LambdaQueryWrapper<>();
        skuStockWrapper.eq(SkuStock::getProductId, productId);
        List<SkuStock> skuStocks = skuStockMapper.selectList(skuStockWrapper);
        //需要新增加的sku信息
        List<SkuStockDto> distinctSkuStockDtos = skuStockDtos.stream()
                .filter(skuStockDto -> !skuStocks.stream()
                        .map(skuStock -> skuStock.getId())
                        .collect(toList())
                        .contains(skuStockDto.getId()))
                .collect(toList());
        if (CollectionUtil.isNotEmpty(distinctSkuStockDtos)) {
            addSkuStock(distinctSkuStockDtos, productId);
        }
        //需要删除的sku信息
        List<SkuStock> distinctSkuStocks = skuStocks.stream()
                .filter(skuStock -> !skuStockDtos.stream()
                        .map(skuStockDto -> skuStockDto.getId())
                        .collect(toList())
                        .contains(skuStock.getId()))
                .collect(toList());
        if (CollectionUtil.isNotEmpty(distinctSkuStocks)) {
            deleteSkuStock(distinctSkuStocks);
        }
        //需要修改的sku信息
        List<SkuStockDto> sameUserList = skuStockDtos.stream()
                .filter(skuStockDto -> skuStocks.stream()
                        .map(skuStock -> skuStock.getId())
                        .collect(toList())
                        .contains(skuStockDto.getId()))
                .collect(toList());
        if (CollectionUtil.isNotEmpty(sameUserList)) {
            updateSkuStock(sameUserList, productId);
        }
    }

    /**
     * 新增sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
     *
     * @param skuStockDtos 需要插入的sku库存list
     * @param productId
     */
    public void addSkuStock(List<SkuStockDto> skuStockDtos, Long productId) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        skuStockDtos.stream().forEach(bean -> {
            bean.setProductId(productId);
            SkuStock skuStock = bean.coverSkuStock();
            //新增sku
            int insert = skuStockMapper.insert(skuStock);
            if (insert == 0) {
                throw new ServiceException("商品sku信息新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
            bean.setId(skuStock.getId());
            //新增sku缓存信息
            goodsSkuStockRedisKey.set(String.valueOf(skuStock.getId()), String.valueOf(bean.getStock()));
        });
    }

    /**
     * 更新购物车商品状态缓存信息
     *
     * @param ids
     */
    public void updateCacheShoppingCartProductStatus(Long[] ids, Integer status) {
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        Arrays.asList(ids).stream().forEach(id -> {
            ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(String.valueOf(id)), ApiShoppingCartProductVo.class);
            if (!BeanUtil.isEmpty(apiShoppingCartProductVo)) {
                apiShoppingCartProductVo.setStatus(status);
                goodsProductRedisKey.set(String.valueOf(id), JSON.toJSONString(apiShoppingCartProductVo));
            }
        });
    }

    /**
     * 更新购物车商品专区缓存信息
     *
     * @param ids
     */
    public void updateCacheShoppingCartProductSaleMode(Long[] ids, Long saleMode) {
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        Arrays.asList(ids).stream().forEach(id -> {
            ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(String.valueOf(id)), ApiShoppingCartProductVo.class);
            if (!BeanUtil.isEmpty(apiShoppingCartProductVo)) {
                apiShoppingCartProductVo.setSaleMode(saleMode);
                apiShoppingCartProductVo.setStatus(CommonConstants.NUMBER_ZERO);
                goodsProductRedisKey.set(String.valueOf(id), JSON.toJSONString(apiShoppingCartProductVo));
            }
        });
    }


    /**
     * 更新购物车商品缓存信息
     *
     * @param productDto
     */
    public void updateCacheShoppingCartProduct(ProductDto productDto) {
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        ApiShoppingCartProductVo apiShoppingCartProductVo = new ApiShoppingCartProductVo();
        apiShoppingCartProductVo.setProductId(productDto.getId());
        apiShoppingCartProductVo.setProductName(productDto.getName());
        apiShoppingCartProductVo.setProductSn(productDto.getProductSn());
        apiShoppingCartProductVo.setPic(productDto.getPic());
        apiShoppingCartProductVo.setStatus(productDto.getStatus());
        apiShoppingCartProductVo.setDistributionMode(productDto.getDistributionMode());
        apiShoppingCartProductVo.setSkuStocks(productDto.getSkuStocks());
        apiShoppingCartProductVo.setSaleMode(productDto.getSaleMode());
        apiShoppingCartProductVo.setDeleted(CommonConstants.NUMBER_ZERO);
        goodsProductRedisKey.set(String.valueOf(productDto.getId()), JSON.toJSONString(apiShoppingCartProductVo));
    }

    /**
     * 删除购物车商品缓存信息 更改删除状态的值
     *
     * @param ids
     */
    public void deleteCacheShoppingCartProduct(Long[] ids) {
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        Arrays.asList(ids).stream().forEach(id -> {
            ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(String.valueOf(id)), ApiShoppingCartProductVo.class);
            if (!BeanUtil.isEmpty(apiShoppingCartProductVo)) {
                apiShoppingCartProductVo.setDeleted(CommonConstants.NUMBER_ONE);
                goodsProductRedisKey.set(String.valueOf(id), JSON.toJSONString(apiShoppingCartProductVo));
            }
        });
    }

    /**
     * 删除sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
     *
     * @param skuStocks 需要插入的sku库存list
     */
    public void deleteSkuStock(List<SkuStock> skuStocks) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        skuStocks.stream().forEach(bean -> {
            //删除商品sku信息
            int delete = skuStockMapper.deleteById(bean.getId());
            if (delete == 0) {
                throw new ServiceException("商品sku信息删除失败！", SystemCode.DATA_DELETE_FAILED.getCode());
            }
            //删除sku缓存信息
            goodsSkuStockRedisKey.del(String.valueOf(bean.getId()));
        });
    }

    /**
     * 修改sku库存(包括sku表、缓存维护的sku信息、购物车sku信息)
     *
     * @param skuStockDtos 需要修改的sku库存list
     * @param productId
     */
    @Override
    public void updateSkuStock(List<SkuStockDto> skuStockDtos, Long productId) {
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        skuStockDtos.stream().forEach(bean -> {
            SkuStock skuStock = skuStockMapper.selectById(bean.getId());
            BeanUtil.copyProperties(bean, skuStock, "sale");
            //更新sku缓存信息
            int update = skuStockMapper.updateById(skuStock);
            if (update == 0) {
                throw new ServiceException("商品sku信息更新失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
            //更新sku缓存信息
            goodsSkuStockRedisKey.set(String.valueOf(bean.getId()), String.valueOf(bean.getStock()));
        });
    }

    /**
     * 生成商品货号公共方法
     *
     * @return Result
     */
    public String getProductSn() {
        String productSn;
        do {
            //生成一个16位的商品id
            String date = DateUtil.format(new Date(), new SimpleDateFormat("yyyyMMdd"));
            Integer count = this.baseMapper.queryAllCount() + CommonConstants.NUMBER_ONE;
            String newCount = new DecimalFormat("00000000").format(count);
            productSn = date + newCount;
            //验证改id是否已经被使用
            Product productSearch = this.baseMapper.selectOne(new QueryWrapper<Product>().eq("product_sn", productSn));
            if (BeanUtil.isEmpty(productSearch)) {
                break;
            }
        } while (true);
        return productSn;
    }

    /**
     * 淘宝商品csv文件导入
     *
     * @param file 商品csv文件
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importCsvProduct(MultipartFile file) {
        if (file.getSize() == 0) {
            throw new ServiceException("文件表格异常", SystemCode.DATA_EXISTED_CODE);
        }
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        if (".csv".equals(suffix)) {
            try {
                CsvReader reader = CsvUtil.getReader();
                //从文件中读取CSV数据
                CsvData data = reader.read(FileUtil.file(CsvFileUtil.multipartFileToFile(file)), Charset.forName("utf-16"));
                if (data.getRowCount() < CommonConstants.NUMBER_FOUR) {
                    throw new ServiceException("文件内容有误！", SystemCode.DATA_ADD_FAILED_CODE);
                }
                //CsvData对象转成list对象
                List<Map<String, Object>> productList = CsvFileUtil.transCsvRowToList(data);
                for (Map<String, Object> obj : productList) {
                    Product product = new Product();
                    //调用生成货号方法生成商品货号
                    String productSn = getProductSn();
                    product.setProductSn(productSn);
                    product.setName(Convert.toStr(obj.get("title")).replaceAll("\"", ""));
                    product.setDetail(Convert.toStr(obj.get("description")).replaceAll("\"", ""));
                    product.setDistributionMode(CommonConstants.NUMBER_ZERO);
                    product.setStatus(CommonConstants.NUMBER_ZERO);
                    product.setScore(BigDecimal.valueOf(5.0));
                    product.setCsvUrl("https://item.taobao.com/item.htm?id=" + Convert.toStr(obj.get("num_id")).replaceAll("\"", ""));
                    //状态设置为素材库中
                    product.setPlace(CommonConstants.NUMBER_ONE);
                    //图片赋值公共方法
                    setCsvPic(product, obj);
                    //sku信息组合
                    List<SkuStock> skuStocks = new ArrayList<>(20);
                    //sku赋值公共方法
                    setCsvSku(product, skuStocks, obj);
                    //商品主表信息新增
                    int insert = productMapper.insert(product);
                    if (insert == 0) {
                        throw new ServiceException("商品信息上传失败！", SystemCode.DATA_ADD_FAILED_CODE);
                    }
                    //sku信息新增
                    if (CollectionUtil.isNotEmpty(skuStocks)) {
                        skuStocks.stream().forEach(bean -> {
                            bean.setProductId(product.getId());
                            int skuStockInsert = skuStockMapper.insert(bean);
                            if (skuStockInsert == 0) {
                                throw new ServiceException("库存信息上传失败！", SystemCode.DATA_ADD_FAILED_CODE);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("文件解析失败！", SystemCode.DATA_ADD_FAILED_CODE);
            }
        } else {
            throw new ServiceException("文件格式有误,请重新上传!", SystemCode.PARAM_TYPE_ERROR_CODE);
        }
        return Result.ok();
    }

    /**
     * 图片赋值公共方法
     *
     * @param product
     * @param obj
     */
    public void setCsvPic(Product product, Map<String, Object> obj) {
        //商品主图字段赋值
        String picString = Convert.toStr(obj.get("picture"));
        if (StringUtil.isNotEmpty(picString)) {
            String[] picStrArray = picString.replaceAll("\"", "").split(";");
            List<String> picList = new ArrayList<>(picStrArray.length);
            Arrays.stream(picStrArray).forEach(picStr -> {
                picStr = picStr.replaceAll("\'", "");
                if (StringUtil.isNotEmpty(picStr) && !"''".equals(picStr)) {
                    String[] picArray = picStr.split("\\|");
                    //取的商品主图信息 过滤掉sku图片信息
                    String[] str = picArray[0].split(":");
                    if (str.length < CommonConstants.NUMBER_FOUR) {
                        String pic = picArray[1];
                        if (StringUtil.isNotEmpty(pic)) {
                            picList.add(pic);
                        }
                    }
                }
            });
            if (CollectionUtil.isNotEmpty(picList)) {
                //调用图片上传服务上传图片并获取返回的图片路径
                List<String> pictures = remoteSysOssService.batchDownload(picList);
                if (CollectionUtil.isNotEmpty(pictures)) {
                    String picture = CollectionUtil.join(pictures, ",");
                    product.setPic(pictures.get(CommonConstants.NUMBER_ZERO));
                    product.setAlbumPics(picture);
                }
            }
        }
    }

    /**
     * sku赋值公共方法 同时商品主表限购类型赋值
     *
     * @param product
     * @param skuStocks
     * @param obj
     */
    public void setCsvSku(Product product, List<SkuStock> skuStocks, Map<String, Object> obj) {
        String skuString = Convert.toStr(obj.get("skuProps")).replaceAll("\"", "");
        if (StringUtil.isNotEmpty(skuString)) {
            //限购类型赋值
            product.setLimitType(CommonConstants.NUMBER_ONE);
            //多规格sku信息新增
            String[] skuArray = skuString.split(";");
            Arrays.stream(skuArray).forEach(sku -> {
                if (sku.contains("::")) {
                    String price = sku.split(":")[0];
                    String stock = sku.split(":")[1];
                    SkuStock skuStock = new SkuStock();
                    if (StringUtil.isNotEmpty(price)) {
                        skuStock.setOriginalPrice(Convert.toBigDecimal(price));
                    }
                    if (StringUtil.isNotEmpty(stock)) {
                        skuStock.setStock(Convert.toBigDecimal(stock));
                    }
                    skuStocks.add(skuStock);
                }
            });
        } else {
            //限购类型赋值
            product.setLimitType(CommonConstants.NUMBER_ZERO);
            //单规格sku信息新增
            SkuStock skuStock = new SkuStock();
            if (obj.get("price") != null) {
                skuStock.setOriginalPrice(Convert.toBigDecimal(obj.get("price")));
            }
            if (obj.get("num") != null) {
                skuStock.setStock(Convert.toBigDecimal(obj.get("num")));
            }
            skuStocks.add(skuStock);
        }
    }

    /**
     * 获取素材库商品分页信息
     *
     * @param productParam
     * @return 商品分页对象
     */
    @Override
    public IPage<ProductVo> getCsvProductList(ProductParam productParam) {
        IPage<ProductVo> page = new Page<>(productParam.getCurrent(), productParam.getSize());
        List<ProductVo> productVos = csvProductMapper.queryCsvProductList(page, productParam);
        return page.setRecords(productVos);
    }

    @Override
    public List<Product> getByProductListId(List<Long> productIdList) {
        return this.getBaseMapper().selectList(new LambdaQueryWrapper<Product>().in(Product::getId, productIdList));
    }

    @Override
    public String getGoodsCode() {
        return this.getBaseMapper().getGoodsCode();
    }

    @Override
    public String getPackageGoodsCode() {
        return this.getBaseMapper().getPackageGoodsCode();
    }

    @Override
    public IPage<ProductVo> getByNameOrNumber(ProductDto productDto) {
        IPage<ProductVo> page = new Page<>(productDto.getCurrent(), productDto.getSize());
        IPage<ProductVo> iPage =this.baseMapper.getByNameOrNumber((new Page<>(productDto.getCurrent(), productDto.getSize())),productDto);
        List<ProductVo> records = iPage.getRecords();

        if(CollectionUtil.isNotEmpty(records)){
            List<Long> productIds = records.stream().map(e -> e.getId()).collect(toList());
            Map paramMap = new HashMap(1);
            paramMap.put("productIdList", productIds);
            List<ProductSecUnitVo> productSecUnitList = this.productSecUnitService.queryProductSecUnitByProductIds(paramMap);
            if(CollectionUtil.isNotEmpty(productSecUnitList)){
                Map<Long, List<ProductSecUnitVo>> unitMap = productSecUnitList.stream().collect(Collectors.groupingBy(ProductSecUnitVo::getProductId));
                records.stream().forEach(e -> {
                    //要把基本单位的值加到辅助单位里，否则单位列表无法选择基本单位
                    List<ProductSecUnitVo> productSecUnitVos = unitMap.get(e.getId());
                    if(productSecUnitVos != null){
                        ProductSecUnitVo baseUnit = new ProductSecUnitVo();
                        baseUnit.setProductId(e.getId());
                        baseUnit.setSecUnitd(1);
                        baseUnit.setUnitd(1);
                        baseUnit.setUnit(e.getUnit());
                        baseUnit.setSecUnitId(e.getUnitId());
                        List<ProductSecUnitVo> productSecUnitVoList = new ArrayList<>();
                        productSecUnitVoList.add(baseUnit);
                        productSecUnitVoList.addAll(productSecUnitVos);
                        e.setProductSecUnits(productSecUnitVoList);
                    }
                });
            }
        }

        return page.setRecords(records);
    }

    /**
     * 外部接口接收商品信息 新增商品信息同时插入运费模版、sku、商品属性、商品辅助单位、展示分类信息，同时清除缓存数据
     *
     * @param outProductDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Product outIssueProduct(OutProductDto outProductDto) {
        boolean updateFlag = false;
        Product product = null;
        //更新
        if(null != outProductDto.getId()){
            //判断商品是否存在
            Product productSearch = productMapper.selectById(outProductDto.getId());
            if (BeanUtil.isEmpty(productSearch)) {
                throw new ServiceException("商品不存在！", SystemCode.DATA_EXISTED.getCode());
            }

            updateFlag = true;
        }else{
            //插入操作
            if(outProductDto.getSaleMode()==null){
                LambdaQueryWrapper<SaleMode> lambdaQueryWrapper =new LambdaQueryWrapper<SaleMode>();
                lambdaQueryWrapper.eq(SaleMode::getDefaultSale, SaleModeEnum.DEFAULT_VALUE.getSaleMode());
                List <SaleMode> saleModeList= saleModeService.list(lambdaQueryWrapper);
                if(CollectionUtils.isNotEmpty(saleModeList)){
                    outProductDto.setSaleMode(saleModeList.get(0).getId());
                    //默认状态下架
                    outProductDto.setStatus(ProductStatusEnum.SELL_OFF.getStatus());
                }
            }
            if(outProductDto.getPlace()==null){
                //默认线上
                outProductDto.setPlace(0);
            }
            //调用生成货号方法生成商品货号
            String productSn = getProductSn();
            outProductDto.setProductSn(productSn);
            //状态默认上架
            if (outProductDto.getStatus() == null) {
                outProductDto.setStatus(ProductStatusEnum.SELL_ON.getStatus());
            }

            //判断基本单位是否存在，如果不存在则新增，如果存在则通过名称去查出id
            String unit = outProductDto.getUnit();
            if(StrUtil.isNotEmpty(unit)){
                LambdaQueryWrapper<ProductUnit> productUnitLambdaQueryWrapper = new LambdaQueryWrapper<>();
                productUnitLambdaQueryWrapper.eq(ProductUnit::getUnit, unit);
                List<ProductUnit> unitList = this.productUnitMapper.selectList(productUnitLambdaQueryWrapper);
                if(CollectionUtil.isEmpty(unitList)){
                    //新增基本单位
                    ProductUnit pu = new ProductUnit();
                    Long id = IDUtil.getId();
                    pu.setId(id);
                    pu.setUnit(unit);
                    this.productUnitMapper.insert(pu);
                    outProductDto.setUnitId(id);
                }else{
                    Long unitId = unitList.get(0).getId();
                    outProductDto.setUnitId(unitId);
                }
            }

            product = outProductDto.coverProduct();
            //评分默认5.0
            if (product.getScore() == null) {
                product.setScore(BigDecimal.valueOf(5.0));
            }
            if(StrUtil.isNotEmpty(outProductDto.getSpec())){
                product.setLimitType(1);
            }else{
                product.setLimitType(0);
            }
            product.setOpenSpecs(true);
            product.setDistributionMode(CommonConstants.NUMBER_ONE);
            product.setFreightTemplateId(0L);
            product.setSourceType(SourceTypeEnum.OTHER.getStatus());
            //商品基础信息新增
            int insert = productMapper.insert(product);
            if (insert == 0) {
                throw new ServiceException("发布失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
            outProductDto.setId(product.getId());
            //商品属性信息新增
            List<ProductAttributeDto> productAttributeDtos = outProductDto.getProductAttributes();
            if (CollectionUtil.isNotEmpty(productAttributeDtos)) {
                addProductAttributeList(productAttributeDtos, product.getId());
            }

            //商品辅助单位信息新增
            List<ProductSecUnitDto> productSecUnitDtos = outProductDto.getProductSecUnits();
            if (CollectionUtil.isNotEmpty(productSecUnitDtos)) {
                //商品id赋值
                ProductSecUnit productSecUnit = productSecUnitDtos.get(0).coverProductSecUnit();
                productSecUnit.setProductId(product.getId());
                //辅助单位比值默认就是1
                productSecUnit.setSecUnitd(1);
                productSecUnitService.save(productSecUnit);
            }

            //新增商品展示分类信息
            List<ProductShowCategoryDto> productShowCategoryDtos = outProductDto.getProductShowCategorys();
            if (CollectionUtil.isNotEmpty(productShowCategoryDtos)) {
                addProductShowCategoryList(productShowCategoryDtos, product.getId());
            }

            //商品sku信息新增
            List<SkuStockDto> skuStockDtos = new ArrayList<>();

            SkuStockDto skuStockDto = new SkuStockDto();
            skuStockDto.setSpecs(StrUtil.isNotEmpty(outProductDto.getSpec()) ? outProductDto.getSpec() : "");
            skuStockDto.setSpecs2("");
            skuStockDtos.add(skuStockDto);
            if (CollectionUtil.isNotEmpty(skuStockDtos)) {
                addSkuStock(skuStockDtos, product.getId());
            }

            //新增后商品id赋值
            outProductDto.setId(product.getId());
            outProductDto.setSkuStocks(skuStockDtos);

            ProductDto productDto = new ProductDto();
            BeanUtil.copyProperties(outProductDto, productDto);
            //更新缓存商品基础信息
            updateCacheShoppingCartProduct(productDto);
        }

        if(updateFlag){

            //判断基本单位是否存在，如果不存在则新增，如果存在则通过名称去查出id
            String unit = outProductDto.getUnit();
            if(StrUtil.isNotEmpty(unit)){
                LambdaQueryWrapper<ProductUnit> productUnitLambdaQueryWrapper = new LambdaQueryWrapper<>();
                productUnitLambdaQueryWrapper.eq(ProductUnit::getUnit, unit);
                List<ProductUnit> unitList = this.productUnitMapper.selectList(productUnitLambdaQueryWrapper);
                if(CollectionUtil.isEmpty(unitList)){
                    //新增基本单位
                    ProductUnit pu = new ProductUnit();
                    Long id = IDUtil.getId();
                    pu.setId(id);
                    pu.setUnit(unit);
                    this.productUnitMapper.insert(pu);
                    outProductDto.setUnitId(id);
                }else{
                    Long unitId = unitList.get(0).getId();
                    outProductDto.setUnitId(unitId);
                }
            }

            //商品基础信息修改
            product = outProductDto.coverProduct();
            int update = productMapper.updateById(product);
            if (update == 0) {
                throw new ServiceException("修改失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }

            //商品属性信息修改(先删除再新增)--暂不对接
            /*productAttributeMapper.delete(new QueryWrapper<ProductAttribute>().eq("product_id", outProductDto.getId()));
            List<ProductAttributeDto> productAttributeDtos = outProductDto.getProductAttributes();
            if (CollectionUtil.isNotEmpty(productAttributeDtos)) {
                addProductAttributeList(productAttributeDtos, outProductDto.getId());
            }*/

            //更新商品辅助单位信息--暂不对接
            /*List<ProductSecUnitDto> productSecUnits = outProductDto.getProductSecUnits();
            if (CollectionUtil.isNotEmpty(productSecUnits)) {
                ProductSecUnit productSecUnit = productSecUnits.get(0).coverProductSecUnit();
                if(productSecUnits.get(0).getId()!=null){
                    productSecUnitService.updateById(productSecUnit);
                }else{
                    productSecUnit.setProductId(outProductDto.getId());
                    productSecUnitService.save(productSecUnit);
                }
            }*/


            //商品展示分类删除--暂不对接
            /*productShowCategoryMapper.delete(new QueryWrapper<ProductShowCategory>().eq("product_id", outProductDto.getId()));
            //商品展示分类信息新增
            List<ProductShowCategoryDto> productShowCategoryDtos = outProductDto.getProductShowCategorys();
            if (CollectionUtil.isNotEmpty(productShowCategoryDtos)) {
                //新增展示分类信息
                addProductShowCategoryList(productShowCategoryDtos, outProductDto.getId());
            }*/
            //查出原来的sku信息
            LambdaQueryWrapper<SkuStock> skuWrapper = new LambdaQueryWrapper<>();
            skuWrapper.eq(SkuStock::getProductId, outProductDto.getId());
            List<SkuStock> skuStockList = this.skuStockMapper.selectList(skuWrapper);
            List<SkuStock> newSkuStockList = new ArrayList<>(skuStockList.size() + 1);
            if(StrUtil.isNotEmpty(outProductDto.getSpec())){
                List<SkuStock> existsList = skuStockList.stream().filter(e -> outProductDto.getSpec().equals(e.getSpecs())).collect(toList());
                if(CollectionUtil.isEmpty(existsList)){
                    //没有相同的规格存在，则将新的规格新增进去
                    SkuStock skuStock = new SkuStock();
                    skuStock.setSpecs(outProductDto.getSpec());
                    newSkuStockList.add(skuStock);
                }
            }else{
                //加一个空规格的sku
                List<SkuStock> existsList = skuStockList.stream().filter(e -> StrUtil.isEmpty(e.getSpecs())).collect(toList());
                if(CollectionUtil.isEmpty(existsList)){
                    //没有相同的规格存在，则将新的规格新增进去
                    SkuStock skuStock = new SkuStock();
                    skuStock.setSpecs("");
                    newSkuStockList.add(skuStock);
                }
            }
            newSkuStockList.addAll(skuStockList);

            //更新sku信息(
            List<SkuStockDto> skuStockDtos = new ArrayList<>(newSkuStockList.size());
            if (CollectionUtil.isNotEmpty(newSkuStockList)) {
                newSkuStockList.forEach(e -> {
                    SkuStockDto skuStockDto = new SkuStockDto();
                    BeanUtil.copyProperties(e, skuStockDto);
                    skuStockDtos.add(skuStockDto);
                });
                updateSkuStockByProduct(skuStockDtos, product.getId());
            }
            outProductDto.setSkuStocks(skuStockDtos);
            ProductDto productDto = new ProductDto();
            BeanUtil.copyProperties(outProductDto, productDto);
            //更新缓存商品基础信息
            updateCacheShoppingCartProduct(productDto);
        }
        return product;
    }

    /**
     * 批量更新商品得分
     * @param productList
     */
    @Override
    public Boolean updateScoreBatch(List<Product> productList){
        List<Product> dbProductList = new ArrayList<>(productList.size());
        productList.stream().forEach(e -> {
            Product p = this.baseMapper.selectById(e.getId());
            p.setScore(e.getScore());
            dbProductList.add(p);
        });
        return this.updateBatchById(dbProductList);
    }

    @Override
    public PageUtils<List<ProductVo>> externalGoodsList(Integer page, Integer size) {
        LambdaQueryWrapper<Product>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Product::getStatus, ProductStatusEnum.SELL_ON.getStatus()).and(t->t.isNull(Product::getSendStatus).or().eq(Product::getSendStatus, SendStatusEnum.NO.getStatus()));
        Page<Product> iPage = this.baseMapper.selectPage(new Page<>(page, size), lambdaQueryWrapper);
        List<Product> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<ProductVo> vos = new LinkedList<>();
        setPcGoodsListVos(records,vos);
        //将发送的数据状态改为已发送
        List<Long> idList=records.stream().map(Product::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    public void updateSendStatus(List<Long> productIds, String sendStatus) {
        this.baseMapper.updateSendStatus(productIds,sendStatus);
    }

    @Override
    public PageUtils<AddProductVo> getProduct(AddIntegralProductParam addIntegralProductParam) {
        OrderSetting orderSetting = remoteOrderService.getOrderSetting();
        Boolean b = orderSetting.getOpenNegativeOrder();
        addIntegralProductParam.setIsStock(b);
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        IPage<AddProductVo> addProductVo = this.baseMapper.getAddProductVo(new Page(addIntegralProductParam.getCurrent(),addIntegralProductParam.getSize()),addIntegralProductParam);
        ShopContextHolder.setShopId(shopId);
        return new PageUtils<AddProductVo>(addProductVo);
    }

    @Override
    public PageUtils<AddProductPackageVo> getAddProductPackageVo(AddPackageProductParam addPackageProductParam) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        Page page = new Page(addPackageProductParam.getCurrent(), addPackageProductParam.getSize());
        page.setOptimizeCountSql(false);
        IPage<AddProductPackageVo> addProductPackageVo = this.baseMapper.getAddProductPackageVo(page, addPackageProductParam);

        List<AddProductPackageVo> records = addProductPackageVo.getRecords();
        if(records!=null&&records.size()>0){
            for (AddProductPackageVo packageVo : records) {
                String productId = packageVo.getProductId();
                LambdaQueryWrapper<ProductShowCategory>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductShowCategory::getProductId,productId);
                wrapper.ne(ProductShowCategory::getParentId,0);
                List<ProductShowCategory> productShowCategoryList = productShowCategoryMapper.selectList(wrapper);
                String categoryName = "";
                if(productShowCategoryList!=null&&productShowCategoryList.size()>0){
                    for (ProductShowCategory productShowCategory : productShowCategoryList) {
                        Long showCategoryId = productShowCategory.getShowCategoryId();
                        ShowCategory showCategory = showCategoryMapper.selectById(showCategoryId);
                        String name = showCategory.getName();
                        if(StringUtil.isNotEmpty(categoryName)){
                            categoryName+="，";
                        }
                        categoryName+=name;
                    }
                }
                packageVo.setCategoryName(categoryName);
            }
        }
        ShopContextHolder.setShopId(shopId);
        return new PageUtils<AddProductPackageVo>(addProductPackageVo);
    }

    @Override
    public PageUtils<ActivityProductVo> getActivityProduct(ActivityProductParam param) {
        IPage<ActivityProductVo> page = this.baseMapper.getActivityProduct(new Page(param.getCurrent(), param.getSize()), param);
        return new PageUtils<ActivityProductVo>(page);
    }

    /**
     * 获取sku-商品信息分页
     * @param productParam
     * @return
     */
    @Override
    public IPage<ProductVo> querySkuProductList(ProductParam productParam) {
        IPage<ProductVo> page = new Page<>(productParam.getCurrent(), productParam.getSize());
        List<ProductVo> productVos = this.baseMapper.querySkuProductList(page, productParam);
        return page.setRecords(productVos);
    }

    /**
     * 组装商品数据
     * @param records
     * @param vos
     */
    private void setPcGoodsListVos(List<Product> records, List<ProductVo> vos) {
        for (Product product : records) {
            ProductVo vo = new ProductVo();
            BeanUtils.copyProperties(product,vo);
            List<ProductAttributeVo> productAttributeVos = productAttributeMapper.queryByProductId(product.getId());
            vo.setProductAttributes(productAttributeVos);
            List<SkuStockMemberPriceVo> skuStockMemberPriceVoList = skuStockMapper.getByProductId(product.getId());
            vo.setSkuStockMemberPriceVos(skuStockMemberPriceVoList);
            vos.add(vo);
        }
    }

    /**
     * 产品批量设置运费
     * @param ids
     * @param freightTemplateId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductFreightTemplate(Long[] ids, Long freightTemplateId) {
        List<Long> list = Arrays.asList(ids);
        list.stream().forEach(id -> {
            boolean sign = new LambdaUpdateChainWrapper<>(productMapper)
                    .eq(Product::getId, id)
                    .set(Product::getFreightTemplateId, freightTemplateId).update();
            if (!sign) {
                throw new ServiceException("商品运费模板更新失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        });
    }


}
                                                                                                                                                                                                                                        