package com.medusa.gruul.goods.service.api.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.DistanceUtil;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.constant.GoodsProductRedisKey;
import com.medusa.gruul.goods.api.constant.ShoppingCartRedisKey;
import com.medusa.gruul.goods.api.entity.PackageCoupon;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.SaleMode;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.model.param.api.ApiProductParam;
import com.medusa.gruul.goods.api.model.vo.api.*;
import com.medusa.gruul.goods.api.model.vo.manager.ProductAllPackageVo;
import com.medusa.gruul.goods.mapper.api.*;
import com.medusa.gruul.goods.mapper.manager.PackageCouponMapper;
import com.medusa.gruul.goods.mapper.manager.ProductPackageMapper;
import com.medusa.gruul.goods.mapper.manager.SkuStockMapper;
import com.medusa.gruul.goods.service.api.IApiProductService;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.shops.api.entity.ShopCouponCategory;
import com.medusa.gruul.shops.api.entity.ShopCouponProduct;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.AccountCouponVo;
import com.medusa.gruul.shops.api.model.ShopsPartnerCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序商品信息 服务实现类
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Slf4j
@Service
public class ApiProductServiceImpl extends ServiceImpl<ApiProductMapper, Product> implements IApiProductService {

    @Autowired
    private ApiProductMapper productMapper;

    @Autowired
    private ApiAliveProductMapper apiAliveProductMapper;

    @Autowired
    private ApiShowCategoryMapper apiShowCategoryMapper;

    @Autowired
    private ApiSaleModeMapper apiSaleModeMapper;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Autowired
    private ApiProductSkuMapper apiProductSkuMapper;

    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private RemoteShopsService remoteShopsService;

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private ProductPackageMapper productPackageMapper;

    @Autowired
    private PackageCouponMapper packageCouponMapper;
    @Autowired
    private SkuStockMapper skuStockMapper;

    /**
     * 根据主键id查询商品详情
     *
     * @param id 商品id
     * @return 商品信息
     */
    @Override
    public ApiProductVo getProductById(Long id,Double latitude,Double longitude,Integer priceType) {
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId="";
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        //购物车sku和数量的对应关系
        Map<Long, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            apiShoppingCartVos = apiShoppingCartVos.stream().filter(e -> e.getPriceType() == priceType).collect(Collectors.toList());
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(ApiShoppingCartVo::getSkuId, ApiShoppingCartVo::getGoodsNumber));
        }

        Map<Object,Object> map=new HashMap<>();
        map.put("id",id);
        map.put("memberLevelId",memberLevelId);



        ApiProductVo apiProductVo = productMapper.queryByPrimaryKey(map);

        if(priceType == CommonConstants.NUMBER_TWO){
            apiProductVo = productMapper.queryAgainByPrimaryKey(map);
        }

        if (BeanUtil.isEmpty(apiProductVo)) {
            throw new ServiceException("商品不存在！", SystemCode.DATA_EXISTED.getCode());
        }
        if (CollectionUtil.isNotEmpty(apiProductVo.getSkuStocks())) {
            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
            Map<Long, Integer> finalSkuNumMap = skuNumMap;
            int skuCount = apiProductVo.getSkuStocks().size();
            ApiProductVo finalApiProductVo = apiProductVo;
            apiProductVo.getSkuStocks().stream().forEach(e -> {
                if (e.getMemberLevelPrice()!=null){
                    if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()== finalApiProductVo.getMemberPriceType()){
                        e.setPrice(e.getMemberLevelPrice());
                    }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()== finalApiProductVo.getMemberPriceType()){
                        e.setPrice(e.getPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                    }
                }
                if(e.getStock() != null && e.getStock().compareTo(new BigDecimal("0")) < 0){
                    e.setStock(new BigDecimal("0"));
                }
                if(StrUtil.isNotBlank(e.getSpecs()) && StrUtil.isNotBlank(e.getSpecs2())){
                    e.setSpecs(e.getSpecs()+" "+e.getSpecs2());
                }
                if(finalSkuNumMap.get(e.getId()) != null){
                    e.setGoodsNumber(finalSkuNumMap.get(e.getId()));
                    if(skuCount == 1){
                        finalApiProductVo.setGoodsNumber(finalSkuNumMap.get(e.getId()));
                    }
                }
            });

        }
        Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
        ShopInfoDto shopInfoDto = result.getData();
        if(shopInfoDto!=null){
            apiProductVo.setShopLogo(shopInfoDto.getLogoUrl());
        }
        //设置店铺信息和店铺距离
        if(StrUtil.isNotBlank(apiProductVo.getShopId())){
            ShopsPartner e = remoteShopsService.getByShopId(Long.parseLong(apiProductVo.getShopId()));
            if(null!= e ){
                apiProductVo.setShopName(e.getName());
                apiProductVo.setShopLogo(e.getLogo());
                if(e.getMapX() != null && e.getMapY() != null && latitude!=null && longitude!=null){
                    Double mapX = e.getMapX();
                    Double mapY = e.getMapY();
                    double distance = DistanceUtil.distanceByLngLat(longitude, latitude, mapX, mapY);
                    String s = DistanceUtil.formatDistance(distance);
                    apiProductVo.setDistance(s);
                    apiProductVo.setMapX(mapX);
                    apiProductVo.setMapY(mapY);
                }

            }
        }
        //如果是权益包商品，添加查询权益包信息以及购买赠送优惠券信息
        if(apiProductVo.getProductType()==ProductTypeEnum.PACKAGE_PRODUCT.getStatus()){
            //查询权益包商品
            List<ProductAllPackageVo> productAllPackageList = productPackageMapper.findProductPackageByPackageId(apiProductVo.getId());
            apiProductVo.setProductAllPackageList(productAllPackageList);
            //查询权益包赠送优惠券
            LambdaQueryWrapper<PackageCoupon>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PackageCoupon::getPackageId,apiProductVo.getId());
            List<PackageCoupon> packageCouponList = packageCouponMapper.selectList(wrapper);
            if(packageCouponList!=null&&packageCouponList.size()>0){
                List<ApiShopCouponVo>apiShopCouponVoList = new ArrayList<>();
                for (PackageCoupon packageCoupon : packageCouponList) {
                    AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(Long.valueOf(packageCoupon.getCouponId()));
                    ApiShopCouponVo apiShopCouponVo = new ApiShopCouponVo();
                    BeanUtils.copyProperties(accountCouponVo,apiShopCouponVo);
                    apiShopCouponVoList.add(apiShopCouponVo);
                }
                apiProductVo.setApiShopCouponList(apiShopCouponVoList);
            }
        }
        return apiProductVo;
    }

    @Override
    public ApiProductVo getIntegralProductById(Long id,Double latitude,Double longitude) {
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId="";
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        Map<Object,Object> map=new HashMap<>();
        map.put("id",id);
        map.put("memberLevelId",memberLevelId);
        ApiProductVo apiProductVo = productMapper.queryIntegralByPrimaryKey(map);
        if (BeanUtil.isEmpty(apiProductVo)) {
            throw new ServiceException("商品不存在！", SystemCode.DATA_EXISTED.getCode());
        }
        if (CollectionUtil.isNotEmpty(apiProductVo.getSkuStocks())) {
            apiProductVo.getSkuStocks().stream().forEach(e -> {
                if(e.getStock() != null && e.getStock().compareTo(new BigDecimal("0")) < 0){
                    e.setStock(new BigDecimal("0"));
                }
                //获取用户已购买数量
                BigDecimal userBuyNum = remoteOrderService.getUserBuyNumByUserId(curUserDto.getUserId(), e.getId().toString(), e.getProductId());
                if(userBuyNum==null){
                    userBuyNum = new BigDecimal(0);
                }
                e.setUserBuyNum(userBuyNum);
            });
        }
        //设置店铺信息和店铺距离
        if(StrUtil.isNotBlank(apiProductVo.getShopId())){
            ShopsPartner e = remoteShopsService.getByShopId(Long.parseLong(apiProductVo.getShopId()));
            if(null!= e ){
                apiProductVo.setShopName(e.getName());
                apiProductVo.setShopLogo(e.getLogo());
                if(e.getMapX() != null && e.getMapY() != null && latitude!=null && longitude!=null){
                    Double mapX = e.getMapX();
                    Double mapY = e.getMapY();
                    double distance = DistanceUtil.distanceByLngLat(longitude, latitude, mapX, mapY);
                    String s = DistanceUtil.formatDistance(distance);
                    apiProductVo.setDistance(s);
                    apiProductVo.setMapX(mapX);
                    apiProductVo.setMapY(mapY);
                }

            }
        }
        return apiProductVo;
    }

    /**
     * 商品分页列表
     *
     * @param apiProductParam 商品查询条件
     * @return 分页对象
     */
    @Override
    public IPage<ApiAliveProductVo> getPageList(ApiProductParam apiProductParam) {
        IPage<ApiAliveProductVo> aliveProductVoPage = new Page<>(apiProductParam.getCurrent(), apiProductParam.getSize());
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId=null;
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        //购物车sku和数量的对应关系
        Map<String, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(e -> (e.getSkuId() + "_" + e.getPriceType()), e -> e.getGoodsNumber()));
        }

        if(apiProductParam.getCouponId()!=null){
            AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(apiProductParam.getCouponId());
            Integer couponType = accountCouponVo.getCouponType();
            if(couponType == CommonConstants.NUMBER_TWO){//商品优惠券
                List<ShopCouponProduct> shopCouponProductList = remoteShopsService.getShopCouponProduct(apiProductParam.getCouponId());
                if(shopCouponProductList!=null&&shopCouponProductList.size()>0){
                    List<String> productIds = shopCouponProductList.stream().map(ShopCouponProduct::getProductId).distinct().collect(Collectors.toList());
                    apiProductParam.setProductIds(productIds);
                }
            }
            if(couponType == CommonConstants.NUMBER_THREE){//品类优惠券
                List<ShopCouponCategory> shopCouponCategoryList = remoteShopsService.getShopCouponCategory(apiProductParam.getCouponId());
                if(shopCouponCategoryList!=null&&shopCouponCategoryList.size()>0){
                    List<String> categoryIds = shopCouponCategoryList.stream().map(ShopCouponCategory::getCategoryId).distinct().collect(Collectors.toList());
                    apiProductParam.setShowCategoryIds(categoryIds);
                }
            }

        }


        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.querySuperMarketProductList(aliveProductVoPage, apiProductParam,memberLevelId);
        //加入能直接加入购物车所需要的参数
        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
            String finalMemberLevelId1 = memberLevelId;
            Map<String, Integer> finalSkuNumMap = skuNumMap;

            Integer priceType = 3;
            if(apiProductParam.getPriceType()!=null){
                priceType = apiProductParam.getPriceType();
            }
            Integer finalPriceType = priceType;
            aliveProductVos.stream().forEach(e -> {
                if(finalPriceType == CommonConstants.NUMBER_ONE){
                    if (e.getMemberLevelPrice()!=null){
                        if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                            e.setMinPrice(e.getMemberLevelPrice());
                        }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                            e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }else if(finalPriceType == CommonConstants.NUMBER_TWO){
                    if (e.getMemberAgainPriceType()!=null){
                        if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberAgainPriceType()){
                            e.setMinPrice(e.getMemberLevelAgainPrice());
                        }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberAgainPriceType()){
                            e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }else{
                    if (e.getMemberLevelPrice()!=null){
                        if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                            e.setMinPrice(e.getMemberLevelPrice());
                        }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                            e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }

                //查询商品sku信息
                Map<Object, Object> paramMap = new HashMap<>();
                paramMap.put("memberLevelId", finalMemberLevelId1);
                paramMap.put("id", e.getId());
                List<ApiSkuStockVo> apiSkuStockVoList = apiProductSkuMapper.querySkuStock(paramMap);
                e.setSkuStocks(apiSkuStockVoList);
                //设置sku购物车的数量
                if(finalSkuNumMap.get(e.getSkuId()+"_"+finalPriceType) != null){
                    e.setGoodsNumber(finalSkuNumMap.get(e.getSkuId()+"_"+finalPriceType));
                }
            });
        }

        return aliveProductVoPage.setRecords(aliveProductVos);
    }




    /**
     * 商超系统分类列表
     *
     * @param apiProductParam 商品查询条件
     * @return 分页对象
     */
    @Override
    public IPage<ApiAliveProductVo> getSupermarketList(ApiProductParam apiProductParam) {
        IPage<ApiAliveProductVo> aliveProductVoPage = new Page<>(apiProductParam.getCurrent(), apiProductParam.getSize());
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberId=null;
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员id
            memberId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        //购物车sku和数量的对应关系
        Map<Long, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(ApiShoppingCartVo::getSkuId, ApiShoppingCartVo::getGoodsNumber));
        }

        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.querySuperMarketProductList(aliveProductVoPage, apiProductParam, memberId);

        //加入能直接加入购物车所需要的参数
        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
            String finalMemberLevelId1 = memberId;
            Map<Long, Integer> finalSkuNumMap = skuNumMap;
            aliveProductVos.stream().forEach(e -> {
                if (e.getMemberLevelPrice()!=null){
                    if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMemberLevelPrice());
                    }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                    }
                }
                //查询商品sku信息
                Map<Object, Object> paramMap = new HashMap<>();
                paramMap.put("memberLevelId", finalMemberLevelId1);
                paramMap.put("id", e.getId());
                List<ApiSkuStockVo> apiSkuStockVoList = apiProductSkuMapper.querySkuStock(paramMap);
                e.setSkuStocks(apiSkuStockVoList);
                //设置sku购物车的数量
                if(finalSkuNumMap.get(e.getSkuId()) != null){
                    e.setGoodsNumber(finalSkuNumMap.get(e.getSkuId()));
                }
            });
        }

        return aliveProductVoPage.setRecords(aliveProductVos);
    }

    //=============================================商品组件根据商品集合匹配未删除的商品===================================================

    /**
     * 根据商品数组匹配未删除的商品
     *
     * @param ids
     * @param launchArea
     * @param saleMode
     * @return List<DiscountProductVo>
     */
    @Override
    public List<ApiAliveProductVo> getAliveProductList(Long[] ids, String launchArea, Long saleMode,Double latitude,Double longitude,Integer priceType) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId=null;
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员等级id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        log.info("{}用户的购物车数据：{}", userKey, stringList);
        //购物车sku和数量的对应关系
        Map<Long, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            apiShoppingCartVos = apiShoppingCartVos.stream().filter(e -> e.getPriceType() == priceType).collect(Collectors.toList());
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(ApiShoppingCartVo::getSkuId, ApiShoppingCartVo::getGoodsNumber));
        }

        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.querySaveProductList(Arrays.asList(ids), saleMode, memberLevelId);

        if(priceType == CommonConstants.NUMBER_TWO){
            aliveProductVos = apiAliveProductMapper.querySaveProductAgainList(Arrays.asList(ids), saleMode, memberLevelId);
        }


        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
            //查询商家分类
            // 店铺id
            List<Long> partnerIdList = aliveProductVos.stream().map(s -> Long.parseLong(s.getShopsPartnerId())).collect(Collectors.toList());
            List<ShopsPartnerCategoryVo> partnerCategoryVoList = this.remoteShopsService.getManyShopsCategoryName(partnerIdList);
            Map<Long, List<ShopsPartnerCategoryVo>> partnerCategoryVoMap = partnerCategoryVoList.stream().collect(Collectors.groupingBy(ShopsPartnerCategoryVo::getShopsPartnerId));

            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
            String finalMemberLevelId1 = memberLevelId;
            Map<Long, Integer> finalSkuNumMap = skuNumMap;
            aliveProductVos.stream().forEach(e -> {
                if (e.getMemberLevelPrice()!=null){
                    if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMemberLevelPrice());
                    }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                    }
                }
                //查询商品sku信息
                Map<Object, Object> paramMap = new HashMap<>();
                paramMap.put("memberLevelId", finalMemberLevelId1);
                paramMap.put("id", e.getId());
                List<ApiSkuStockVo> apiSkuStockVoList = apiProductSkuMapper.querySkuStock(paramMap);
                e.setSkuStocks(apiSkuStockVoList);
                //设置sku购物车的数量
                if(finalSkuNumMap.get(e.getSkuId()) != null){
                    e.setGoodsNumber(finalSkuNumMap.get(e.getSkuId()));
                }
                if(e.getMapX()!=null&&e.getMapY()!=null&&latitude!=null&&longitude!=null){
                    Double mapX = e.getMapX();
                    Double mapY = e.getMapY();
                    double distance = DistanceUtil.distanceByLngLat(longitude, latitude, mapX, mapY);
                    String s = DistanceUtil.formatDistance(distance);
                    e.setDistance(s);

                }
                // 设置商家分类
                List<ShopsPartnerCategoryVo> tempList = partnerCategoryVoMap.get(Long.parseLong(e.getShopsPartnerId()));
                if(CollectionUtil.isNotEmpty(tempList)){
                    String names = tempList.stream().map(ShopsPartnerCategoryVo::getName).collect(Collectors.joining(","));
                    e.setShopsCategoryName(names);
                }
            });
        }
        ShopContextHolder.setShopId(shopId);
        return aliveProductVos;
    }


    //=============================================商品分类页组件根据商品分类集合匹配对应分类下的商品===================================================

    /**
     * pc商品分类集合匹配对应分类下的商品
     *
     * @param ids
     * @return List<DiscountProductVo>
     */
    @Override
    public List<ApiShowCategoryProductVo> getAliveProductListGroupByCategory(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        List<ApiShowCategoryVo> apiShowCategoryVos = apiShowCategoryMapper.queryAllApiShowCategoryList();
        List<ApiShowCategoryProductVo> apiShowCategoryProductVos = new ArrayList<>(apiShowCategoryVos.size());
        if (CollectionUtil.isNotEmpty(apiShowCategoryVos)) {
            apiShowCategoryVos.stream().forEach(apiShowCategoryVo -> {
                if (idList.contains(apiShowCategoryVo.getId()) && CollectionUtil.isNotEmpty(apiShowCategoryVo.getShowCategoryVos())) {
                    apiShowCategoryVo.getShowCategoryVos().stream().forEach(apiShowCategorySecondVo -> {
                        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.queryShowCategoryProductList(apiShowCategorySecondVo.getId());
                        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
                            ApiShowCategoryProductVo apiShowCategoryProductVo = new ApiShowCategoryProductVo();
                            apiShowCategoryProductVo.setId(apiShowCategorySecondVo.getId());
                            apiShowCategoryProductVo.setName(apiShowCategorySecondVo.getName());
                            apiShowCategoryProductVo.setApiAliveProductVos(aliveProductVos);
                            apiShowCategoryProductVos.add(apiShowCategoryProductVo);
                        }
                    });
                }
            });
        }
        return apiShowCategoryProductVos;
    }

    @Override
    public List<ApiAliveProductVo> getOrderHistoryGoods() {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId=null;
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员等级id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }
        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        //购物车sku和数量的对应关系
        Map<Long, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(ApiShoppingCartVo::getSkuId, ApiShoppingCartVo::getGoodsNumber));
        }
        //获取历史订单商品ids
        List<Long> ids= remoteOrderService.getIdsByOrderHistory(userKey);
        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.querySaveProductList(ids, null, memberLevelId);
        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
            String finalMemberLevelId1 = memberLevelId;
            Map<Long, Integer> finalSkuNumMap = skuNumMap;
            aliveProductVos.stream().forEach(e -> {
                if (e.getMemberLevelPrice()!=null){
                    if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMemberLevelPrice());
                    }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                        e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                    }
                }
                //查询商品sku信息
                Map<Object, Object> paramMap = new HashMap<>();
                paramMap.put("memberLevelId", finalMemberLevelId1);
                paramMap.put("id", e.getId());
                List<ApiSkuStockVo> apiSkuStockVoList = apiProductSkuMapper.querySkuStock(paramMap);
                e.setSkuStocks(apiSkuStockVoList);
                //设置sku购物车的数量
                if(finalSkuNumMap.get(e.getSkuId()) != null){
                    e.setGoodsNumber(finalSkuNumMap.get(e.getSkuId()));
                }
            });
        }

        return aliveProductVos;
    }

    @Override
    @Transactional
    public void updatePackageGoodsStatus() {
        Date now = new Date();
        List<Long>list = new ArrayList<>();
        //1.当前时间大于权益包展示结束时间
        LambdaQueryWrapper<Product>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStatus, CommonConstants.NUMBER_ONE);
        wrapper.eq(Product::getProductType,ProductTypeEnum.PACKAGE_PRODUCT.getStatus());
        wrapper.lt(Product::getPackageShowEndTime,now);
        List<Product> productList = productMapper.selectList(wrapper);
        if(productList!=null&&productList.size()>0){
            for (Product product : productList) {
                product.setStatus(CommonConstants.NUMBER_ZERO);
                productMapper.updateById(product);
                list.add(product.getId());
            }
        }
        //2.当前时间小于权益包展示开始时间
        LambdaQueryWrapper<Product>wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(Product::getStatus, CommonConstants.NUMBER_ONE);
        wrapper2.eq(Product::getProductType,ProductTypeEnum.PACKAGE_PRODUCT.getStatus());
        wrapper2.gt(Product::getPackageShowStartTime,now);
        List<Product> productList2 = productMapper.selectList(wrapper2);
        if(productList2!=null&&productList2.size()>0){
            for (Product product : productList2) {
                product.setStatus(CommonConstants.NUMBER_ZERO);
                productMapper.updateById(product);
                list.add(product.getId());
            }
        }
        //3.当前时间大于权益包使用结束时间
        LambdaQueryWrapper<Product>wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(Product::getStatus, CommonConstants.NUMBER_ONE);
        wrapper3.eq(Product::getProductType,ProductTypeEnum.PACKAGE_PRODUCT.getStatus());
        wrapper3.lt(Product::getPackageEndTime,now);
        List<Product> productList3 = productMapper.selectList(wrapper3);
        if(productList3!=null&&productList3.size()>0){
            for (Product product : productList3) {
                product.setStatus(CommonConstants.NUMBER_ZERO);
                productMapper.updateById(product);
                list.add(product.getId());
            }
        }

        //4.当前时间小于权益包使用开始时间
        LambdaQueryWrapper<Product>wrapper4 = new LambdaQueryWrapper<>();
        wrapper4.eq(Product::getStatus, CommonConstants.NUMBER_ONE);
        wrapper4.eq(Product::getProductType,ProductTypeEnum.PACKAGE_PRODUCT.getStatus());
        wrapper4.gt(Product::getPackageStartTime,now);
        List<Product> productList4 = productMapper.selectList(wrapper4);
        if(productList4!=null&&productList4.size()>0){
            for (Product product : productList4) {
                product.setStatus(CommonConstants.NUMBER_ZERO);
                productMapper.updateById(product);
                list.add(product.getId());
            }
        }
        if(list!=null&&list.size()>0){
            Long[] ids = list.toArray(new Long[list.size()]);
            //更新购物车商品状态缓存信息
            updateCacheShoppingCartProductStatus(ids,CommonConstants.NUMBER_ZERO);
        }

    }
    /**
     * 更新购物车商品状态缓存信息
     *
     * @param ids
     */
    public void updateCacheShoppingCartProductStatus(Long[] ids, Integer status) {
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        Arrays.asList(ids).stream().forEach(id -> {
            ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(String.valueOf(id)), ApiShoppingCartProductVo.class);
            if (!BeanUtil.isEmpty(apiShoppingCartProductVo)) {
                apiShoppingCartProductVo.setStatus(status);
                goodsProductRedisKey.set(String.valueOf(id), JSON.toJSONString(apiShoppingCartProductVo));
            }
        });
    }
    /**
     * 小程序商品分类集合匹配对应分类下的商品
     *
     * @param ids
     * @param saleMode
     * @return List<DiscountProductVo>
     */
    @Override
    public List<ApiShowCategoryProductVo> getAliveProductListByCategory(Long[] ids, Long saleMode,Double latitude,Double longitude,Integer priceType) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);

        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String memberLevelId=null;
        if (curUserDto != null) {
            //查询用户的会员id
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
            //会员等级id
            memberLevelId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        }

        //用户购物车缓存key值 用户id
        String userKey = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        //购物车sku和数量的对应关系
        Map<Long, Integer> skuNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stringList)) {
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            apiShoppingCartVos = apiShoppingCartVos.stream().filter(e -> e.getPriceType() == priceType).collect(Collectors.toList());
            skuNumMap = apiShoppingCartVos.stream().collect(Collectors.toMap(ApiShoppingCartVo::getSkuId, ApiShoppingCartVo::getGoodsNumber));
        }

        SaleMode saleModeSearch = apiSaleModeMapper.selectOne(new QueryWrapper<SaleMode>().eq("id", saleMode));
        if (BeanUtil.isEmpty(saleModeSearch)) {
            return new ArrayList<>(CommonConstants.NUMBER_ZERO);
        }
        List<Long> idList = Arrays.asList(ids);
        List<ApiShowCategoryVo> apiShowCategoryVos;
        //查询分类
        apiShowCategoryVos = apiShowCategoryMapper.queryApiSupermarketShowCategoryList(saleMode);
        List<ApiShowCategoryProductVo> apiShowCategoryProductVos = new ArrayList<>(apiShowCategoryVos.size());
        if (CollectionUtil.isNotEmpty(apiShowCategoryVos)) {
            String finalMemberLevelId = memberLevelId;
            Map<Long, Integer> finalSkuNumMap = skuNumMap;
            apiShowCategoryVos.stream().forEach(apiShowCategoryVo -> {
                //判断是否有二级分类并且在过滤的一级分类下
                if (idList.contains(apiShowCategoryVo.getId()) && CollectionUtil.isNotEmpty(apiShowCategoryVo.getShowCategoryVos())) {
                    apiShowCategoryVo.getShowCategoryVos().stream().forEach(apiShowCategorySecondVo -> {

                        List<ApiAliveProductVo> aliveProductVos = apiAliveProductMapper.queryShowCategoryProductListBySaleMode(apiShowCategorySecondVo.getId(), saleMode, finalMemberLevelId);

                        if(priceType == CommonConstants.NUMBER_TWO){
                            aliveProductVos = apiAliveProductMapper.queryShowCategoryAgainProductListBySaleMode(apiShowCategorySecondVo.getId(), saleMode, finalMemberLevelId);
                        }

                        if (CollectionUtil.isNotEmpty(aliveProductVos)) {
                            ApiShowCategoryProductVo apiShowCategoryProductVo = new ApiShowCategoryProductVo();
                            apiShowCategoryProductVo.setId(apiShowCategorySecondVo.getId());
                            apiShowCategoryProductVo.setName(apiShowCategorySecondVo.getName());
                            //如果有会员价就替换到最小价,如果是折扣率就最小价格*折扣率
                            aliveProductVos.stream().forEach(e -> {
                                if (e.getMemberLevelPrice()!=null){
                                    if(ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==e.getMemberPriceType()){
                                        e.setMinPrice(e.getMemberLevelPrice());
                                    }else if(ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==e.getMemberPriceType()){
                                        e.setMinPrice(e.getMinPrice().multiply(e.getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                    }
                                }
                                //查询商品sku信息
                                Map<Object, Object> paramMap = new HashMap<>();
                                paramMap.put("memberLevelId", finalMemberLevelId);
                                paramMap.put("id", e.getId());
                                Long id = e.getId();
                                LambdaQueryWrapper<SkuStock>skuStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
                                skuStockLambdaQueryWrapper.eq(SkuStock::getDeleted,CommonConstants.NUMBER_ZERO);
                                skuStockLambdaQueryWrapper.eq(SkuStock::getProductId,id);
                                List<SkuStock> stockList = skuStockMapper.selectList(skuStockLambdaQueryWrapper);
                                Integer sale = 0;
                                if(stockList!=null&&stockList.size()>0){
                                    for (SkuStock skuStock : stockList) {
                                         sale = sale + skuStock.getSale();
                                    }
                                }
                                e.setSale(sale);
                                List<ApiSkuStockVo> apiSkuStockVoList = apiProductSkuMapper.querySkuStock(paramMap);
                                e.setSkuStocks(apiSkuStockVoList);
                                //设置sku购物车的数量
                                if(finalSkuNumMap.get(e.getSkuId()) != null){
                                    e.setGoodsNumber(finalSkuNumMap.get(e.getSkuId()));
                                }

                                if(e.getMapX()!=null&&e.getMapY()!=null&&latitude!=null&&longitude!=null){
                                    double distance = DistanceUtil.distanceByLngLat(longitude, latitude, e.getMapX(), e.getMapY());
                                    String s = DistanceUtil.formatDistance(distance);
                                    e.setDistance(s);
                                }
                            });
                            //log.info("分类下商品信息，{}" , aliveProductVos.toString());
                            apiShowCategoryProductVo.setApiAliveProductVos(aliveProductVos);
                            apiShowCategoryProductVos.add(apiShowCategoryProductVo);
                        }
                    });
                }
            });
        }
        ShopContextHolder.setShopId(shopId);
        return apiShowCategoryProductVos;

    }
}
