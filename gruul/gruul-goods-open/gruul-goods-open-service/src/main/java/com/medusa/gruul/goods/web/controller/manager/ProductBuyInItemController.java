package com.medusa.gruul.goods.web.controller.manager;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;

import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.goods.api.entity.ProductBuyInItem;
import com.medusa.gruul.goods.api.model.param.manager.ProductBuyInItemParam;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInItemAllVo;
import com.medusa.gruul.goods.service.manager.IProductBuyInItemService;
import lombok.extern.slf4j.Slf4j;
import com.medusa.gruul.common.core.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 购货入库明细
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Slf4j
@Api(tags="购货入库明细")
@RestController
@RequestMapping("/productBuyInDetailed/productBuyInDetailed")
public class ProductBuyInItemController {
	@Autowired
	private IProductBuyInItemService productBuyInItemService;
	
	/**
	 * 分页列表查询
	 *
	 * @param productBuyInDetailed
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-分页列表查询")*/
	@ApiOperation(value="购货入库明细-分页列表查询", notes="购货入库明细-分页列表查询")
	@PostMapping(value = "/list")
	public Result<?> queryPageList(@RequestBody ProductBuyInItemParam productBuyInItemParam) {
		PageUtils<ProductBuyInItemAllVo> pageUtils = new PageUtils(productBuyInItemService.selectList(productBuyInItemParam));
		return Result.ok(pageUtils);
	}
	
	/**
	 * 添加
	 *
	 * @param productBuyInDetailed
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-添加")*/
	@ApiOperation(value="购货入库明细-添加", notes="购货入库明细-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProductBuyInItem productBuyInItem) {
		productBuyInItemService.save(productBuyInItem);
		return Result.ok("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param productBuyInDetailed
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-编辑")*/
	@ApiOperation(value="购货入库明细-编辑", notes="购货入库明细-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProductBuyInItem productBuyInItem) {
		productBuyInItemService.updateById(productBuyInItem);
		return Result.ok("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-通过id删除")*/
	@ApiOperation(value="购货入库明细-通过id删除", notes="购货入库明细-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		productBuyInItemService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-批量删除")*/
	@ApiOperation(value="购货入库明细-批量删除", notes="购货入库明细-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productBuyInItemService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "购货入库明细-通过id查询")*/
	@ApiOperation(value="购货入库明细-通过id查询", notes="购货入库明细-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProductBuyInItem productBuyInItem = productBuyInItemService.getById(id);
		return Result.ok(productBuyInItem);
	}


}
