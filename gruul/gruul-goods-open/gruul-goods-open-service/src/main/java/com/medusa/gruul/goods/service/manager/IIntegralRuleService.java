package com.medusa.gruul.goods.service.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.IntegralRule;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralRuleDto;
import com.medusa.gruul.goods.api.model.vo.manager.IntegralRuleVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: 积分规则服务类
 * @Date: Created in 15:42 2023/8/18
 */
public interface IIntegralRuleService extends IService<IntegralRule> {

    /**
     * 获取积分规则列表
     * @return
     */
    List<IntegralRuleVo> getList();

    /**
     * 添加积分规则
     * @param list
     */
    void add(List<IntegralRuleDto> list);

    /**
     * 更具积分规则类型获取积分规则
     * @param ruleType
     * @return
     */
    IntegralRuleVo getIntegralRuleVoByRuleType(Integer ruleType,String tenantId);
}
