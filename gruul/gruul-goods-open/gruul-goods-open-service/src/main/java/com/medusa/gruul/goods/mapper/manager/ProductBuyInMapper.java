package com.medusa.gruul.goods.mapper.manager;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.goods.api.entity.ProductBuyIn;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Repository
public interface ProductBuyInMapper extends BaseMapper<ProductBuyIn> {
    /**
     * 获取当天的最大入库编号
     *
     * @param time             查询条件

     * @return java.lang.String
     */
    String getReceiptNo(@Param("time") String time);


    /**
     * 获取商品入库列表
     *
     * @param page 分页数据
     * @param productBuyInDto        查询条件参数
     * @return com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo
     */
    IPage<ProductBuyInVo> selectList(IPage page , @Param("productBuyInDto") ProductBuyInDto productBuyInDto);
    /**
     * 根据Id获取商品入库
     *
     *
     * @param id        查询条件参数
     * @return com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo
     */
    ProductBuyInVo getById(Long id);


}
