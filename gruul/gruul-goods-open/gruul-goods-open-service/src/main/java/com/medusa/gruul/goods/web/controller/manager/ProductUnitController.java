package com.medusa.gruul.goods.web.controller.manager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductUnit;
import com.medusa.gruul.goods.api.model.dto.manager.ProductUnitDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.goods.service.manager.IProductService;
import com.medusa.gruul.goods.service.manager.IProductUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 商品基本单位
 * @Author: jeecg-boot
 * @Date:   2022-02-24
 * @Version: V1.0
 */
@Slf4j
@Api(tags="商品基本单位")
@RestController
@RequestMapping("/manager/productUnit")
public class ProductUnitController {
	@Autowired
	private IProductUnitService productUnitService;
     @Autowired
     private IProductService productService;
	
	/**
	 * 分页列表查询
	 *
	 * @param productUnitDto
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-分页列表查询")*/
	@ApiOperation(value="商品基本单位-分页列表查询", notes="商品基本单位-分页列表查询")
    @PostMapping(value = "/list")
	public Result<?> queryPageList(@RequestBody ProductUnitDto productUnitDto) {
        IPage<ProductUnit> page = new Page<>(productUnitDto.getCurrent(), productUnitDto.getSize());
        LambdaQueryWrapper<ProductUnit> lambdaQueryWrapper=new LambdaQueryWrapper<ProductUnit>();
        lambdaQueryWrapper.orderByAsc(ProductUnit::getSort);
        PageUtils<ProductUnit> pageUtils = new PageUtils(productUnitService.page(page,lambdaQueryWrapper));
		return Result.ok(pageUtils);
	}

     /**
      * 查询全部
      * @param productUnitDto
      * @return
      */
     /**@AutoLog(value = "商品基本单位-分页列表查询")*/
     @ApiOperation(value="商品基本单位-查询全部", notes="商品基本单位-查询全部")
     @PostMapping(value = "/all")
     public Result<?> unitAll(@RequestBody ProductUnitDto productUnitDto) {
         LambdaQueryWrapper<ProductUnit> lambdaQueryWrapper=new LambdaQueryWrapper<ProductUnit>();
         lambdaQueryWrapper.orderByAsc(ProductUnit::getSort);
         List<ProductUnit> list = productUnitService.list(lambdaQueryWrapper);
         return Result.ok(list);
     }
	
	/**
	 * 添加
	 *
	 * @param goodsUnit
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-添加")*/
	@ApiOperation(value="商品基本单位-添加", notes="商品基本单位-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody List<ProductUnit> productUnitList) {
        List<String> units=productUnitList.stream().map(ProductUnit::getUnit).collect(Collectors.toList());
        LambdaQueryWrapper<ProductUnit> lambdaQueryWrapper=new LambdaQueryWrapper<ProductUnit>();
        lambdaQueryWrapper.in(ProductUnit::getUnit,units);
        int count=productUnitService.count(lambdaQueryWrapper);
        if(count>0){
            return Result.failed("商品基本单位已存在！");
        }
        productUnitService.saveBatch(productUnitList);
		return Result.ok("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param goodsUnit
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-编辑")*/
	@ApiOperation(value="商品基本单位-编辑", notes="商品基本单位-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody ProductUnit productUnit) {
        productUnitService.updateById(productUnit);
		return Result.ok("编辑成功!");
	}

     /**
      * 排序"
      *
      * @param goodsUnit
      * @return
      */
     /**@AutoLog(value = "商品基本单位-排序"")*/
     @ApiOperation(value="商品基本单位-排序", notes="商品基本单位-排序")
     @PostMapping(value = "/sort")
     public Result<?> sort(@RequestBody List<ProductUnit> productUnitList) {
         productUnitService.sort(productUnitList);
         return Result.ok();
     }
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-通过id删除")*/
	@ApiOperation(value="商品基本单位-通过id删除", notes="商品基本单位-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestBody Map paramMap) {
        Long id = Long.parseLong((String)paramMap.get("id"));
        LambdaQueryWrapper<Product> lambdaQueryWrapper=new LambdaQueryWrapper<Product>();
        lambdaQueryWrapper.eq(Product::getUnitId,id);
        int i=productService.count(lambdaQueryWrapper);
        String str="删除成功!";
        if(i==0){
            productUnitService.removeById(id);
        }else {
            str="该基本单位已经和商品绑定不能删除！";
        }

		return Result.ok(str);
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-批量删除")*/
	@ApiOperation(value="商品基本单位-批量删除", notes="商品基本单位-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestBody Map paramMap) {
        String ids = (String)paramMap.get("ids");
        List<Long> list = new ArrayList<Long>();
        Arrays.asList(ids.split(",")).stream().forEach(e->{
            list.add(Long.parseLong(e));
        });
        this.productUnitService.removeByIds(list);
		return Result.ok("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "商品基本单位-通过id查询")*/
	@ApiOperation(value="商品基本单位-通过id查询", notes="商品基本单位-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ProductUnit productUnit = productUnitService.getById(id);
		return Result.ok(productUnit);
	}


}
