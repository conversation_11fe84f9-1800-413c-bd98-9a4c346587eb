package com.medusa.gruul.goods.web.controller.manager;

import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.goods.api.model.dto.manager.ProductDto;
import com.medusa.gruul.goods.service.manager.IMemberLevelGoodsAgainPriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:23 2025/3/21
 */
@Slf4j
@Api(tags="会员等级复购价格")
@RestController
@RequestMapping("/manager/memberLevelGoodsAgainPrice")
public class MemberLevelGoodsAgainPriceController {

    @Autowired
    private IMemberLevelGoodsAgainPriceService memberLevelGoodsAgainPriceService;

    @ApiOperation(value="会员等级复购价格-添加", notes="会员等级复购价格-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProductDto productDto) {
        memberLevelGoodsAgainPriceService.addOrUpdate(productDto);
        return Result.ok("编辑成功！");
    }



}
