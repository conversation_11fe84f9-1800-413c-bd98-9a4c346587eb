package com.medusa.gruul.goods.service.manager;



import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.ProductBuyIn;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;

import java.util.Map;


/**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
public interface IProductBuyInService extends IService<ProductBuyIn> {
    /**
     * 获取当天的最大入库编号
     *
     * @param time             查询条件

     * @return java.lang.String
     */
    String getReceiptNo(String time);

    /**
     * 获取商品入库列表
     *
     * @param productBuyInDto             查询条件

     * @return com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo
     */
    IPage<ProductBuyInVo> selectList(ProductBuyInDto productBuyInDto);

    /**
     * 通过Id获取商品入库信息
     *
     * @param id             查询条件

     * @return com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo
     */
    ProductBuyInVo getById(Long id);

    /**
     * 添加商品入库信息
     *
     * @param productBuyInDto             需要添加的信息

     */
    void add(ProductBuyInDto productBuyInDto);

    /**
     * 修改商品入库信息
     *
     * @param productBuyInDto             需要修改的信息

     */
    void update(ProductBuyInDto productBuyInDto);


    /**
     * 删除商品入库信息
     *
     * @param productBuyInDto             删除入库单的信息

     */
    void delete(ProductBuyInDto productBuyInDto);


    /**
     * 获取入库单号和制单人
     *@return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> getReceiptNoAndUser();
}
