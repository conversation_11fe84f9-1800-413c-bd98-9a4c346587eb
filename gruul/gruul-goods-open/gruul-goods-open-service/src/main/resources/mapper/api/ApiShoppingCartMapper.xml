<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.mapper.api.ApiShoppingCartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartVo">
        <id column="id" property="id"/>
        <result column="is_deleted" property="deleted"/>
        <result column="product_id" property="productId"/>
        <result column="user_id" property="userId"/>
        <result column="sku_id" property="skuId"/>
        <result column="goods_number" property="goodsNumber"/>
        <result column="name" property="productName"/>
        <result column="pic" property="pic"/>
        <result column="product_sn" property="productSn"/>
        <result column="sale_mode" property="saleMode"/>
        <result column="status" property="status"/>
        <collection property="skuStocks" ofType="com.medusa.gruul.goods.api.model.vo.manager.SkuStockVo"
                    column="product_id"
                    select="querySkuStock"></collection>
    </resultMap>

    <resultMap id="SkuStockResultMap" type="com.medusa.gruul.goods.api.model.vo.manager.SkuStockVo">
        <id column="s_id" property="id"/>
        <result column="s_version" property="version"/>
        <result column="s_product_id" property="productId"/>
        <result column="s_sku_code" property="skuCode"/>
        <result column="s_specs" property="specs"/>
        <result column="s_pic" property="pic"/>
        <result column="s_price" property="price"/>
        <result column="s_original_price" property="originalPrice"/>
        <result column="s_stock" property="stock"/>
        <result column="s_low_stock" property="lowStock"/>
        <result column="s_sale" property="sale"/>
        <result column="s_per_limit" property="perLimit"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        c.id, c.product_id, c.user_id, c.sku_id, c.goods_number, p.name, p.pic, p.product_sn, p.sale_mode, p.status
    </sql>

    <sql id="Base_Sku_List">
        s.id AS s_id, s.version AS s_version, s.product_id AS s_product_id, s.sku_code AS s_sku_code, s.specs AS s_specs, s.pic AS s_pic, s.price AS s_price, s.original_price AS s_original_price, s.stock AS s_stock, s.low_stock AS s_low_stock, s.sale AS s_sale, s.per_limit AS s_per_limit
    </sql>

    <sql id="Base_Sku_member_List">
        s.id AS s_id, s.version AS s_version, s.product_id AS s_product_id, s.sku_code AS s_sku_code, s.specs AS s_specs, s.pic AS s_pic, s.price AS s_price, s.original_price AS s_original_price, s.stock AS s_stock, s.low_stock AS s_low_stock, s.sale AS s_sale, s.per_limit AS s_per_limit,
        sum(m.member_level_price) AS member_level_price ,sum(m.member_level_percentage) AS member_level_percentage
    </sql>

    <sql id="Base_Member_List">
        m.id AS m_id, m.product_id AS m_product_id, m.sku_id AS m_sku_id, m.member_level_id AS m_member_level_id, m.member_level_price AS m_member_level_price, m.member_level_name AS m_member_level_name
    </sql>

    <select id="queryShoppingCartListByUserId" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_shopping_cart c
        left JOIN
        t_product p
        ON
        c.product_id = p.id
        WHERE
        c.user_id = #{userId}
        AND
        p.distribution_mode = #{type}
        AND
        c.is_deleted = 0
        order by c.create_time desc
    </select>

    <select id="querySkuStock" resultMap="SkuStockResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Sku_List"/>
        FROM
        t_sku_stock s
        WHERE
        s.is_deleted = 0
        AND
        s.product_id = #{id}
    </select>

    <select id="selectEffectShoppingCart" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_shopping_cart c
        inner JOIN
        t_product p
        ON
        c.product_id = p.id
        WHERE
        c.user_id = #{userId}
        AND
        p.distribution_mode = #{type}
        AND
        c.is_deleted = 0
        AND
        (p.is_deleted = 1 or
        p.status = 0 or
        p.status = 2 or
        EXISTS (select * from t_sku_stock k where k.is_deleted = 1 and c.sku_id = k.id) or
        EXISTS (select * from
        (select s.product_id,sum(s.stock) as sum from t_sku_stock s where s.is_deleted = 0 group by s.product_id) t
        where c.product_id = t.product_id and t.sum = 0
        )
        )
    </select>

</mapper>
