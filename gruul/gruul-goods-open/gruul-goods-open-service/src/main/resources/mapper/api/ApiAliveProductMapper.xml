<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.mapper.api.ApiAliveProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.goods.api.model.vo.api.ApiAliveProductVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="pic" property="pic"/>
        <result column="wide_pic" property="widePic"/>
        <result column="album_pics" property="albumPics"/>
        <result column="distribution_mode" property="distributionMode"/>
        <result column="product_sn" property="productSn"/>
        <result column="limit_type" property="limitType"/>
        <result column="min_price" property="minPrice"/>
        <result column="max_price" property="maxPrice"/>
        <result column="inventory" property="inventory"/>
        <result column="sale_describe" property="saleDescribe"/>
        <result column="shop_name" property="shopName"/>
        <result column="category_name" property="categoryName"/>
        <result column="shops_partner_id" property="shopsPartnerId"/>
        <result column="shop_id" property="shopId"/>
        <result column="product_type" property="productType"/>
    </resultMap>

    <resultMap id="BaseListResultMap" type="com.medusa.gruul.goods.api.model.vo.api.ApiAliveProductVo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="name" property="name"/>
        <result column="pic" property="pic"/>
        <result column="wide_pic" property="widePic"/>
        <result column="album_pics" property="albumPics"/>
        <result column="distribution_mode" property="distributionMode"/>
        <result column="product_sn" property="productSn"/>
        <result column="limit_type" property="limitType"/>
        <result column="min_price" property="minPrice"/>
        <result column="max_price" property="maxPrice"/>
        <result column="inventory" property="inventory"/>
        <result column="sale" property="sale"/>
        <result column="memberPriceType" property="memberPriceType"/>
        <result column="memberAgainPriceType" property="memberAgainPriceType"/>
        <result column="status" property="status"/>
        <result column="sku_id" property="skuId"/>
        <result column="per_Limit" property="perLimit"/>
        <result column="sku_count" property="skuCount"/>
        <result column="sale_describe" property="saleDescribe"/>
        <result column="shop_name" property="shopName"/>
        <result column="category_name" property="categoryName"/>
        <result column="map_x" property="mapX"/>
        <result column="map_y" property="mapY"/>
        <result column="shops_category_name" property="shopsCategoryName"/>
        <result column="shops_partner_id" property="shopsPartnerId"/>
    </resultMap>

    <resultMap id="SkuStockResultMap" type="com.medusa.gruul.goods.api.model.vo.api.ApiSkuStockVo">
        <id column="s_id" property="id"/>
        <result column="s_version" property="version"/>
        <result column="s_product_id" property="productId"/>
        <result column="s_sku_code" property="skuCode"/>
        <result column="s_specs" property="specs"/>
        <result column="s_pic" property="pic"/>
        <result column="s_price" property="price"/>
        <result column="s_original_price" property="originalPrice"/>
        <result column="s_stock" property="stock"/>
        <result column="s_low_stock" property="lowStock"/>
        <result column="s_sale" property="sale"/>
        <result column="s_per_limit" property="perLimit"/>
        <result column="member_Level_Price" property="memberLevelPrice"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Product_List">
        t.id, t.create_time, t.name, t.pic, t.wide_pic, t.album_pics, t.distribution_mode,
        t.product_sn, t.limit_type, t.sale_mode, min(u.price) as min_price, max(u.original_price) as max_price,
        sum(u.stock) as inventory, sum(u.sale) as sale,min(m.member_level_price) as member_level_price,sum(m.member_level_percentage) as member_level_percentage,
        min(u.id) as sku_id, count(u.id) as sku_count, min(per_Limit) as per_Limit,
        t.member_price_type as memberPriceType, t.status, t.sale_describe
    </sql>
    <sql id="Base_Product_Again_List">
        t.id, t.create_time, t.name, t.pic, t.wide_pic, t.album_pics, t.distribution_mode,
        t.product_sn, t.limit_type, t.sale_mode, min(u.price) as min_price, max(u.original_price) as max_price,
        sum(u.stock) as inventory, sum(u.sale) as sale,min(m.member_level_again_price) as member_level_price,sum(m.member_level_again_percentage) as member_level_percentage,
        min(u.id) as sku_id, count(u.id) as sku_count, min(per_Limit) as per_Limit,
        t.member_again_price_type as memberPriceType, t.status, t.sale_describe
    </sql>
    <sql id="Api_Product_List">
        t.id, t.create_time, t.name, t.pic, t.wide_pic, t.album_pics, t.distribution_mode,
        t.product_sn, t.limit_type, t.sale_mode, min(u.price) as min_price, max(u.original_price) as max_price,
        sum(u.stock) as inventory, sum(u.sale) as sale,min(m.member_level_price) as member_level_price,sum(m.member_level_percentage) as member_level_percentage,
        min(u.id) as sku_id, count(u.id) as sku_count, min(per_Limit) as per_Limit,
        t.member_price_type as memberPriceType, t.status, t.sale_describe
    </sql>

    <sql id="Api_Product_Again_List">
        t.id, t.create_time, t.name, t.pic, t.wide_pic, t.album_pics, t.distribution_mode,
        t.product_sn, t.limit_type, t.sale_mode, min(u.price) as min_price, max(u.original_price) as max_price,
        sum(u.stock) as inventory, sum(u.sale) as sale,min(m.member_level_again_price) as member_level_price,sum(m.member_level_again_percentage) as member_level_percentage,
        min(u.id) as sku_id, count(u.id) as sku_count, min(per_Limit) as per_Limit,
        t.member_again_price_type as memberPriceType, t.status, t.sale_describe
    </sql>

    <sql id="Base_Sku_Stock_List">
        s.id AS s_id, s.version AS s_version, s.product_id AS s_product_id, s.sku_code AS s_sku_code,
        s.specs AS s_specs, s.pic AS s_pic, s.price AS s_price, s.original_price AS s_original_price,
        s.stock AS s_stock, s.low_stock AS s_low_stock, s.sale AS s_sale, s.per_limit AS s_per_limit
    </sql>

    <select id="querySaveProductList" resultMap="BaseResultMap">
        SELECT * from
        (SELECT s.name as shop_name,sc.name as category_name,s.map_x,s.map_y,s.id as shops_partner_id,
                t.shop_id as shop_id,t.product_type as product_type,
        <include refid="Base_Product_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id and t.is_deleted = 0
        inner join t_product_show_category c ON c.product_id = t.id and c.is_deleted = 0 and c.parent_id != 0
        inner join t_show_category sc ON c.show_category_id = sc.id and sc.is_deleted = 0
        left join t_shops_partner s on s.shop_id = u.shop_id and s.is_deleted = 0
        LEFT JOIN t_member_level_goods_price m ON m.product_id = t.id and m.member_level_id = #{memberLevelId} and m.is_deleted = 0
        where t.is_deleted = 0 and t.status = 1 and u.is_deleted = 0 and s.prohibit_status = 0 group by u.product_id,s.name,sc.name,s.map_x,s.map_y,s.id) p
        <where>
            p.id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        <if test="saleMode!=null">
            AND p.sale_mode = #{saleMode}
        </if>
        ORDER BY
        p.id
        DESC
    </select>

    <select id="querySaveProductAgainList" resultMap="BaseResultMap">
        SELECT * from
        (SELECT s.name as shop_name,sc.name as category_name,s.map_x,s.map_y,s.id as shops_partner_id,
        t.shop_id as shop_id,t.product_type as product_type,
        <include refid="Base_Product_Again_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id and t.is_deleted = 0
        inner join t_product_show_category c ON c.product_id = t.id and c.is_deleted = 0 and c.parent_id != 0
        inner join t_show_category sc ON c.show_category_id = sc.id and sc.is_deleted = 0
        left join t_shops_partner s on s.shop_id = u.shop_id and s.is_deleted = 0
        LEFT JOIN t_member_level_goods_again_price m ON m.product_id = t.id and m.member_level_id = #{memberLevelId} and m.is_deleted = 0
        where t.is_deleted = 0 and t.status = 1 and u.is_deleted = 0 and s.prohibit_status = 0 group by u.product_id,s.name,sc.name,s.map_x,s.map_y,s.id) p
        <where>
            p.id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        <if test="saleMode!=null">
            AND p.sale_mode = #{saleMode}
        </if>
        ORDER BY
        p.id
        DESC
    </select>

    <select id="querySuperMarketProductList"
            parameterType="com.medusa.gruul.goods.api.model.param.api.ApiProductParam"
            resultMap="BaseListResultMap">
        SELECT p.*  from
        (SELECT s.name as shop_name,min(ma.member_level_again_price) as member_level_again_price,
                sum(ma.member_level_again_percentage) as member_level_again_percentage,t.member_again_price_type as memberAgainPriceType,
        <include refid="Api_Product_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id
        LEFT  JOIN t_member_level_goods_price m ON m.product_id = t.id and m.member_level_id = #{memberId} and m.is_deleted = 0
        LEFT  JOIN t_member_level_goods_again_price ma ON  ma.product_id = t.id and ma.member_level_id = #{memberId} and m.is_deleted = 0
        left join t_shops_partner s on s.shop_id = t.shop_id
        where t.is_deleted = 0 and t.status = 1 and u.is_deleted = 0
        <if test="apiProductParam.saleMode != null and apiProductParam.saleMode != ''">
            and t.sale_mode = #{apiProductParam.saleMode}
        </if>
        <if test="apiProductParam.name != null and apiProductParam.name != ''">
            AND (t.name LIKE CONCAT('%',#{apiProductParam.name},'%') or t.bar_code LIKE CONCAT('%',#{apiProductParam.name},'%')  or t.goods_code LIKE CONCAT('%',#{apiProductParam.name},'%'))
        </if>
        <if test="apiProductParam.barCode != null and apiProductParam.barCode != ''">
            AND t.bar_code LIKE CONCAT('%',#{apiProductParam.barCode},'%')
        </if>
        <if test="apiProductParam.showCategoryId != null">
            AND EXISTS (
            SELECT
            psc.id
            FROM
            t_product_show_category psc
            WHERE
            psc.product_id = t.id and psc.is_deleted = 0
            AND
            (psc.show_category_id = #{apiProductParam.showCategoryId})
            )
        </if>
        <if test="apiProductParam.productIds != null">
            AND u.product_id in
            <foreach collection="apiProductParam.productIds" index="index" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
        <if test="apiProductParam.showCategoryIds != null">
            AND EXISTS (
            SELECT
            psc.id
            FROM
            t_product_show_category psc
            WHERE
            psc.product_id = t.id and psc.is_deleted = 0
            AND
                (
                    psc.show_category_id in
                    <foreach collection="apiProductParam.showCategoryIds" index="index" item="showCategoryId" open="(" separator="," close=")">
                        #{showCategoryId}
                    </foreach>
                )
            )
        </if>
        group by u.product_id,s.name
        having 1=1
        <if test="apiProductParam.priceType != null and apiProductParam.priceType == 1">
            AND min(m.member_level_price)>0
        </if>
        <if test="apiProductParam.priceType != null and apiProductParam.priceType == 2">
            AND min(ma.member_level_again_price)>0
        </if>
        ) p

        ORDER BY
        <choose>
            <when test="apiProductParam.type == 1">
                p.sale desc, p.min_price desc
            </when>
            <when test="apiProductParam.type == 2">
                p.sale ${apiProductParam.sort}
            </when>
            <when test="apiProductParam.type == 3">
                p.create_time ${apiProductParam.sort}
            </when>
            <when test="apiProductParam.type == 4">
                p.min_price ${apiProductParam.sort}
            </when>
            <otherwise>
                p.create_time desc
            </otherwise>
        </choose>
    </select>


    <select id="queryShowCategoryProductList"
            parameterType="java.lang.Integer"
            resultMap="BaseListResultMap">
        SELECT p.*  from
        (SELECT
        <include refid="Api_Product_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id
        inner join t_product_show_category c on t.id = c.product_id
        LEFT  JOIN t_member_level_goods_price m ON m.product_id = t.id and m.member_level_id = 2
        where c.show_category_id = #{secondShowCategoryId}
        and t.is_deleted = 0
        and c.is_deleted = 0
        and t.status = 1
        and u.is_deleted = 0
        group by u.product_id) p
        ORDER BY
        p.create_time desc
    </select>

    <select id="queryShowCategoryProductListBySaleMode"
            resultMap="BaseListResultMap">
        SELECT p.*  from
        (SELECT s.name as shop_name,sc.name as category_name,s.map_x,s.map_y,sc2.name as shops_category_name,
        <include refid="Api_Product_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id
        inner join t_product_show_category c ON c.product_id = t.id and c.parent_id != 0 and c.is_deleted = 0
        inner join t_show_category sc ON c.show_category_id = sc.id and sc.is_deleted = 0
        inner join t_shops_partner s on s.shop_id = u.shop_id and s.is_deleted = 0
        left join t_shops_show_category ssc on ssc.shops_partner_id = s.id and ssc.parent_id != 0 and ssc.is_deleted = 0
        left join t_shops_category sc2 ON ssc.shops_category_id = sc2.id and sc2.is_deleted = 0
        LEFT JOIN t_member_level_goods_price m ON m.product_id = t.id and m.member_level_id = #{memberId} and m.is_deleted = 0
        where c.show_category_id = #{secondShowCategoryId}
        and t.sale_mode = #{saleMode}
        and t.is_deleted = 0
        and c.is_deleted = 0
        and t.status = 1
        and u.is_deleted = 0
        and s.prohibit_status = 0
        group by u.product_id,s.name,sc.name,s.map_x,s.map_y,sc2.name) p
        ORDER BY
        p.create_time desc
    </select>

    <select id="queryShowCategoryAgainProductListBySaleMode"
            resultMap="BaseListResultMap">
        SELECT p.*  from
        (SELECT s.name as shop_name,sc.name as category_name,s.map_x,s.map_y,sc2.name as shops_category_name,
        <include refid="Api_Product_Again_List"/>
        FROM
        t_sku_stock u
        inner join t_product t on u.product_id = t.id
        inner join t_product_show_category c ON c.product_id = t.id and c.parent_id != 0 and c.is_deleted = 0
        inner join t_show_category sc ON c.show_category_id = sc.id and sc.is_deleted = 0
        inner join t_shops_partner s on s.shop_id = u.shop_id and s.is_deleted = 0
        left join t_shops_show_category ssc on ssc.shops_partner_id = s.id and ssc.parent_id != 0 and ssc.is_deleted = 0
        left join t_shops_category sc2 ON ssc.shops_category_id = sc2.id and sc2.is_deleted = 0
        LEFT JOIN t_member_level_goods_again_price m ON m.product_id = t.id and m.member_level_id = #{memberId} and m.is_deleted = 0
        where c.show_category_id = #{secondShowCategoryId}
        and t.sale_mode = #{saleMode}
        and t.is_deleted = 0
        and c.is_deleted = 0
        and t.status = 1
        and u.is_deleted = 0
        and s.prohibit_status = 0
        group by u.product_id,s.name,sc.name,s.map_x,s.map_y,sc2.name) p
        ORDER BY
        p.create_time desc
    </select>
</mapper>
