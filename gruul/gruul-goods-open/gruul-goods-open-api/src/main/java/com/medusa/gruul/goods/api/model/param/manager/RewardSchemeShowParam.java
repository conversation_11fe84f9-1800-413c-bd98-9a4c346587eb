package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:20 2025/3/14
 */
@Data
@ApiModel(value = "RewardSchemeShowParam对象", description = "奖励方案明细查询参数")
public class RewardSchemeShowParam extends QueryParam {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "单据日期开始时间")
    private String billDateStartTime;

    @ApiModelProperty(value = "单据日期结束时间")
    private String billDateEndTime;

    @ApiModelProperty(value = "经手人名称")
    private String userName;

    @ApiModelProperty(value = "制单人名称")
    private String createUserName;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "有效期开始时间")
    private String startTime;

    @ApiModelProperty(value = "有效期结束时间")
    private String endTime;

    @ApiModelProperty(value = "奖励类型：1->佣金；2->提成")
    private Integer rewardType;


    @ApiModelProperty(value = "商品名称")
    private String productName;


    @ApiModelProperty(value = "会员等级名称")
    private String memberLevelName;

    @ApiModelProperty(value = "根据单据编号排序：1.正序；2.倒序")
    private Integer billNoSort;

    @ApiModelProperty(value = "根据单据日期排序：1.正序；2.倒序")
    private Integer billDateSort;
}
