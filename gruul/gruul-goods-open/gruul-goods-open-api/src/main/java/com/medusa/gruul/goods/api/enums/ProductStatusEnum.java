package com.medusa.gruul.goods.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 商品状态枚举
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Getter
public enum ProductStatusEnum {

    /**
     * 下架
     */
    SELL_OFF(0, "下架"),
    /**
     * 上架
     */
    SELL_ON(1, "上架"),
    /**
     * 已售完
     */
    SELL_OUT(2, "已售完"),
    /**
     * 不使用会员价
     */
    MEMBER_PRICE_NOT_USED(0,"不使用会员价"),
    /**
     * 会员价固定金额
     */
    MEMBER_PRICE_FIXED_AMOUNT(1,"固定金额"),
    /**
     * 会员价百分比
     */
    MEMBER_PRICE_PERCENTAGE(2,"百分比");


    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;


    ProductStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
