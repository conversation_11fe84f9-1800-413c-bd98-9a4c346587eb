package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:22 2024/11/13
 */
@Data
@ApiModel(value = "ActivityProductParam对象", description = "参加活动商品查询参数")
public class ActivityProductParam extends QueryParam {
    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "专区名称")
    private String modeName;

    @ApiModelProperty(value = "商品类型->1.普通商品，2.权益包商品")
    private Integer productType;

}
