package com.medusa.gruul.goods.api.model.vo.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:41 2024/9/24
 */
@Data
@ApiModel(value = "ApiShopCouponVo对象", description = "权益包赠送优惠券信息")
public class ApiShopCouponVo {

    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 满额（存满100减20的100值）
     */
    @ApiModelProperty(value = "满额")
    private BigDecimal fullAmount;

    /**
     * 减额或者折扣（存满100减20的20值）
     */
    @ApiModelProperty(value = "减额或者折扣")
    private BigDecimal promotion;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 显示开始时间
     */
    @ApiModelProperty(value = "显示开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayStartTime;


    /**
     * 显示结束时间
     */
    @ApiModelProperty(value = "显示结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date displayEndTime;

    /**
     * 领取次数
     */
    @ApiModelProperty(value = "领取次数")
    private Integer receiveTimes;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    private Integer useDate;


}
