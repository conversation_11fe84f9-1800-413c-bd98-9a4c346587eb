package com.medusa.gruul.goods.api.model.dto.manager;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <p>
 * 商品会员价格
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value = "新增或修改商品会员价格DTO")
public class MemberLevelGoodsPriceDto  extends QueryParam {

    private Long id;

    @ApiModelProperty(value = "商品id")
    @NotBlank(message = "商品id不能为空！")
    private Long productId;

    @ApiModelProperty(value = "商品名称")
    @NotBlank(message = "商品名称不能为空！")
    private String goodsName;

    @ApiModelProperty(value = "商品规格Id")
    @NotBlank(message = "商品规格Id不能为空！")
    private Long skuId;


    @ApiModelProperty(value = "会员等级id")
    @NotBlank(message = "会员等级id不能为空！")
    private String memberLevelId;


    @ApiModelProperty(value = "会员等级价格")
    private BigDecimal memberLevelPrice;


    @ApiModelProperty(value = "会员等级价格百分比")
    private BigDecimal memberLevelPercentage;

    @ApiModelProperty(value = "会员等级")
    private String memberLevel;



    @ApiModelProperty(value = "备注")
    private String remark;

    public MemberLevelGoodsPrice coverMemberGoodsPrice() {
        MemberLevelGoodsPrice memberLevelGoodsPrice = new MemberLevelGoodsPrice();
        BeanUtil.copyProperties(this, memberLevelGoodsPrice);
        return memberLevelGoodsPrice;
    }
}