package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 权益包列表
 */
@Data
@ApiModel(value = "ProductPackageExcelVo", description = "商品导出返回信息")
public class ProductPackageExcelVo implements Serializable {

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "商品名称")
    private String name;

    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusDict;


    @ApiModelProperty(value = "使用期限")
    private String durationTime;

    private String packageStartTime;
    private String packageEndTime;

}