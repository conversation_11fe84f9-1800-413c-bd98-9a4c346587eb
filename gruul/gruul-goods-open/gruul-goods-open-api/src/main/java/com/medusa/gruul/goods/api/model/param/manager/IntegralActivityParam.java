package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: 积分活动查询参数
 * @Date: Created in 18:01 2023/8/18
 */
@Data
public class IntegralActivityParam extends QueryParam {
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    /**
     * 方案状态
     */
    @ApiModelProperty(value = "方案状态")
    private Integer projectStatus;
    /**
     * 根据创建时间排序：1.正序；2.倒序
     */
    @ApiModelProperty(value = "根据创建时间排序：1.正序；2.倒序")
    private Integer createTimeSort;
    /**
     * 根据开始时间排序：1.正序；2.倒序
     */
    @ApiModelProperty(value = "根据开始时间排序：1.正序；2.倒序")
    private Integer startTimeSort;
    /**
     * 根据结束时间排序：1.正序；2.倒序
     */
    @ApiModelProperty(value = "根据结束时间排序：1.正序；2.倒序")
    private Integer endTimeSort;
}
