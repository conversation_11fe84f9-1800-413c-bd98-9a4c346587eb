package com.medusa.gruul.goods.api.constant;

import com.medusa.gruul.common.redis.RedisVisitorBaseFacade;

/**
 * @Author: 积分商品可兑换数redis key
 * @Description: TODO
 * @Date: Created in 14:56 2023/8/24
 */
public class IntegralGoodsExchangeNumRedisKey extends RedisVisitorBaseFacade {


    public static final String KEY_BASE = "Integral:product:ExchangeNum:";

    public IntegralGoodsExchangeNumRedisKey() {
        this(KEY_BASE);
    }


    public IntegralGoodsExchangeNumRedisKey(String baseKey) {
        super(baseKey);
    }
}
