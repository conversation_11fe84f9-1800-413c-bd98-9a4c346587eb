package com.medusa.gruul.goods.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 积分规则类型枚举
 * @Date: Created in 14:24 2023/8/22
 */
@Getter
public enum RuleTypeEnum {

    DESCRIPTION(0, "积分规则说明"),
    /**
     * 消费获积分
     */
    PAY_ORDER(1, "消费获积分"),
    /**
     * 发展下级
     */
    LOGIN_USER(2, "发展下级"),
    /**
     * 新用户注册获取积分
     */
    NEW_USER(3,"新用户注册获取积分"),
    /**
     * 每天登录获取积分
     */
    LOGIN_DAY(4,"每天登录获取积分"),
    /**
     * 购买通惠证获取积分
     */
    PAY_TICKET(5,"购买通惠证获取积分"),

    JUNIOR_PAY_ORDER(6,"下级下单获取积分");


    @EnumValue
    /**
     * 值
     */
    private final int saleMode;

    /**
     * 描述
     */
    private final String desc;

    RuleTypeEnum(int saleMode, String desc) {
        this.saleMode = saleMode;
        this.desc = desc;
    }

}
