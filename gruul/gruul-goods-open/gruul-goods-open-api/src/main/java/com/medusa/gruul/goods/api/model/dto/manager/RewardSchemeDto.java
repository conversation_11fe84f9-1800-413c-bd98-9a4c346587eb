package com.medusa.gruul.goods.api.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:13 2025/3/10
 */
@Data
@ApiModel(value = "奖励方案Dto")
public class RewardSchemeDto {

    @ApiModelProperty(value = "奖励方案id")
    private Long id;


    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    @NotNull(message = "单据编号不能为空")
    private String billNo;

    /**
     * 单据日期
     */
    @ApiModelProperty(value = "单据日期")
    @NotNull(message = "单据日期不能为空")
    private LocalDateTime billDate;

    /**
     * 有效期-开始时间
     */
    @ApiModelProperty(value = "有效期-开始时间")
    @NotNull(message = "有效期-开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 有效期-结束时间
     */
    @ApiModelProperty(value = "有效期-结束时间")
    @NotNull(message = "有效期-结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 经手人id
     */
    @ApiModelProperty(value = "经手人id")
    @NotNull(message = "经手人id不能为空")
    private String userId;

    /**
     * 经手人名称
     */
    @ApiModelProperty(value = "经手人名称")
    @NotNull(message = "经手人名称不能为空")
    private String userName;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    @NotNull(message = "方案名称不能为空")
    private String name;

    /**
     * 状态：0->草稿；1->生效中；-1->失效；-2->停止
     */
    @ApiModelProperty(value = "状态：0->草稿；1->生效中；-1->失效；-2->停止")
    private Integer status;

    /**
     * 审核状态:100->待审核;101->已审核;200->审核不通过
     */
    @ApiModelProperty(value = "审核状态:100->待审核;101->已审核;200->审核不通过")
    private Integer approvalStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 奖励方案明细
     */
    @ApiModelProperty(value = "奖励方案明细")
    @NotEmpty(message = "奖励方案明细不能为空")
    private List<RewardSchemeDetDto>list;

}
