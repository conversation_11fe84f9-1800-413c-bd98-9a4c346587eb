<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.medusa.gruul.sms.mapper.TSmsProviderEntityMapper">
    <resultMap id="BaseResultMap" type="com.medusa.gruul.sms.model.entity.TSmsProviderEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
          <result column="user_id" property="userId" jdbcType="BIGINT"/>
          <result column="sms_provider_name" property="smsProviderName" jdbcType="VARCHAR"/>
          <result column="sms_provider_appId" property="smsProviderAppId" jdbcType="VARCHAR"/>
          <result column="sms_provider_app_secret" property="smsProviderAppSecret" jdbcType="VARCHAR"/>
          <result column="sms_provider_available_count" property="smsProviderAvailableCount" jdbcType="INTEGER"/>
          <result column="sms_provider_used_count" property="smsProviderUsedCount" jdbcType="INTEGER"/>
          <result column="sms_provider_total_count" property="smsProviderTotalCount" jdbcType="INTEGER"/>
          <result column="sms_provider_status" property="smsProviderStatus" jdbcType="TINYINT"/>
          <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
          <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
          <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
         id, user_id,  sms_provider_name,  sms_provider_appId,  sms_provider_app_secret,  sms_provider_available_count,  sms_provider_used_count,  sms_provider_total_count,  sms_provider_status,  is_deleted,  create_time,  update_time, update_time 
    </sql>

    <select id="searchByEntity" resultMap="BaseResultMap" parameterType="com.medusa.gruul.sms.model.entity.TSmsProviderEntity">
        select
        <include refid="Base_Column_List"/>
        from  t_sms_provider
        <where>
            <if test="id != null">
            id = #{id,jdbcType=BIGINT}
            </if>
               <if test="userId != null">
                   AND    user_id = #{userId,jdbcType=BIGINT}
               </if>
               <if test="smsProviderName != null">
                   AND    sms_provider_name = #{smsProviderName,jdbcType=VARCHAR}
               </if>
               <if test="smsProviderAppId != null">
                   AND    sms_provider_appId = #{smsProviderAppId,jdbcType=VARCHAR}
               </if>
               <if test="smsProviderAppSecret != null">
                   AND    sms_provider_app_secret = #{smsProviderAppSecret,jdbcType=VARCHAR}
               </if>
               <if test="smsProviderAvailableCount != null">
                   AND    sms_provider_available_count = #{smsProviderAvailableCount,jdbcType=INTEGER}
               </if>
               <if test="smsProviderUsedCount != null">
                   AND    sms_provider_used_count = #{smsProviderUsedCount,jdbcType=INTEGER}
               </if>
               <if test="smsProviderTotalCount != null">
                   AND    sms_provider_total_count = #{smsProviderTotalCount,jdbcType=INTEGER}
               </if>
               <if test="smsProviderStatus != null">
                   AND    sms_provider_status = #{smsProviderStatus,jdbcType=TINYINT}
               </if>
               <if test="isDeleted != null">
                   AND    is_deleted = #{isDeleted,jdbcType=TINYINT}
               </if>
               <if test="createTime != null">
                   AND    create_time = #{createTime,jdbcType=TIMESTAMP}
               </if>
               <if test="updateTime != null">
                   AND    update_time = #{updateTime,jdbcType=TIMESTAMP}
               </if>
        </where>
    </select>


</mapper>
