package com.medusa.gruul.sms.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 短信-系统模板编码枚举类
 */
@Getter
public enum SysSmsTemplateCodeEnum {

    /**
     * 验证码短信
     */
    VERIFYCODE("verify_code", "验证码短信");


    @EnumValue
    /**
     * 值
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    SysSmsTemplateCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
