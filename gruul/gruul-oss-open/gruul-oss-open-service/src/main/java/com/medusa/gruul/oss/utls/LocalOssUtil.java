package com.medusa.gruul.oss.utls;

import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.WebUtils;
import com.medusa.gruul.oss.constant.OssConfigConstant;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.Date;

/**
 * 附件辅助类，实现附件的上传、下载的逻辑功能
 *
 * <AUTHOR>
 */
public class LocalOssUtil {


    /**
     *  上传文件
     * @param basePath 文件存储的服务器目录路径
     * @param file 上传文件
     * @return 文件存储路径
     * @throws IOException
     */
    public static void upload(String basePath, MultipartFile file) throws IOException {
        upload(basePath, file, OssConfigConstant.FILE_DEFAULT_MAX_SIZE, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
    }

    /**
     * 上传文件
     * @param file 上传文件
     * @param fileMaxSize 允许上传文件大小
     * @return 文件存储路径
     * @throws IOException
     */
    public static void upload(String basePath, MultipartFile file, Long fileMaxSize) throws IOException {
        upload(basePath, file, fileMaxSize, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
    }

    /**
     * 上传文件
     * @param file 上传文件
     * @param allowedExtArray 允许上传的文件扩展名数组
     * @return  文件存储路径
     */
    public static void upload(String basePath, MultipartFile file, String[] allowedExtArray) throws IOException {
        upload(basePath, file, OssConfigConstant.FILE_DEFAULT_MAX_SIZE, allowedExtArray);
    }

    /**
     * 最终上传文件方法
     * @param basePath 文件存储目录，包括文件名，基础目录，是绝对路径
     * @param file 上传文件
     * @param fileMaxSize 允许上传文件大小
     * @param allowedExtArray 允许上传的文件扩展名数组
     * @return 文件的存储路径
     * @throws IOException
     */
    public static void upload(String basePath, MultipartFile file, Long fileMaxSize, String[] allowedExtArray) throws IOException {
        validateAttachment(file, fileMaxSize, allowedExtArray);
        File dest = new File(basePath);
        File parentFile = dest.getParentFile();
        //检查目录不存在
        if (!parentFile.exists())
        {
            parentFile.mkdirs();
        }
        //String abpath = dest.getAbsolutePath();
        //File dest2 = new File(abpath);
        //文件写入
        file.transferTo(dest);
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 上传文件
     * @return 后缀名
     */
    public static String getExt(MultipartFile file)
    {
        String fileName = file.getOriginalFilename();
        String ext = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (StrUtil.isEmpty(ext))
        {
            ext = MimeTypeUtils.getExtension(file.getContentType());
        }
        return ext;
    }

    /**
     * 校验文件是否合法
     * @param file
     * @param fileMaxSize 文件最大的大小
     * @param allowedExtArray 允许的扩展名数组
     */
    public static void validateAttachment(MultipartFile file, Long fileMaxSize, String[] allowedExtArray)
    {
        if(file.isEmpty()){
            throw new ServiceException("文件为空，上传失败");
        }
        String fileName = file.getOriginalFilename();
        if(fileName.length() > OssConfigConstant.FILE_NAME_MAX_LENGTH){
            throw new ServiceException("文件名不能超过" + OssConfigConstant.FILE_NAME_MAX_LENGTH + "个字符");
        }
        long size = file.getSize();
        if (null != fileMaxSize && fileMaxSize != -1 && size > fileMaxSize)
        {
            throw new ServiceException("文件大小不能超过" + fileMaxSize / 1024  + "KB");
        }
        //如果不指定文件大小限制，则按照默认大小限制
        if (null == fileMaxSize && OssConfigConstant.FILE_DEFAULT_MAX_SIZE != -1 && size > OssConfigConstant.FILE_DEFAULT_MAX_SIZE)
        {
            throw new ServiceException("文件大小不能超过" + OssConfigConstant.FILE_DEFAULT_MAX_SIZE / 1024 / 1024 + "M");
        }

        String ext = getExt(file);
        if (allowedExtArray != null && !isAllowedExt(ext, allowedExtArray))
        {
            throw new ServiceException("不允许上传的文件类型");
        }

    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param ext
     * @param allowedExt
     * @return
     */
    public static final boolean isAllowedExt(String ext, String[] allowedExt)
    {
        for (String str : allowedExt)
        {
            if (str.equalsIgnoreCase(ext))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回附件的网络访问路径
     * @param url 存储在数据库的路径
     * @return 附件网络访问路径
     */
    public static final String getAttachmentUrl(HttpServletRequest request, String url){
        if(StrUtil.isEmpty(url)){
            return "";
        }
        if(url.startsWith(OssConfigConstant.HTTP_SCHEME) || url.startsWith(OssConfigConstant.HTTPS_SCHEME)){
            return url;
        }
        String basePath = WebUtils.getBasePath(request);
        if(!url.startsWith("/")){
            return basePath + "/" + url;
        }
        return basePath + url;
    }

}
