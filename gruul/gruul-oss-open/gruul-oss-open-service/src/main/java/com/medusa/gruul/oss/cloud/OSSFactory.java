package com.medusa.gruul.oss.cloud;


import com.medusa.gruul.common.core.constant.enums.CloudServiceEnum;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.oss.api.constant.OssConfigRedisKey;
import com.medusa.gruul.platform.api.model.dto.OssConfigDto;
import org.springframework.stereotype.Component;

/**
 * 文件上传Factory
 *
 * @<NAME_EMAIL>
 */
@Component
public class OSSFactory {


    public AbstractCloudStorageService build() {
        //获取云存储配置信息
        OssConfigRedisKey redisKey = new OssConfigRedisKey();
        String key = "base:" + TenantContextHolder.getTenantId();
        OssConfigDto dto = redisKey.getObject(key, new OssConfigDto());
        if (dto.getType().equals(CloudServiceEnum.QINIU.getType())) {
            return new QiniuCloudStorageService(dto);
        } else if (dto.getType().equals(CloudServiceEnum.ALIYUN.getType())) {
            return new AliyunCloudStorageService(dto);
        } else if (dto.getType().equals(CloudServiceEnum.QCLOUD.getType())) {
            return new QcloudCloudStorageService(dto);
        } else if (dto.getType().equals(CloudServiceEnum.LOCAL.getType())) {
            return new LocalStorageService(dto);
        }

        return null;
    }

}
