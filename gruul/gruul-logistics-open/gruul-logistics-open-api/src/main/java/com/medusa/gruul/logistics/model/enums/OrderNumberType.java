package com.medusa.gruul.logistics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: 微信小程序-查询订单类型枚举
 * @Date: Created in 17:52 2024/5/14
 */
@Getter
public enum OrderNumberType {

    MCHNO(1, "使用下单商户号和商户侧单号"),
    WXPAYNO(2, "使用微信支付单号");
    @EnumValue
    /**
     * 值
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    OrderNumberType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
