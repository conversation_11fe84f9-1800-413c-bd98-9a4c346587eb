package com.medusa.gruul.logistics.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 微信小程序-订单状态
 * @Date: Created in 17:40 2024/5/15
 */
@Data
@ApiModel("微信小程序-订单状态-入参DTO")
public class WxDeliverGoodsStatusDto {

    @ApiModelProperty("原支付交易对应的微信订单号。")
    private String transaction_id;

    @ApiModelProperty("支付下单商户的商户号，由微信支付生成并下发。")
    private String merchant_id;

    @ApiModelProperty("商户系统内部订单号，只能是数字、大小写字母`_-*`且在同一个商户号下唯一。")
    private String merchant_trade_no;

}
