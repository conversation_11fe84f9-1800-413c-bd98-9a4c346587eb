<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.AuthMenuButtonMapper">
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo">
        <id column="id" property="id"/>
        <result column="button_name" property="buttonName"/>
        <result column="button_code" property="buttonCode"/>
        <result column="menu_id" property="menuId"/>
        <result column="menu_name" property="menuName"/>
    </resultMap>

    <select id="getAuthMenuButtonVo" resultMap="BaseResultMap">
        select
               t1.id,
               t1.button_name,
               t1.button_code,
               t1.menu_id,
               t2.menu_name
        from
             t_auth_menu_button t1
        left join t_auth_menu_info t2
            on t1.menu_id = t2.id and t2.is_deleted = 0
        where
            t1.is_deleted = 0
          and t1.menu_id = #{menuId}
    </select>
    <select id="getAuthMenuButtonVoByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT
            t3.id,
            t3.button_name,
            t3.button_code,
            t3.menu_id,
            t4.menu_name
        FROM
            t_auth_user_role t1
                LEFT JOIN t_auth_role_menu t2 ON t1.role_id = t2.role_id
                AND t2.is_deleted = 0
                LEFT JOIN t_auth_menu_button t3 ON t2.menu_id = t3.id
                AND t3.is_deleted = 0
                left join t_auth_menu_info t4 on t3.menu_id = t4.id
                and t4.is_deleted = 0
        WHERE
            t1.is_deleted = 0
          AND t2.type = 1
          AND t1.user_id = #{userId}
    </select>
</mapper>
