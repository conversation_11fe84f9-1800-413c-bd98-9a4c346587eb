package com.medusa.gruul.platform.web.controller;

import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.model.dto.SpecialSettingDto;
import com.medusa.gruul.platform.api.model.vo.SpecialSettingVo;
import com.medusa.gruul.platform.model.dto.SavePayConfigDto;
import com.medusa.gruul.platform.model.vo.SystemConfigVo;
import com.medusa.gruul.platform.service.ISpecialSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: plh
 * @Description: 特殊配置 前端控制器
 * @Date: Created in 11:08 2024/8/15
 */
@Api(tags = "特殊配置")
@RestController
@RequestMapping("/special-setting")
public class SpecialSettingController {


    @Autowired
    private ISpecialSettingService specialSettingService;

    @GetMapping("/getSpecialSetting")
    @ApiOperation(value = "查询特殊配置")
    public Result<SpecialSettingVo> getSpecialSetting() {
        SpecialSettingVo specialSettingVo = specialSettingService.getSpecialSetting();
        return Result.ok(specialSettingVo);
    }


    @GetMapping("/getMainSpecialSetting")
    @ApiOperation(value = "查询特殊配置")
    public Result<SpecialSettingVo> getMainSpecialSetting() {
        SpecialSettingVo specialSettingVo = specialSettingService.getMainSpecialSetting();
        return Result.ok(specialSettingVo);
    }

    @PostMapping("/saveSpecialSetting")
    @ApiOperation(value = "保存或更新特殊配置")
    public Result savePayConfig(@RequestBody SpecialSettingDto specialSettingDto) {
        specialSettingService.saveSpecialSetting(specialSettingDto);
        return Result.ok();
    }

}
