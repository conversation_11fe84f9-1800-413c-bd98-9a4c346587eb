package com.medusa.gruul.platform;

import cn.dev33.satoken.spring.SaTokenSetup;
import com.medusa.gruul.account.api.conf.MiniInfoProperty;
import com.medusa.gruul.common.swagger.annotation.EnableGruulSwagger2;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;


/**
 * <AUTHOR>
 */
@EnableGruulSwagger2
@SpringCloudApplication
@EnableFeignClients(basePackages = "com.medusa.gruul.*.api.*")
@EnableDiscoveryClient
@SaTokenSetup
@EnableConfigurationProperties(MiniInfoProperty.class)
@EnableEncryptableProperties
public class PlatformApplication {
    @Bean
    public MultipartResolver multipartResolver() {
        return new CommonsMultipartResolver();
    }

    public static void main(String[] args) {
        SpringApplication.run(PlatformApplication.class, args);
    }

}
