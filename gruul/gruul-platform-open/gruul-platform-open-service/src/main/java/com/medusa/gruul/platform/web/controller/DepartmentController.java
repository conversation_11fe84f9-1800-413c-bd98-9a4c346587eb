package com.medusa.gruul.platform.web.controller;

import com.alipay.api.domain.DepartmentDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.platform.model.dto.BindingEmployeeDto;
import com.medusa.gruul.platform.model.dto.BindingWarehouseDto;
import com.medusa.gruul.platform.model.dto.PlatformAccountInfoParamDto;
import com.medusa.gruul.platform.model.param.DepartmentParam;
import com.medusa.gruul.platform.service.IPlatformDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:03 2024/9/13
 */
@RestController
@Api(tags = "平台部门接口",value = "平台部门接口")
@RequestMapping("/department-info")
public class DepartmentController {

    @Autowired
    private IPlatformDepartmentService platformDepartmentService;

    @PostMapping("/pageDepartment")
    @ApiOperation(value = "分页查询平台部门信息", notes = "分页查询平台部门信息")
    public Result<PageUtils<DepartmentVo>> pageDepartment(@RequestBody DepartmentParam departmentParam) {

        PageUtils<DepartmentVo> pageUtils = this.platformDepartmentService.searchDepartment(departmentParam);

        return Result.ok(pageUtils);
    }

    /**
     * 通过id删除
     * @param platformDepartment
     * @return
     */
    @ApiOperation(value="部门信息-通过id删除", notes="部门信息-通过id删除")
    @PostMapping(value = "/delete")
    public Result<?> delete(@RequestBody PlatformDepartment platformDepartment) {
        boolean b = this.platformDepartmentService.removeById(platformDepartment.getId());
        String str = "删除失败";
        if(b){
            str = "删除成功";
        }
        return Result.ok(str);
    }

//    /**
//     * 部门信息-设为默认仓库
//     * @param platformDepartment
//     * @return
//     */
//    @ApiOperation(value="部门信息-设为默认仓库", notes="部门信息-设为默认仓库")
//    @PostMapping(value = "/setDefault")
//    public Result<?> setDefault(@RequestBody PlatformDepartment platformDepartment) {
//        this.platformDepartmentService.setDefault(platformDepartment.getId());
//        return Result.ok("设置成功");
//    }

    /**
     * 部门信息-绑定经手人
     * @param dto
     * @return
     */
    @PostMapping("/bindingEmployee")
    @ApiOperation(value = "部门信息-绑定经手人")
    public Result bindingEmployee(@RequestBody @Validated BindingEmployeeDto dto){
        platformDepartmentService.bindingEmployee(dto);
        return Result.ok("绑定成功");
    }
}
