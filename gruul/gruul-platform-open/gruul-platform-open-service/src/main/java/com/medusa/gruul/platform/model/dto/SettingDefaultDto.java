package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:27 2024/10/29
 */
@Data
@ApiModel(value="SettingDefaultDto对象", description="设置默认值dto")
public class SettingDefaultDto {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String accountId;

}
