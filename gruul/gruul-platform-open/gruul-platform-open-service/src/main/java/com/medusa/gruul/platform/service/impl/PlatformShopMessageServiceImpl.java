package com.medusa.gruul.platform.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.MessageStatusEnum;
import com.medusa.gruul.common.core.constant.enums.MessageTypeEnum;
import com.medusa.gruul.common.core.constant.enums.UseTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.api.entity.PlatformShopInfo;
import com.medusa.gruul.platform.api.entity.PlatformShopMessage;
import com.medusa.gruul.platform.api.entity.PlatformShopTemplateDetail;
import com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto;
import com.medusa.gruul.platform.api.model.vo.ShopMessageDetailVo;
import com.medusa.gruul.platform.api.model.vo.ShopMessageVo;
import com.medusa.gruul.platform.mapper.PlatformShopMessageMapper;
import com.medusa.gruul.platform.model.dto.MotifyMsgStateDto;
import com.medusa.gruul.platform.model.dto.PlatformShopMessAgeParamDto;
import com.medusa.gruul.platform.service.*;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 店铺消息配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Service
@Log4j2
public class PlatformShopMessageServiceImpl extends ServiceImpl<PlatformShopMessageMapper, PlatformShopMessage> implements IPlatformShopMessageService {

    @Autowired
    private IPlatformShopTemplateDetailService platformShopTemplateDetailService;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;

    @Override
    public List<ShopMessageVo> msgAll() {
        PlatformShopInfo info = platformShopInfoService.getInfo();
        PlatformShopTemplateDetail templateDetail = platformShopTemplateDetailService.getById(info.getShopTemplateDetailId());
        if (BeanUtil.isEmpty(templateDetail)) {
            return new ArrayList<>();
        }
        List<PlatformShopMessage> list = this.baseMapper.selectList(new QueryWrapper<PlatformShopMessage>()
                .eq("version", templateDetail.getVersion())
                .eq("use_type", CommonConstants.NUMBER_ONE));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Integer, List<PlatformShopMessage>> listMap = list.stream().collect(Collectors.groupingBy(PlatformShopMessage::getMessageType));
        List<ShopMessageVo> vos = new LinkedList<>();
        //获取订单消息
        getVos(listMap.get(CommonConstants.NUMBER_ONE), vos, "订单消息");
        //获取用户消息
        getVos(listMap.get(CommonConstants.NUMBER_THREE), vos, "用户消息");
        return vos;
    }

    /**
     * 发送订阅消息
     *
     * @param msgSendDto com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto
     */
    @Override
    public void subscribeMsgSend(SubscribeMsgSendDto msgSendDto) {

    }

    /**
     * 封装数据
     *
     * @param platformShopMessages 指定类型数据
     * @param list                 数组
     * @param title                标题
     */
    private void getVos(List<PlatformShopMessage> platformShopMessages, List<ShopMessageVo> list, String title) {
        if (CollectionUtil.isEmpty(platformShopMessages)) {
            return;
        }
        ShopMessageVo vo = new ShopMessageVo();
        vo.setMsgTitle(title);
        List<ShopMessageDetailVo> vos = new LinkedList<>();
        for (PlatformShopMessage platformShopMessage : platformShopMessages) {
            ShopMessageDetailVo shopMessageDetailVo = BeanUtil.toBean(platformShopMessage, ShopMessageDetailVo.class);
            vos.add(shopMessageDetailVo);
        }
        vo.setShopMessageDetailVos(vos);
        list.add(vo);
    }




    @Override
    public void modifyState(MotifyMsgStateDto msgStateDto) {
        PlatformShopMessage shopMessage = this.getById(msgStateDto.getId());
        if (shopMessage == null) {
            throw new ServiceException("不存在指定消息");
        }
        if (msgStateDto.getMiniOpen() != null && msgStateDto.getMiniOpen() > 0) {
            String miniTemplateId = shopMessage.getMiniTemplateId();
            if (StrUtil.isEmpty(miniTemplateId)) {
                throw new ServiceException("请上传审核小程序之后再开启");
            }
        }
        PlatformShopMessage platformShopMessage = BeanUtil.toBean(msgStateDto, PlatformShopMessage.class);
        this.updateById(platformShopMessage);
    }

    @Override
    public void saveDeliverOrderMessage(OrderVo orderVo) {

        PlatformShopMessage platformShopMessage = new PlatformShopMessage();
        platformShopMessage.setTenantId(orderVo.getTenantId());
        platformShopMessage.setShopId(orderVo.getShopId());
        platformShopMessage.setOrderId(orderVo.getId().toString());
        platformShopMessage.setUseType(UseTypeEnum.SHOP.getStatus());
        platformShopMessage.setTitle("发货通知");
        platformShopMessage.setMessageType(MessageTypeEnum.ORDER_MESSAGE.getStatus());
        platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
        platformShopMessage.setContent("您有一条新的付款订单（"+orderVo.getId()+"），请前去发货！");

        this.save(platformShopMessage);

    }

    @Override
    public PageUtils<PlatformShopMessage> searchMessageInfo(PlatformShopMessAgeParamDto platformShopMessAgeParamDto) {
        LambdaQueryWrapper<PlatformShopMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformShopMessage::getUseType,UseTypeEnum.SHOP.getStatus());
        wrapper.isNull(PlatformShopMessage::getMpMsg);
        wrapper.isNull(PlatformShopMessage::getMiniMsg);
        if(platformShopMessAgeParamDto.getStatus()!=null){
            wrapper.eq(PlatformShopMessage::getStatus,platformShopMessAgeParamDto.getStatus());
        }
        wrapper.orderByDesc(PlatformShopMessage::getCreateTime);
        IPage<PlatformShopMessage> page = this.page(new Page<PlatformShopMessage>(platformShopMessAgeParamDto.getCurrent(), platformShopMessAgeParamDto.getSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public void deleteMessageInfo(Long id) {
        if(id==null){
            throw new ServiceException("id不能为空！");
        }
        //删除消息
        this.removeById(id);
    }

    @Override
    public void updateStatus(Long id) {
        if(id==null){
            throw new ServiceException("id不能为空！");
        }
        PlatformShopMessage platformShopMessage = this.getById(id);
        platformShopMessage.setStatus(MessageStatusEnum.YES.getStatus());
        this.updateById(platformShopMessage);
    }

    @Override
    @Transactional
    public void updateBatchStatus(String ids) {
        if(StringUtils.isNotEmpty(ids)){
            List<String> updateIds =  Arrays.asList(ids.split(","));
            for (String updateId : updateIds) {
                updateStatus(Long.valueOf(updateId));
            }
        }else{
            throw new ServiceException("id不能为空");
        }
    }

}
