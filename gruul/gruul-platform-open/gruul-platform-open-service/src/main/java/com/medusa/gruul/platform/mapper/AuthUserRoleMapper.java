package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.platform.api.entity.AuthUserRole;
import com.medusa.gruul.platform.model.vo.AuthUserMenuVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: 用户角色关系Mapper类
 * @Date: Created in 19:03 2023/8/16
 */
public interface AuthUserRoleMapper extends BaseMapper<AuthUserRole> {

    /**
     * 获取用户一级菜单
     * @param id
     * @return
     */
    List<AuthUserMenuVo> getAuthUserMenuVo(@Param("id") Long id);

    /**
     * 获取用户二级菜单
     * @param id
     * @return
     */
    List<AuthUserMenuVo> getAuthUserMenuSecondVo(@Param("id") Long id,@Param("menuPId") Long menuPId);
}
