package com.medusa.gruul.platform.web.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.platform.model.dto.*;
import com.medusa.gruul.platform.model.vo.AccountInfoVo;
import com.medusa.gruul.platform.model.vo.LoginAccountInfoDetailVo;
import com.medusa.gruul.platform.service.IAccountInfoService;
import com.medusa.gruul.platform.service.IAuthUserRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 平台用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-07
 */
@RestController
@Api(tags = "平台用户接口",value = "平台用户接口")
@RequestMapping("/account-info")
public class    AccountInfoController {


    @Autowired
    private IAccountInfoService accountInfoService;
    @Autowired
    private IAuthUserRoleService authUserRoleService;



    @GetMapping("/checkout/account")
    @EscapeLogin
    @ApiOperation(value = "账号校验是否存在", tags = "接口")
    public Result checkoutAccount(@ApiParam(value = "手机") @RequestParam String phone,
                                  @ApiParam(value = "type=1 校验账号存在  type=2校验账号不存在 默认使用1 ") @RequestParam(defaultValue = "1") Integer type) {
        accountInfoService.checkoutAccount(phone, type);
        return Result.ok();
    }


    @PostMapping("/pre/account/verify")
    @EscapeLogin
    @ApiOperation(value = "预扫码接口,返回二维码url,url中会带上state参数供回调接口入口使用", tags = "接口")
    public Result preAccountScanCode(@RequestBody @Validated PreAccountVerifyDto preAccountVerifyDto) {
        String url = accountInfoService.preAccountScanCode(preAccountVerifyDto);
        return Result.ok(url);
    }

    @GetMapping("/account/verify/notify")
    @EscapeLogin
    @ApiOperation(value = "用户扫码回调统一入口(网站应用扫码成功跳转地址),回调成功会生成code返回", tags = "接口")
    public void accountScanCodeNotify(@ApiParam(value = "微信扫码返回的codo") @RequestParam String code, @ApiParam(value = "预扫码返回的state") @RequestParam String state, HttpServletResponse response) {
        accountInfoService.accountScanCodeNotify(code, state, response);
    }

    @GetMapping("/verify/state/result")
    @EscapeLogin
    @ApiOperation(value = "扫码回调成功,根据code获取指定场景值回调结束后的结果", tags = "接口")
    public Result verifyStateResult(@ApiParam(value = "预扫码返回的code") @RequestParam String code) {
        return accountInfoService.verifyStateResult(code);
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据请求token获取当前用户最新的信息", tags = "接口")
    public Result<LoginAccountInfoDetailVo> info() {
        LoginAccountInfoDetailVo accountInfoVo = accountInfoService.info();
        return Result.ok(accountInfoVo);
    }

    @PutMapping("/verify/data")
    @ApiOperation(value = "校验当前用户相关信息是否正确，正确返回true  不正确返回false", tags = "接口")
    public Result<Boolean> verifyData(@RequestBody @Validated VerifyDataDto verifyDataDto) {
        Boolean flag = accountInfoService.verifyData(verifyDataDto);
        return Result.ok(flag);
    }

    @PutMapping("/email/change")
    @ApiOperation(value = "修改电子发票邮箱", tags = "接口")
    public Result emailChange(@RequestBody @Validated EmailChangeDto emailChangeDto) {
        accountInfoService.emailChange(emailChangeDto);
        return Result.ok();
    }


    @PutMapping("/phone/change/tie")
    @ApiOperation(value = "手机号换绑", tags = "接口")
    public Result phoneChangeTie(@RequestBody PhoneChangeTieDto phoneChangeTieDto) {
        accountInfoService.phoneChangeTie(phoneChangeTieDto);
        return Result.ok();
    }

    @PutMapping("/pass/change/tie")

    @ApiOperation(value = "修改密码", tags = "接口")
    public Result passChangeTie(@RequestBody PassChangeTieDto passChangeTieDto) {
        accountInfoService.passChangeTie(passChangeTieDto);
        return Result.ok();
    }



    @PostMapping("/login-v1")
    @EscapeLogin
    @ApiOperation(value = "登录接口", tags = {"账号相关", "商户相关接口"})
    public Result<AccountInfoVo> login1(@RequestBody @Validated LoginDto tenementLoginDto) {
        if(tenementLoginDto.getLoginType()==null){
            tenementLoginDto.setLoginType(0);
        }
        AccountInfoVo accountInfoVo = accountInfoService.login(tenementLoginDto, true);
        return Result.ok(accountInfoVo);
    }

    @PostMapping("/externaLogin-v1")
    @ApiOperation(value = "外部系统登录接口，返回token", tags = {"账号相关", "商户相关接口"})
    @EscapeLogin
    public Result externaLogin1(@RequestBody @Validated LoginDto tenementLoginDto) {
        tenementLoginDto.setLoginType(1);
        AccountInfoVo accountInfoVo = accountInfoService.login(tenementLoginDto, false);
        return Result.ok(accountInfoVo.getToken());
    }




    @PostMapping("/password-retrieve")
    @EscapeLogin
    @ApiOperation(value = "重置密码接口(目前仅限商户账号修改,商户子账号无法修改)", tags = {"账号相关", "商户相关接口"})
    public Result passwordRetrieve(@RequestBody @Validated PasswordRetrieveDto passwordRetrieveDto) {
        accountInfoService.passwordRetrieve(passwordRetrieveDto);
        return Result.ok();
    }

    @PostMapping("/getAccountInfo")
    @ApiOperation(value = "获取用户信息", tags = "接口")
    public Result getAccountInfo(@RequestBody AccountInfoDto accountInfoDto) {
        LambdaQueryWrapper<AccountInfo> lambdaQueryWrapper=new LambdaQueryWrapper<AccountInfo>();
        if(StrUtil.isNotBlank(accountInfoDto.getKeyword())){
            lambdaQueryWrapper.like(AccountInfo::getNikeName,accountInfoDto.getKeyword());
        }
        PageUtils<AccountInfo> pageAccountInfo = new PageUtils(accountInfoService.page(new Page<AccountInfo>(accountInfoDto.getCurrent(), accountInfoDto.getSize()),lambdaQueryWrapper));
        return Result.ok(pageAccountInfo);
    }




    @PostMapping("/addAccountInfo")
    @ApiOperation(value = "添加平台用户", tags = {"账号相关", "商户相关接口"})
    public Result addAccountInfo(@RequestBody @Validated PlatformAccountInfoDto accountInfoDto) {
        AccountInfo accountInfo = accountInfoService.addAccountInfo(accountInfoDto);
        return Result.ok(accountInfo);
    }

    @PostMapping("/deleteAccountInfo")
    @ApiOperation(value = "删除平台用户", tags = {"账号相关", "商户相关接口"})
    public Result deleteAccountInfo(@RequestBody PlatformAccountInfoDto accountInfoDto){
        accountInfoService.deleteAccountInfo(accountInfoDto);
        return Result.ok("删除成功");
    }

    @PostMapping("/editAccountInfo")
    @ApiOperation(value = "编辑平台用户", tags = {"账号相关", "商户相关接口"})
    public Result editAccountInfo(@RequestBody @Validated PlatformAccountInfoDto accountInfoDto) {
        AccountInfo accountInfo = accountInfoService.editAccountInfo(accountInfoDto);
        return Result.ok(accountInfo);
    }

    @PostMapping("/pageAccountInfo")
    @ApiOperation(value = "分页查询平台用户信息", tags = "接口")
    public Result pageAccountInfo(@RequestBody PlatformAccountInfoParamDto accountInfoDto) {
        PageUtils<AccountInfo> pageAccountInfo = this.accountInfoService.searchAccountInfo(accountInfoDto);
        return Result.ok(pageAccountInfo);
    }

    @PostMapping("/pageUserInfo")
    @ApiOperation(value = "分页查询平台用户信息-多店铺", notes = "分页查询平台用户信息-多店铺")
    public Result<PageUtils<UserInfoVo>> pageUserInfo(@RequestBody PlatformAccountInfoParamDto accountInfoDto) {
        PageUtils<UserInfoVo> userInfoVoPageUtils = this.accountInfoService.searchUserInfoVo(accountInfoDto);
        return Result.ok(userInfoVoPageUtils);
    }

    @PostMapping("/resetUserPwd")
    @ApiOperation(value = "商户号用户重置平台用户密码", tags = {"账号相关", "商户相关接口"})
    public Result resetUserPwd(@RequestBody @Validated PlatformAccountInfoDto accountInfoDto) {
        AccountInfo accountInfo = accountInfoService.resetUserPwd(accountInfoDto);
        return Result.ok(accountInfo);
    }



    @PostMapping("/addUserRoleInfo")
    @ApiOperation(value = "保存用户角色接口")
    public Result addUserRoleInfo(@RequestBody @Validated AuthUserRoleParamDto authUserRoleParamDto){
        authUserRoleService.saveAuthUserRole(authUserRoleParamDto);
        return Result.ok("保存成功！");
    }

    @PostMapping("/deleteUserRoleInfo")
    @ApiOperation(value = "删除用户角色接口")
    public Result deleteUserRoleInfo(@RequestBody AuthUserRoleParamDto authUserRoleParamDto){
        authUserRoleService.deleteAuthUserRole(authUserRoleParamDto);
        return Result.ok("删除成功！");
    }

    @PostMapping("/bindMiniAccount")
    @ApiOperation(value = "用户信息-绑定小程序客户")
    public Result bindMiniAccount(@RequestBody @Validated BindMiniAccountDto dto){
        accountInfoService.bindMiniAccount(dto);
        return Result.ok("绑定成功");
    }

    @PostMapping("/bindStoreFront")
    @ApiOperation(value = "用户信息-绑定门店")
    public Result bindStoreFront(@RequestBody @Validated AccountBindStoreFrontDto dto){
        accountInfoService.accountBindStoreFront(dto);
        return Result.ok("绑定成功");
    }

    @PostMapping("/bindEmployee")
    @ApiOperation(value = "用户信息-绑定职员，部门")
    public Result bindEmployee(@RequestBody @Validated AccountBindEmployeeDto dto){
        accountInfoService.accountBindEmployee(dto);
        return Result.ok("绑定成功");
    }
    @PostMapping("/bindWarehouse")
    @ApiOperation(value = "用户信息-绑定仓库")
    public Result bindWarehouse(@RequestBody @Validated AccountBindWarehouseDto dto){
        accountInfoService.accountBindWarehouse(dto);
        return Result.ok("绑定成功");
    }
    @PostMapping("/settingDefaultDepartment")
    @ApiOperation(value = "用户信息-设置默认部门")
    public Result settingDefaultDepartment(@RequestBody @Validated SettingDefaultDto dto){
        accountInfoService.settingDefaultDepartment(dto.getAccountId());
        return Result.ok("设置成功");
    }


    @PostMapping("/settingDefaultStoreFront")
    @ApiOperation(value = "用户信息-设置默认门店")
    public Result settingDefaultStoreFront(@RequestBody @Validated SettingDefaultDto dto){
        accountInfoService.settingDefaultStoreFront(dto.getAccountId());
        return Result.ok("设置成功");
    }

}
