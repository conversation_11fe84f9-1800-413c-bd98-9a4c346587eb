package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplate;
import com.medusa.gruul.platform.api.model.vo.PlatformRenovationTemplateAllVo;

/**
 * @Description: 平台装修模板表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
public interface IPlatformRenovationTemplateService extends IService<PlatformRenovationTemplate> {

    /**
     * 通过id查询装修模板相关的所有信息
     * @param id
     * @return
     */
    PlatformRenovationTemplateAllVo queryRelatedById(Long id);

}
