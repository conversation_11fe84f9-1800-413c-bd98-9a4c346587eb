package com.medusa.gruul.platform.web.controller;

import java.util.Arrays;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePage;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplatePageService;
import lombok.extern.slf4j.Slf4j;
import com.medusa.gruul.common.core.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 平台装修模板页面表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Slf4j
@Api(tags="平台装修模板页面相关接口")
@RestController
@RequestMapping("/platform-renovation-template-page")
public class PlatformRenovationTemplatePageController {
	@Autowired
	private IPlatformRenovationTemplatePageService platformRenovationTemplatePageService;
	

	/**
	 * 添加
	 *
	 * @param platformRenovationTemplatePage
	 * @return
	 */
	/**@AutoLog(value = "平台装修模板页面表-添加")*/
	@ApiOperation(value="平台装修模板页面表-添加", notes="平台装修模板页面表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody PlatformRenovationTemplatePage platformRenovationTemplatePage) {
		platformRenovationTemplatePageService.save(platformRenovationTemplatePage);
		return Result.ok("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param platformRenovationTemplatePage
	 * @return
	 */
	/**@AutoLog(value = "平台装修模板页面表-编辑")*/
	@ApiOperation(value="平台装修模板页面表-编辑", notes="平台装修模板页面表-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody PlatformRenovationTemplatePage platformRenovationTemplatePage) {
		platformRenovationTemplatePageService.updateById(platformRenovationTemplatePage);
		return Result.ok("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "平台装修模板页面表-通过id删除")*/
	@ApiOperation(value="平台装修模板页面表-通过id删除", notes="平台装修模板页面表-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		platformRenovationTemplatePageService.removeById(id);
		return Result.ok("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	/**@AutoLog(value = "平台装修模板页面表-批量删除")*/
	@ApiOperation(value="平台装修模板页面表-批量删除", notes="平台装修模板页面表-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.platformRenovationTemplatePageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	/**@AutoLog(value = "平台装修模板页面表-通过id查询")*/
	@ApiOperation(value="平台装修模板页面表-通过id查询", notes="平台装修模板页面表-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		PlatformRenovationTemplatePage platformRenovationTemplatePage = platformRenovationTemplatePageService.getById(id);
		return Result.ok(platformRenovationTemplatePage);
	}


}
