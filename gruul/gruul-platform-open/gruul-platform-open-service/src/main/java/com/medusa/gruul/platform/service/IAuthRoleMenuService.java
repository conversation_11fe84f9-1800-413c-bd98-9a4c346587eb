package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.AuthRoleMenu;
import com.medusa.gruul.platform.api.model.dto.AuthRoleMenuDto;
import com.medusa.gruul.platform.api.model.dto.AuthUserMenuDto;
import com.medusa.gruul.platform.model.dto.AuthRoleMenuParamDto;
import com.medusa.gruul.platform.model.dto.AuthUserRoleParamDto;

import java.util.List;

/**
 * @Author: plh
 * @Description: 角色菜单关系服务类
 * @Date: Created in 21:02 2023/8/16
 */
public interface IAuthRoleMenuService extends IService<AuthRoleMenu> {

    /**
     * 添加角色菜单关系
     * @param authRoleMenuDto
     */
    void addAuthRoleMenu(AuthRoleMenuDto authRoleMenuDto);

    /**
     * 删除角色菜单关系
     * @param roleId
     */
    void deleteAuthRoleMenu(Long roleId);

    /**
     * 根据角色id获取到关联的菜单id
     * @param roleId
     * @return
     */
    List<Long>getMenuIds(Long roleId);

    /**
     * 添加租户菜单
     * @param authUserMenuDto
     */
    void addAuthUserMenu(AuthUserMenuDto authUserMenuDto);

    /**
     * 删除租户菜单
     */
    void deleteUserMenu();

    /**
     * 获取租户菜单
     * @return
     */
    List<Long>getUserMenuIds();
}
