package com.medusa.gruul.platform.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 部门信息查询参数
 * @Date: Created in 9:50 2024/9/13
 */
@Data
public class DepartmentParam extends QueryParam {

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;

    /**
     * 部门全称
     */
    @ApiModelProperty(value = "部门全称")
    private String deptFullName;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String departmentName;
    /**
     * 停用启用状态
     */
    @ApiModelProperty(value = "停用启用状态")
    private String 	stopOpenState;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private String employeeId;

    /**
     * 职员名称
     */
    @ApiModelProperty(value = "职员名称")
    private String employeeFullName;
}
