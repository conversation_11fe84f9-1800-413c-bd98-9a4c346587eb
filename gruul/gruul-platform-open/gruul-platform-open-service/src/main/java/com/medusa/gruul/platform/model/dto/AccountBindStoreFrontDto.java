package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:52 2024/10/28
 */
@Data
@ApiModel(value="AccountBindStoreFrontDto对象", description="用户绑定小程序客户dto")
public class AccountBindStoreFrontDto {
    /**用户id*/
    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String accountId;
    /**
     * 门店标识
     */
    @ApiModelProperty(value = "门店标识")
    @NotBlank(message = "门店标识能为空")
    private String storeFrontCode;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    private String storeFrontName;

}
