package com.medusa.gruul.platform.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.MessageStatusEnum;
import com.medusa.gruul.common.core.constant.enums.WxMessageTemplateCodeEnum;
import com.medusa.gruul.common.core.constant.enums.WxMessageTemplateStatusEnum;
import com.medusa.gruul.common.core.constant.enums.WxMessageTemplateTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.PlatformShopMessage;
import com.medusa.gruul.platform.api.entity.WxMessageTemplate;
import com.medusa.gruul.platform.api.entity.WxMessageTemplateDetail;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.platform.mapper.PlatformShopMessageMapper;
import com.medusa.gruul.platform.mapper.WxMessageTemplateDetailMapper;
import com.medusa.gruul.platform.mapper.WxMessageTemplateMapper;
import com.medusa.gruul.platform.model.dto.ApiWxMiniTemplateDto;
import com.medusa.gruul.platform.model.dto.UpdateWxMessageTemplateStatusDto;
import com.medusa.gruul.platform.model.dto.WxMessageTemplateDetailDto;
import com.medusa.gruul.platform.model.dto.WxMessageTemplateDto;
import com.medusa.gruul.platform.model.param.WxMessageTemplateParam;
import com.medusa.gruul.platform.api.model.vo.WxMessageTemplateDetailVo;
import com.medusa.gruul.platform.api.model.vo.WxMessageTemplateVo;
import com.medusa.gruul.platform.service.IWxMessageTemplateService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:00 2024/11/6
 */
@Service
@Slf4j
public class WxMessageTemplateServiceImpl extends ServiceImpl<WxMessageTemplateMapper, WxMessageTemplate>implements IWxMessageTemplateService {

    @Autowired
    private WxMessageTemplateDetailMapper wxMessageTemplateDetailMapper;
    @Autowired
    private PlatformShopMessageMapper platformShopMessageMapper;

    @Override
    public PageUtils<WxMessageTemplateVo> getWxMessageTemplate(WxMessageTemplateParam param) {
        IPage<WxMessageTemplateVo>page = this.baseMapper.getWxMessageTemplate(new Page<>(param.getCurrent(),param.getSize()),param);
        return new PageUtils<WxMessageTemplateVo>(page);
    }

    @Override
    public WxMessageTemplateVo getWxMessageTemplateDetail(String id) {
        WxMessageTemplateVo wxMessageTemplateVo = new WxMessageTemplateVo();
        WxMessageTemplate wxMessageTemplate = this.baseMapper.selectById(id);
        BeanUtils.copyProperties(wxMessageTemplate,wxMessageTemplateVo);
        LambdaQueryWrapper<WxMessageTemplateDetail>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMessageTemplateDetail::getMainId,id);
        List<WxMessageTemplateDetail> wxMessageTemplateDetailList = wxMessageTemplateDetailMapper.selectList(wrapper);
        List<WxMessageTemplateDetailVo>list = new ArrayList<>();
        if(wxMessageTemplateDetailList!=null&&wxMessageTemplateDetailList.size()>0){
            for (WxMessageTemplateDetail wxMessageTemplateDetail : wxMessageTemplateDetailList) {
                WxMessageTemplateDetailVo wxMessageTemplateDetailVo = new WxMessageTemplateDetailVo();
                BeanUtils.copyProperties(wxMessageTemplateDetail,wxMessageTemplateDetailVo);
                list.add(wxMessageTemplateDetailVo);
            }
        }
        wxMessageTemplateVo.setList(list);
        return wxMessageTemplateVo;
    }

    @Override
    @Transactional
    public void addWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto) {
        //1.添加微信消息模板
        WxMessageTemplate wxMessageTemplate = new WxMessageTemplate();
        BeanUtils.copyProperties(wxMessageTemplateDto,wxMessageTemplate);
        wxMessageTemplate.setStatus(CommonConstants.NUMBER_ONE);
        this.baseMapper.insert(wxMessageTemplate);
        //2.添加微信消息模板内容
        List<WxMessageTemplateDetailDto> list = wxMessageTemplateDto.getList();
        if(list!=null&&list.size()>0){
            for (WxMessageTemplateDetailDto wxMessageTemplateDetailDto : list) {
                WxMessageTemplateDetail wxMessageTemplateDetail = new WxMessageTemplateDetail();
                BeanUtils.copyProperties(wxMessageTemplateDetailDto,wxMessageTemplateDetail);
                wxMessageTemplateDetail.setMainId(wxMessageTemplate.getId()+"");
                wxMessageTemplateDetailMapper.insert(wxMessageTemplateDetail);
            }
        }
    }

    @Override
    @Transactional
    public void deleteWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto) {
        Long id = wxMessageTemplateDto.getId();
        if(id==null){
            throw new ServiceException("消息模板id不能为空");
        }
        //1.先删除子表信息
        LambdaQueryWrapper<WxMessageTemplateDetail>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMessageTemplateDetail::getMainId,id);
        List<WxMessageTemplateDetail> list = wxMessageTemplateDetailMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (WxMessageTemplateDetail wxMessageTemplateDetail : list) {
                wxMessageTemplateDetailMapper.deleteById(wxMessageTemplateDetail.getId());
            }
        }
        //2.再删除主表信息
        this.baseMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void updateWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto) {
        Long id = wxMessageTemplateDto.getId();
        if(id==null){
            throw new ServiceException("消息模板id不能为空");
        }
        //1.首先更新主表信息
        WxMessageTemplate wxMessageTemplate = this.baseMapper.selectById(id);
        BeanUtils.copyProperties(wxMessageTemplateDto,wxMessageTemplate);
        this.baseMapper.updateById(wxMessageTemplate);
        //2.删除子表信息
        LambdaQueryWrapper<WxMessageTemplateDetail>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMessageTemplateDetail::getMainId,id);
        List<WxMessageTemplateDetail> list = wxMessageTemplateDetailMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (WxMessageTemplateDetail wxMessageTemplateDetail : list) {
                wxMessageTemplateDetailMapper.deleteById(wxMessageTemplateDetail.getId());
            }
        }
        //3.添加子表信息
        List<WxMessageTemplateDetailDto> dtoList = wxMessageTemplateDto.getList();
        if(dtoList!=null&&dtoList.size()>0){
            for (WxMessageTemplateDetailDto wxMessageTemplateDetailDto : dtoList) {
                WxMessageTemplateDetail wxMessageTemplateDetail = new WxMessageTemplateDetail();
                BeanUtils.copyProperties(wxMessageTemplateDetailDto,wxMessageTemplateDetail);
                wxMessageTemplateDetail.setMainId(wxMessageTemplate.getId()+"");
                wxMessageTemplateDetailMapper.insert(wxMessageTemplateDetail);
            }
        }
    }

    @Override
    public void updateWxMessageTemplateStatus(UpdateWxMessageTemplateStatusDto updateWxMessageTemplateStatusDto) {
        Long id = updateWxMessageTemplateStatusDto.getId();
        if(id==null){
            throw new ServiceException("消息模板id不能为空");
        }
        WxMessageTemplate wxMessageTemplate = this.baseMapper.selectById(id);
        wxMessageTemplate.setStatus(updateWxMessageTemplateStatusDto.getStatus());
        this.baseMapper.updateById(wxMessageTemplate);
    }

    @Override
    public void wxSendMessage(WxSendMessageDto wxSendMessageDto) {

        WxMaDefaultConfigImpl wxMaDefaultConfig = new WxMaDefaultConfigImpl();
        wxMaDefaultConfig.setAppid(wxSendMessageDto.getAppId());
        wxMaDefaultConfig.setSecret(wxSendMessageDto.getAppSecret());
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(wxMaDefaultConfig);

        //消息主体
        List<WxMaSubscribeMessage.Data>dataList = new ArrayList<>();
        List<Map<String,String>> mapList = wxSendMessageDto.getMapList();
        if(mapList!=null&&mapList.size()>0){
            for (Map<String,String> map : mapList) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    WxMaSubscribeMessage.Data data = new WxMaSubscribeMessage.Data();
                    data.setName(key);
                    data.setValue(value);
                    dataList.add(data);
                }
            }
        }


        List<String> openIds = wxSendMessageDto.getOpenIds();
        if(openIds!=null&&openIds.size()>0){
            for (String openId : openIds) {

                WxMaSubscribeMessage.WxMaSubscribeMessageBuilder builder = WxMaSubscribeMessage.builder();
                //发送人openId
                builder.toUser(openId);
                //发送的模板id
                builder.templateId(wxSendMessageDto.getTemplateId());
                //消息主体
                builder.data(dataList);
                //点击订阅消息的跳转链接
                builder.page(wxSendMessageDto.getPage());
                WxMaSubscribeMessage msg = builder.build();

                PlatformShopMessage platformShopMessage = new PlatformShopMessage();
                platformShopMessage.setTenantId(wxSendMessageDto.getTenantId());
                platformShopMessage.setShopId(wxSendMessageDto.getShopId());
                platformShopMessage.setUseType(wxSendMessageDto.getUseType());
                platformShopMessage.setTitle(wxSendMessageDto.getTitle());
                platformShopMessage.setMiniTemplateId(wxSendMessageDto.getTemplateId());
                platformShopMessage.setMiniMsg(wxSendMessageDto.getMessage());
                platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
                platformShopMessageMapper.insert(platformShopMessage);
                try {
                    wxMaService.getMsgService().sendSubscribeMsg(msg);
                    platformShopMessage.setMark("成功");
                    platformShopMessageMapper.updateById(platformShopMessage);
                } catch (WxErrorException e) {
                    platformShopMessage.setMark("失败");
                    platformShopMessageMapper.updateById(platformShopMessage);
                    log.error("订阅消息发送失败，消息模板Id:{}，用户openId:{}, 错误码：{},错误信息：{}",
                            wxSendMessageDto.getTemplateId(),openId, e.getError().getErrorCode(), e.getError().getErrorMsg());
                }

            }
        }

    }

    @Override
    public void wxMpSendMessage(WxSendMessageDto wxSendMessageDto) {
        WxMpDefaultConfigImpl wxMpDefaultConfig = new WxMpDefaultConfigImpl();
        wxMpDefaultConfig.setAppId(wxSendMessageDto.getAppMpId());
        wxMpDefaultConfig.setSecret(wxSendMessageDto.getAppMpSecret());
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(wxMpDefaultConfig);

        //消息主体
        List<WxMpTemplateData>dataList = new ArrayList<>();
        List<Map<String,String>> mapList = wxSendMessageDto.getMapList();
        if(mapList!=null&&mapList.size()>0){
            for (Map<String,String> map : mapList) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    WxMpTemplateData data= new WxMpTemplateData();
                    data.setName(key);
                    data.setValue(value);
                    dataList.add(data);
                }
            }
        }

        List<String> openIds = wxSendMessageDto.getOpenIds();
        if(openIds!=null&&openIds.size()>0){
            for (String openId : openIds) {

                WxMaSubscribeMessage.WxMaSubscribeMessageBuilder builder = WxMaSubscribeMessage.builder();
                //发送人openId
                builder.toUser(openId);
                //发送的模板id
                builder.templateId(wxSendMessageDto.getTemplateId());
                WxMpTemplateMessage.MiniProgram miniProgram = new WxMpTemplateMessage.MiniProgram();
                miniProgram.setAppid(wxSendMessageDto.getAppId());
                miniProgram.setPagePath(wxSendMessageDto.getPage());
                miniProgram.setUsePath(true);
                //2,推送消息
                WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                        .toUser(openId)//要推送的用户openid
                        .templateId(wxSendMessageDto.getTemplateId())//模版id
                        .miniProgram(miniProgram)
                        .data(dataList)
                        .build();

                PlatformShopMessage platformShopMessage = new PlatformShopMessage();
                platformShopMessage.setTenantId(wxSendMessageDto.getTenantId());
                platformShopMessage.setShopId(wxSendMessageDto.getShopId());
                platformShopMessage.setUseType(wxSendMessageDto.getUseType());
                platformShopMessage.setTitle(wxSendMessageDto.getTitle());
                platformShopMessage.setMpTemplateId(wxSendMessageDto.getTemplateId());
                platformShopMessage.setMpMsg(wxSendMessageDto.getMessage());
                platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
                platformShopMessageMapper.insert(platformShopMessage);
                try {
                    wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                    platformShopMessage.setMark("成功");
                    platformShopMessageMapper.updateById(platformShopMessage);
                } catch (WxErrorException e) {
                    platformShopMessage.setMark("失败");
                    platformShopMessageMapper.updateById(platformShopMessage);
                    log.error("模板消息发送失败，消息模板Id:{}，用户openId:{}, 错误码：{},错误信息：{}",
                            wxSendMessageDto.getTemplateId(),openId, e.getError().getErrorCode(), e.getError().getErrorMsg());
                }

            }
        }
    }

    @Override
    public WxMessageTemplateVo getWxMessageTemplateByCode(String code) {
        WxMessageTemplateVo wxMessageTemplateVo = new WxMessageTemplateVo();
        LambdaQueryWrapper<WxMessageTemplate>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMessageTemplate::getCode,code);
        wrapper.eq(WxMessageTemplate::getStatus,WxMessageTemplateStatusEnum.YES.getStatus());
        List<WxMessageTemplate> wxMessageTemplates = this.baseMapper.selectList(wrapper);
        if(wxMessageTemplates!=null&&wxMessageTemplates.size()>0){
            WxMessageTemplate wxMessageTemplate = wxMessageTemplates.get(0);
            wxMessageTemplateVo = this.getWxMessageTemplateDetail(wxMessageTemplate.getId() + "");
        }
        return wxMessageTemplateVo;
    }

    @Override
    public ApiWxMiniTemplateDto getApiWxMiniTemplate(Integer type) {
        ApiWxMiniTemplateDto apiWxMiniTemplateDto = new ApiWxMiniTemplateDto();
        List<String>templateIds = new ArrayList<>();
        if(type!=null&&(type == CommonConstants.NUMBER_ONE||type == CommonConstants.NUMBER_TWO)){
            LambdaQueryWrapper<WxMessageTemplate>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WxMessageTemplate::getType, WxMessageTemplateTypeEnum.MINI.getStatus());
            wrapper.eq(WxMessageTemplate::getStatus, WxMessageTemplateStatusEnum.YES.getStatus());
            wrapper.eq(WxMessageTemplate::getCode, WxMessageTemplateCodeEnum.ACTIVITY.getStatus());
            List<WxMessageTemplate> activityList = this.baseMapper.selectList(wrapper);//活动通知
            if(activityList!=null&&activityList.size()>0){
                for (WxMessageTemplate wxMessageTemplate : activityList) {
                    templateIds.add(wxMessageTemplate.getTemplateId());
                }
            }
            LambdaQueryWrapper<WxMessageTemplate>wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(WxMessageTemplate::getType, WxMessageTemplateTypeEnum.MINI.getStatus());
            wrapper1.eq(WxMessageTemplate::getStatus, WxMessageTemplateStatusEnum.YES.getStatus());
            wrapper1.eq(WxMessageTemplate::getCode, WxMessageTemplateCodeEnum.BIRTHDAY.getStatus());
            List<WxMessageTemplate> birthdayList = this.baseMapper.selectList(wrapper1);//生日提醒
            if(birthdayList!=null&&birthdayList.size()>0){
                for (WxMessageTemplate wxMessageTemplate : birthdayList) {
                    templateIds.add(wxMessageTemplate.getTemplateId());
                }
            }
        }
        apiWxMiniTemplateDto.setTemplateIds(templateIds);
        if(templateIds.size()>0){
            apiWxMiniTemplateDto.setShowFlag(true);
        }else{
            apiWxMiniTemplateDto.setShowFlag(false);
        }
        return apiWxMiniTemplateDto;
    }
}
