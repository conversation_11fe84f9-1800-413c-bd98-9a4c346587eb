package com.medusa.gruul.platform.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleMenu;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoDto;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoSecondDto;
import com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo;
import com.medusa.gruul.platform.api.model.vo.AuthMenuInfoSecondVo;
import com.medusa.gruul.platform.api.model.vo.AuthMenuInfoVo;
import com.medusa.gruul.platform.mapper.AuthMenuInfoMapper;
import com.medusa.gruul.platform.mapper.AuthRoleMenuMapper;
import com.medusa.gruul.platform.service.IAccountInfoService;
import com.medusa.gruul.platform.service.IAuthMenuInfoService;
import com.medusa.gruul.platform.service.IAuthRoleMenuService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: 平台菜单 服务实现类
 * @Date: Created in 14:13 2023/8/16
 */
@Service
@Log4j2
public class AuthMenuInfoServiceImpl extends ServiceImpl<AuthMenuInfoMapper, AuthMenuInfo> implements IAuthMenuInfoService {

    @Autowired
    private AuthRoleMenuMapper authRoleMenuMapper;

    @Autowired
    private IAccountInfoService accountInfoService;

    @Autowired
    private IAuthRoleMenuService authRoleMenuService;

    @Override
    public List<AuthMenuInfoVo> getAllAuthMenuInfoList() {
        AccountInfo accountInfo = accountInfoService.getById(CurUserUtil.getPcRqeustAccountInfo().getUserId());
        List<AuthMenuInfoVo> authMenuInfoVos = this.baseMapper.queryAllAuthMenuInfoList();
        List<AuthMenuInfoVo>list = new ArrayList<>();
        if(accountInfo.getAccountType()!=null&&accountInfo.getAccountType()==1){
            List<Long> userMenuIds = authRoleMenuService.getUserMenuIds();
            if(userMenuIds!=null&&userMenuIds.size()>0){
                for (Long userMenuId : userMenuIds) {
                    List<AuthMenuInfoVo> authMenuInfoVoList = authMenuInfoVos.stream().filter(e -> e.getAuthMenuInfoId().equals(userMenuId)).collect(Collectors.toList());
                    if(authMenuInfoVoList!=null&&authMenuInfoVoList.size()>0){
                        for (AuthMenuInfoVo authMenuInfoVo : authMenuInfoVoList) {
                            List<AuthMenuInfoSecondVo> authMenuInfoSecondVos = authMenuInfoVo.getAuthMenuInfoSecondVos();
                            List<AuthMenuInfoSecondVo>list2 = new ArrayList<>();
                            for (Long userMenuId2 : userMenuIds) {
                                List<AuthMenuInfoSecondVo> menuInfoSecondVoList = authMenuInfoSecondVos.stream().filter(e2 -> e2.getAuthMenuInfoId().equals(userMenuId2)).collect(Collectors.toList());
                                if(menuInfoSecondVoList!=null&&menuInfoSecondVoList.size()>0){
                                    for (AuthMenuInfoSecondVo authMenuInfoSecondVo : menuInfoSecondVoList) {
                                        List<AuthMenuButtonVo> authMenuButtonVos = authMenuInfoSecondVo.getAuthMenuButtonVos();
                                        List<AuthMenuButtonVo>list3 = new ArrayList<>();
                                        for (Long userMenuId3 : userMenuIds) {
                                            List<AuthMenuButtonVo> menuButtonVoList = authMenuButtonVos.stream().filter(e3 -> e3.getId().equals(userMenuId3)).collect(Collectors.toList());
                                            if(menuButtonVoList!=null&&menuButtonVoList.size()>0){
                                                for (AuthMenuButtonVo authMenuButtonVo : menuButtonVoList) {
                                                    list3.add(authMenuButtonVo);
                                                }
                                            }
                                        }
                                        authMenuInfoSecondVo.setAuthMenuButtonVos(list3);
                                        list2.add(authMenuInfoSecondVo);
                                    }
                                }
                            }
                            authMenuInfoVo.setAuthMenuInfoSecondVos(list2);
                            list.add(authMenuInfoVo);
                        }
                    }
                }
            }
        }
        if(list.size()==0){
            list = authMenuInfoVos;
        }
        return list;
    }

    @Override
    public void saveAuthMenuInfo(AuthMenuInfoDto authMenuInfoDto) {
        //保存一级菜单
        AuthMenuInfo authMenuInfo = ifAuthMenuInfoNameAndCode(authMenuInfoDto);
        int insert = this.baseMapper.insert(authMenuInfo);
        if (insert == 0) {
            throw new ServiceException("新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
        }
    }

    @Override
    public void addSecondList(List<AuthMenuInfoSecondDto> authMenuInfoSecondDtos) {
        authMenuInfoSecondDtos.stream().forEach(bean -> {
            LambdaQueryWrapper<AuthMenuInfo>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AuthMenuInfo::getMenuName,bean.getMenuName());
            wrapper.eq(AuthMenuInfo::getMenuPid,bean.getMenuPid());
            AuthMenuInfo authMenuInfo = this.baseMapper.selectOne(wrapper);
            if (!BeanUtil.isEmpty(authMenuInfo)) {
                throw new ServiceException(authMenuInfo.getMenuName() + "菜单名称已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            LambdaQueryWrapper<AuthMenuInfo>wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(AuthMenuInfo::getMenuCode,bean.getMenuCode());
            wrapper1.eq(AuthMenuInfo::getMenuPid,bean.getMenuPid());
            AuthMenuInfo authMenuInfo1 = this.baseMapper.selectOne(wrapper1);
            if (!BeanUtil.isEmpty(authMenuInfo1)) {
                throw new ServiceException(authMenuInfo1.getMenuCode() + "分类标识已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            AuthMenuInfo authMenuInfoSecond = bean.coverAuthMenuInfo();
            int secondInsert = this.baseMapper.insert(authMenuInfoSecond);
            if (secondInsert == 0) {
                throw new ServiceException("新增失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        });
    }

    @Override
    public void deleteAuthMenuInfo(Long id) {
        AuthMenuInfo authMenuInfo = this.baseMapper.selectById(id);
        if (BeanUtil.isEmpty(authMenuInfo)) {
            throw new ServiceException("该菜单不存在！", SystemCode.DATA_EXISTED.getCode());
        }
        List<Long> ids = new ArrayList<>();

        LambdaQueryWrapper<AuthRoleMenu>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleMenu::getParentId,id).or().eq(AuthRoleMenu::getMenuId,id);
        Integer num = authRoleMenuMapper.selectCount(wrapper);
        if (num > 0){
            throw new ServiceException("该菜单下关联有角色，暂时无法删除");
        }
        if (authMenuInfo.getMenuPid() == 0) {
            LambdaQueryWrapper<AuthMenuInfo>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AuthMenuInfo::getMenuPid,id);
            List<AuthMenuInfo> authMenuInfos = this.baseMapper.selectList(queryWrapper);
            ids = authMenuInfos.stream().map(AuthMenuInfo::getId).collect(Collectors.toList());
        }
        //有子菜单删除子菜单
        if (!CollectionUtil.isEmpty(ids)) {
            LambdaQueryWrapper<AuthMenuInfo>deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(AuthMenuInfo::getMenuPid,id);
            this.baseMapper.delete(deleteWrapper);
        }
        //删除自身菜单
        this.baseMapper.deleteById(id);
        //清空角色关联菜单的信息
        ids.add(id);
        authRoleMenuMapper.delete(new LambdaQueryWrapper<AuthRoleMenu>().in(AuthRoleMenu::getMenuId, ids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAuthMenuInfo(AuthMenuInfoDto authMenuInfoDto) {
        //判断原分类是否已被删除
        AuthMenuInfo oldAuthMenuInfo = this.baseMapper.selectById(authMenuInfoDto.getId());
        if (BeanUtil.isEmpty(oldAuthMenuInfo)) {
            throw new ServiceException("该菜单不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        LambdaQueryWrapper<AuthMenuInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthMenuInfo::getMenuName,authMenuInfoDto.getMenuName());
        wrapper.eq(AuthMenuInfo::getMenuPid,authMenuInfoDto.getMenuPid());
        wrapper.ne(AuthMenuInfo::getId,authMenuInfoDto.getId());
        AuthMenuInfo searchAuthMenuInfo = this.baseMapper.selectOne(wrapper);
        if (!BeanUtil.isEmpty(searchAuthMenuInfo)) {
            throw new ServiceException("菜单名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        LambdaQueryWrapper<AuthMenuInfo> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AuthMenuInfo::getMenuName,authMenuInfoDto.getMenuCode());
        wrapper1.eq(AuthMenuInfo::getMenuPid,authMenuInfoDto.getMenuPid());
        wrapper1.ne(AuthMenuInfo::getId,authMenuInfoDto.getId());
        AuthMenuInfo searchAuthMenuInfo1 = this.baseMapper.selectOne(wrapper1);
        if (!BeanUtil.isEmpty(searchAuthMenuInfo1)) {
            throw new ServiceException("菜单编号已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        //大类保存
        AuthMenuInfo authMenuInfo = authMenuInfoDto.coverAuthMenuInfo();
        int update = this.baseMapper.updateById(authMenuInfo);
        if (update == 0) {
            throw new ServiceException("修改失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
        }
    }

    /**
     * 判断查询分类名称是否已存在
     *
     * @param authMenuInfoDto
     * @return sortingCategory
     */
    private AuthMenuInfo ifAuthMenuInfoNameAndCode(AuthMenuInfoDto authMenuInfoDto) {

        LambdaQueryWrapper<AuthMenuInfo>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthMenuInfo::getMenuName,authMenuInfoDto.getMenuName());
        AuthMenuInfo authMenuInfo = this.baseMapper.selectOne(queryWrapper);
        if(!BeanUtil.isEmpty(authMenuInfo)){
            throw new ServiceException("菜单名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        LambdaQueryWrapper<AuthMenuInfo>queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(AuthMenuInfo::getMenuCode,authMenuInfoDto.getMenuCode());
        AuthMenuInfo authMenuInfo1 = this.baseMapper.selectOne(queryWrapper1);
        if(!BeanUtil.isEmpty(authMenuInfo1)){
            throw new ServiceException("菜单标识已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        authMenuInfo = authMenuInfoDto.coverAuthMenuInfo();
        return authMenuInfo;
    }

}
