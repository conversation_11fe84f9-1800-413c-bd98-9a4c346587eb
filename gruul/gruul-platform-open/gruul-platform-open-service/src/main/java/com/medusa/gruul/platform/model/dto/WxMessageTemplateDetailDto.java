package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:51 2024/11/6
 */
@Data
@ApiModel(value="WxMessageTemplateDetailDto对象", description="微信消息模板内容dto")
public class WxMessageTemplateDetailDto {


    @ApiModelProperty(value = "字段名称")
    private String name;

    @ApiModelProperty(value = "字段标识")
    private String keyData;

    @ApiModelProperty(value = "字段值")
    private String valueData;

}
