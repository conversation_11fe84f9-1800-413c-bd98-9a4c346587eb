package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleMenu;
import com.medusa.gruul.platform.api.entity.AuthUserRole;
import com.medusa.gruul.platform.mapper.AuthRoleInfoMapper;
import com.medusa.gruul.platform.model.dto.PlatformAuthRoleInfoDto;
import com.medusa.gruul.platform.model.dto.PlatformAuthRoleInfoParamDto;
import com.medusa.gruul.platform.model.vo.AuthRoleInfoVo;
import com.medusa.gruul.platform.service.IAccountInfoService;
import com.medusa.gruul.platform.service.IAuthRoleInfoService;
import com.medusa.gruul.platform.service.IAuthRoleMenuService;
import com.medusa.gruul.platform.service.IAuthUserRoleService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Service
@Log4j2
public class AuthRoleServiceImpl extends ServiceImpl<AuthRoleInfoMapper, AuthRoleInfo> implements IAuthRoleInfoService {

    @Autowired
    private IAuthUserRoleService authUserRoleService;

    @Autowired
    private IAuthRoleMenuService authRoleMenuService;

    @Override
    public PageUtils searchAuthRoleInfo(PlatformAuthRoleInfoParamDto platformAuthRoleInfoParamDto) {
        LambdaQueryWrapper<AuthRoleInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(platformAuthRoleInfoParamDto.getRoleCode()), AuthRoleInfo::getRoleCode, platformAuthRoleInfoParamDto.getRoleCode()).
                like(StrUtil.isNotBlank(platformAuthRoleInfoParamDto.getRoleName()), AuthRoleInfo::getRoleName, platformAuthRoleInfoParamDto.getRoleName()).
                orderByDesc(AuthRoleInfo::getId);
        IPage<AuthRoleInfo> page = this.page(new Page<AuthRoleInfo>(platformAuthRoleInfoParamDto.getCurrent(), platformAuthRoleInfoParamDto.getSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<AuthRoleInfoVo> getList() {
        List<AuthRoleInfoVo>authRoleInfoVoList = new ArrayList<>();
        List<AuthRoleInfo> authRoleInfoList = this.list();
        for (AuthRoleInfo authRoleInfo : authRoleInfoList) {
            AuthRoleInfoVo authRoleInfoVo = new AuthRoleInfoVo();
            BeanUtils.copyProperties(authRoleInfo,authRoleInfoVo);
            authRoleInfoVoList.add(authRoleInfoVo);
        }
        return authRoleInfoVoList;
    }

    @Override
    public AuthRoleInfo addAuthRoleInfo(PlatformAuthRoleInfoDto platformAuthRoleInfoDto) {

        LambdaQueryWrapper<AuthRoleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleInfo::getRoleName,platformAuthRoleInfoDto.getRoleName());
        AuthRoleInfo searchAuthRoleInfo = this.baseMapper.selectOne(wrapper);
        if(!BeanUtil.isEmpty(searchAuthRoleInfo)){
            throw new ServiceException("角色名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        LambdaQueryWrapper<AuthRoleInfo> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AuthRoleInfo::getRoleCode,platformAuthRoleInfoDto.getRoleCode());
        AuthRoleInfo searchAuthRoleInfo1 = this.baseMapper.selectOne(wrapper1);
        if(!BeanUtil.isEmpty(searchAuthRoleInfo1)){
            throw new ServiceException("角色编码已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        AuthRoleInfo authRoleInfo = new AuthRoleInfo();
        BeanUtils.copyProperties(platformAuthRoleInfoDto,authRoleInfo);
        this.save(authRoleInfo);
        return authRoleInfo;
    }

    @Override
    public AuthRoleInfo editAuthRoleInfo(PlatformAuthRoleInfoDto platformAuthRoleInfoDto) {
        //判断角色是否存在
        AuthRoleInfo authRoleInfo = this.getById(platformAuthRoleInfoDto.getId());
        if (authRoleInfo == null) {
            throw new ServiceException("角色不存在");
        }
        LambdaQueryWrapper<AuthRoleInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleInfo::getRoleName,platformAuthRoleInfoDto.getRoleName());
        wrapper.ne(AuthRoleInfo::getId,platformAuthRoleInfoDto.getId());
        AuthRoleInfo searchAuthRoleInfo = this.baseMapper.selectOne(wrapper);
        if(!BeanUtil.isEmpty(searchAuthRoleInfo)){
            throw new ServiceException("角色名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        LambdaQueryWrapper<AuthRoleInfo>wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AuthRoleInfo::getRoleCode,platformAuthRoleInfoDto.getRoleCode());
        wrapper1.ne(AuthRoleInfo::getId,platformAuthRoleInfoDto.getId());
        AuthRoleInfo searchAuthRoleInfo1 = this.baseMapper.selectOne(wrapper1);
        if(!BeanUtil.isEmpty(searchAuthRoleInfo1)){
            throw new ServiceException("角色编码已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        BeanUtils.copyProperties(platformAuthRoleInfoDto,authRoleInfo);
        this.updateById(authRoleInfo);
        return authRoleInfo;
    }

    @Override
    public void deleteById(Long id) {
        if(id==null){
            throw new ServiceException("id不能为空！");
        }
        //判断用户角色关系是否存在，存在不让删除
        LambdaQueryWrapper<AuthUserRole>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthUserRole::getRoleId,id);
        List<AuthUserRole> authUserRoleList = authUserRoleService.list(wrapper);
        if(authUserRoleList!=null&&authUserRoleList.size()>0){
            throw new ServiceException("存在用户角色关系，不能删除！");
        }
        //删除角色菜单关系
        LambdaQueryWrapper<AuthRoleMenu>deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AuthRoleMenu::getRoleId,id);
        authRoleMenuService.remove(deleteWrapper);
        //删除角色
        this.removeById(id);

    }
}
