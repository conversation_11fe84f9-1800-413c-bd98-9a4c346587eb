package com.medusa.gruul.platform.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 小程序配置信息类
 * <AUTHOR>
@ApiModel(value = "t_platform_mini_config对象")
@Data
@TableName("t_platform_mini_config")
@EqualsAndHashCode(callSuper = true)
public class PlatformMiniConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1863199597655154768L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 小程序appId
     */
    @ApiModelProperty(value = "小程序appId")
    private String appId;

    /**
     * 小程序密钥
     */
    @ApiModelProperty(value = "小程序密钥")
    private String appSecret;

    /**
     * 公众号appId
     */
    @ApiModelProperty(value = "公众号appId")
    private String appMpId;

    /**
     * 公众号密钥
     */
    @ApiModelProperty(value = "公众号密钥")
    private String appMpSecret;

    /**
     * 小程序密钥
     */
    @ApiModelProperty(value = "小程序预备号")
    private String reserveNumber;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 技术公司
     */
    @ApiModelProperty(value = "技术公司")
    private String technologyCompany;
}