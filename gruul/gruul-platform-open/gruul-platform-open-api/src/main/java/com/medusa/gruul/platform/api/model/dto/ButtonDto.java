package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:48 2023/9/21
 */
@Data
@ApiModel(value = "按钮dto")
public class ButtonDto {
    @ApiModelProperty(value = "按钮id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "菜单父级id")
    @NotNull
    private Long parentId;

}
