package com.medusa.gruul.platform.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: plh
 * @Description: 特殊配置Dto
 * @Date: Created in 11:13 2024/8/15
 */
@Data
public class SpecialSettingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台查看商户订单：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "平台查看商户订单：0->否；1->是")
    private Integer platformSearchShopOrder;


    /**
     * 商户允许平台查看订单：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "商户允许平台查看订单：0->否；1->是")
    private Integer shopAllowPlatformSearchOrder;

    /**
     * 平台代商户发货：0->否；1->是
     */
    @NotNull
    @ApiModelProperty(value = "平台代商户发货：0->否；1->是")
    private Integer platformProxyShipping;

    /**
     * 新人优惠券领取天数
     */
    @NotNull
    @ApiModelProperty(value = "新人优惠券领取天数")
    private Integer newPeopleCouponDays;

    /**
     * 小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;
     */
    @NotNull
    @ApiModelProperty(value = "小程序展示商品类型->0.商品;1.权益包;2.商品和权益包;")
    private Integer miniShowProductType;

    /**
     * 会员指定商品销售->0.关闭;1.启用;
     */
    @NotNull
    @ApiModelProperty(value = "会员指定商品销售->0.关闭;1.启用;")
    private Integer memberSales;

    /**
     * 佣金提现是否自动审核：0->否；1->是
     */
    @ApiModelProperty(value = "佣金提现是否自动审核：0->否；1->是")
    private Integer commissionCashFlag;

    /**
     * 接口对接职员是否可添加：0->否；1->是
     */
    @ApiModelProperty(value = "接口对接职员是否可添加：0->否；1->是")
    private Integer addEmployeeFlag;
}
