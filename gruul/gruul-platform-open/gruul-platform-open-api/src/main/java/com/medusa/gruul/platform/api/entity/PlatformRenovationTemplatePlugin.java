package com.medusa.gruul.platform.api.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * @Description: 平台装修模板全局控件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@TableName("t_platform_renovation_template_plugin")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_platform_renovation_template_plugin对象", description="平台装修模板全局控件属性表")
public class PlatformRenovationTemplatePlugin extends BaseEntity {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;

	/**创建人姓名*/
    @ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**创建人id*/
    @ApiModelProperty(value = "创建人id")
	private Long createUserId;
	/**最近一次修改人id*/
    @ApiModelProperty(value = "最近一次修改人id")
	private Long lastModifyUserId;
	/**最近一次修改人姓名*/
    @ApiModelProperty(value = "最近一次修改人姓名")
	private String lastModifyUserName;
	/**平台装修模板ID*/
    @ApiModelProperty(value = "平台装修模板ID")
	private Long templateId;
	/**控件名称中文*/
    @ApiModelProperty(value = "控件名称中文")
	private String pluginNameCn;
	/**控件名称英文*/
    @ApiModelProperty(value = "控件名称英文")
	private String pluginNameEn;
	/**是否允许取消 0否 1是*/
    @ApiModelProperty(value = "是否允许取消 0否 1是")
	private String isMandatory;
	/**是否选中 0否 1是*/
    @ApiModelProperty(value = "是否选中 0否 1是")
	private String isSelection;
	/**控件*/
    @ApiModelProperty(value = "控件")
	private String pluginProperties;
}
