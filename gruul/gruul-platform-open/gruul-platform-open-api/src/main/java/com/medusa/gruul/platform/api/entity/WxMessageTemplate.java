package com.medusa.gruul.platform.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:19 2024/11/6
 */
@Data
@Accessors(chain = true)
@TableName("t_wx_message_template")
@ApiModel(value = "WxMessageTemplate对象", description = "微信小程序消息模板")
public class WxMessageTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField(value = "last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField(value = "last_modify_user_name")
    private String lastModifyUserName;

    /**
     * 模板标识
     */
    @ApiModelProperty(value = "模板标识->1.活动通知;2.生日祝福提醒;3.商品上新;4.卖家发货提醒")
    @TableField(value = "code")
    private String code;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField(value = "template_id")
    private String templateId;


    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    @TableField(value = "name")
    private String name;

    /**
     * 模板类型->1.订阅消息;2.公众号模板消息
     */
    @ApiModelProperty(value = "模板类型->1.订阅消息;2.公众号模板消息")
    @TableField(value = "type")
    private Integer type;

    /**
     * 模板状态->1.启用;2.停用
     */
    @ApiModelProperty(value = "模板状态->1.启用;2.停用")
    @TableField(value = "status")
    private Integer status;
}
