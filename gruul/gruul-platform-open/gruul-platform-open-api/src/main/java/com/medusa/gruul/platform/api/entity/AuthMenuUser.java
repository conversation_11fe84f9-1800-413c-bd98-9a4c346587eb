package com.medusa.gruul.platform.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:31 2024/5/11
 */
@Data
@Accessors(chain = true)
@TableName("t_auth_menu_user")
@ApiModel(value = "AuthMenuUser", description = "租户菜单记录")
public class AuthMenuUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;


    /**
     * 上级分类的编号：0表示一级分类
     */
    @ApiModelProperty(value = "上级分类的编号：0表示一级分类")
    @TableField("parent_id")
    private Long parentId;
    /**
     * 菜单id
     */
    @ApiModelProperty(value = "菜单id")
    @TableField("menu_id")
    private Long menuId;
    /**
     * 菜单类型：0.菜单，1按钮
     */
    @ApiModelProperty(value = "菜单类型：0.菜单，1按钮")
    @TableField("type")
    private Integer type;
    /**
     * 启用状态：1.是；0.否
     */
    @ApiModelProperty(value = "启用状态")
    @TableField("enable_status")
    private Integer enableStatus;

    /**
     * 创建该角色用户id
     */
    @ApiModelProperty(value = "创建该角色用户id")
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 最近更新人id
     */
    @ApiModelProperty(value = "最近更新人id")
    @TableField("last_modify_user_id")
    private Long lastModifyUserId;

    /**
     * 最近更新人姓名
     */
    @ApiModelProperty(value = "最近更新人姓名")
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

}
