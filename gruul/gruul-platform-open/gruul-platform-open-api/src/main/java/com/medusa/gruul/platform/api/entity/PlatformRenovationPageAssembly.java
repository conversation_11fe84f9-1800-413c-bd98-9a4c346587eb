package com.medusa.gruul.platform.api.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 平台装修模板页面组件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@TableName("t_platform_renovation_page_assembly")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_platform_renovation_page_assembly对象", description="平台装修模板页面组件属性表")
public class PlatformRenovationPageAssembly extends BaseEntity {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;

	/**创建人姓名*/
    @ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**创建人id*/
    @ApiModelProperty(value = "创建人id")
	private Long createUserId;
	/**最近一次修改人id*/
    @ApiModelProperty(value = "最近一次修改人id")
	private Long lastModifyUserId;
	/**最近一次修改人姓名*/
    @ApiModelProperty(value = "最近一次修改人姓名")
	private String lastModifyUserName;
	/**所属页面ID*/
    @ApiModelProperty(value = "所属页面ID")
	private Long pageId;
	/**组件属性json串*/
    @ApiModelProperty(value = "组件属性json串")
	private String properties;
}
