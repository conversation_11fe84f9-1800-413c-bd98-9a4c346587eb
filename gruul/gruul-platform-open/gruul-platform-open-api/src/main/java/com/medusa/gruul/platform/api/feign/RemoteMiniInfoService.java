package com.medusa.gruul.platform.api.feign;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplate;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.model.dto.*;
import com.medusa.gruul.platform.api.model.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(value = "platform-open")
public interface RemoteMiniInfoService {


    /**
     * 获取店铺信息
     *
     * @return com.medusa.gruul.platform.api.model.dto.InfoDto
     */
    @RequestMapping(value = "/get/shop/info", method = RequestMethod.GET)
    @ApiOperation(value = "获取店铺信息")
    Result<ShopInfoDto> getShopInfo();


    /**
     * 获取当前店铺可使用的小程序订阅模板
     *
     * @return com.medusa.gruul.platform.api.model.vo.MiniMsgVo
     */
    @RequestMapping(value = "/mini/subscribe/msg", method = RequestMethod.GET)
    @ApiOperation(value = "获取当前店铺可使用的小程序订阅模板")
    List<MiniMsgVo> getCurrentMiniMsg();

    /**
     * 确认是否商家账号
     *
     * @param token 请求token
     * @return java.lang.Boolean
     */
    @RequestMapping(value = "/affirm/lessee/{token}", method = RequestMethod.GET)
    @ApiOperation(value = "确认是否商家, 确认是否商家账号")
    Result<Boolean> affirmLessee(@PathVariable(value = "token") String token);

    /**
     * 获取当前平台使用oss配置(默认使用七牛云)
     *
     * @return com.medusa.gruul.platform.api.model.dto.OssConfigDto
     */
    @RequestMapping(value = "/oss/config", method = RequestMethod.GET)
    @ApiOperation(value = "获取当前平台使用oss配置")
    Result<OssConfigDto> currentOssConfig();

    /**
     * 根据code换取小程序用户基本信息
     *
     * @param code     code
     * @return com.medusa.gruul.platform.api.entity.MiniInfo
     */
    @ApiOperation(value = "根据code换取小程序用户基本信息")
    @RequestMapping(value = "/login/{code}", method = RequestMethod.GET)
    LoginDto login(@PathVariable(value = "code") String code);

    /**
     * 换取指定小程序信息
     *
     * @return com.medusa.gruul.platform.api.model.dto.MiniAuthInfoDto
     * code=200 返回所需信息;  code=400返回错误原因
     */
    @GetMapping("/mini/auth/info")
    @ApiOperation(value = "换取小程序部分授权信息")
    Result<MiniAuthInfoDto> getMiniAuthInfo();

    /**
     * 提供默认值查询
     *
     * @param version
     * @param uniqueIdentification 唯一标识
     * @return 导入时的kv
     */
    @ApiOperation(value = "提供默认值查询")
    @RequestMapping(value = "/default/value", method = RequestMethod.GET)
    String getDefaultValue(@RequestParam(value = "version") String version,
                           @RequestParam(value = "uniqueIdentification") String uniqueIdentification);


    /**
     * 获取店铺配置信息
     *
     * @return com.medusa.gruul.platform.api.model.dto.ShopConfigDto
     */
    @RequestMapping(value = "/shop/config", method = RequestMethod.GET)
    @ApiOperation(value = "获取店铺配置信息")
    ShopConfigDto getShopConfig();


    /**
     * 根据appid获取店铺配置信息
     *
     * @param appId 小程序appId
     * @return com.medusa.gruul.platform.api.model.dto.ShopConfigDto
     */
    @RequestMapping(value = "/shop/config/appid", method = RequestMethod.GET)
    @ApiOperation(value = "根据appid获取店铺配置信息(小程序信息,支付配置)")
    ShopConfigDto getShopConfigAndAppId(@RequestParam(value = "appId", required = true) String appId);


    /**
     * 获取店铺当前使用的套餐功能状态
     * <p>
     * code == 200 返回正确数据
     *
     * @return com.medusa.gruul.platform.api.model.dto.ShopInfoDto
     */
    @RequestMapping(value = "/get/shop/function", method = RequestMethod.GET)
    @ApiOperation(value = "获取店铺当前使用的套餐功能状态,当前仅限拼团模板数据")
    Result<ShopPackageFunctionDto> getShopFunction();

    /**
     * 封装小程序信息
     * @return
     */
    @RequestMapping(value = "/get/miniInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取小程序信息，采用硬编码。方便切换多个小程序使用时获取")
    default WxMaService getWxMaService(String appId, String secret) {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(appId);
        config.setSecret(secret);
        config.setMsgDataFormat("JSON");
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }

    /**
     *
     * @return
     */
    @RequestMapping(value = "/shop/config/mini", method = RequestMethod.GET)
    @ApiOperation(value = "获取店铺小程序配置信息")
    MiniInfoVo getShopConfigMini();

    /**
     * 获取所有oss配置(默认使用七牛云)
     *
     * @return com.medusa.gruul.platform.api.model.dto.OssConfigDto
     */
    @RequestMapping(value = "/oss/all/config", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有oss配置")
    Result<List<OssConfigDto>> allOssConfig();

    /**
     * 根据shopId判断用户是否存在
     * @param shopId
     * @return
     */
    @RequestMapping(value = "/accountInfo/exist", method = RequestMethod.GET)
    @ApiOperation(value = "根据shopId判断用户是否存在")
    Result<Boolean> userIsExistByShopId(@RequestParam(value = "shopId", required = true) String shopId);

    /**
     * 获取所有平台装修模板
     * @return
     */
    @RequestMapping(value = "/renovation-template/all", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有平台装修模板")
    Result<List<PlatformRenovationTemplate>> getAllRenovationTemplate();

    /**
     * 根据模板id获取装修模板相关的所有信息
     * @param templateId
     * @return
     */
    @RequestMapping(value = "/renovation-template/all-info/id", method = RequestMethod.GET)
    @ApiOperation(value = "根据模板id获取装修模板相关的所有信息")
    Result<PlatformRenovationTemplateAllVo> getRenovationTemplateAllInfoById(@RequestParam(value = "templateId", required = true) Long templateId);


    /**
     * 获取含有设备id的用户
     * @return
     */
    @RequestMapping(value = "/get/account/registrationId", method = RequestMethod.GET)
    @ApiOperation(value = "根据模板id获取装修模板相关的所有信息")
    Result<List<AccountInfo>> getAccountRegistrationId(@RequestParam(value = "mobileType", required = true) String mobileType);

    /**
     * 获取店铺特殊配置
     * @return
     */
    @RequestMapping(value = "/get/special/setting", method = RequestMethod.GET)
    @ApiOperation(value = "获取店铺特殊配置")
    List<SpecialSetting>getSpecialSetting();

    /**
     * 获取允许平台查看订单的店铺id
     * @return
     */
    @RequestMapping(value = "/get/special/setting/shopIds", method = RequestMethod.GET)
    @ApiOperation(value = "获取允许平台查看订单的店铺id")
    List<String>getSpecialSettingShopIds(@RequestParam(value = "shopId", required = true)String shopId);

    /**
     * 通过店铺id获取特殊配置
     * @param shopId
     * @return
     */
    @GetMapping("/get/special/setting/byShopId")
    @ApiOperation(value = "通过店铺id获取特殊配置")
    List<SpecialSetting> getSpecialSettingByShopId(@RequestParam(value = "shopId", required = true)String shopId);

    /**
     * 通过门店id获取部门信息，职员信息
     * @param storeFrontId
     * @return
     */
    @GetMapping("/get/department/storeFrontId")
    @ApiOperation(value = "通过门店id获取部门信息，职员信息")
    StoreFrontOrderVo getPlatformDepartmentByStoreFrontId(@RequestParam(value = "storeFrontId", required = true)String storeFrontId);

    /**
     * 根据平台用户id获取关联部门，职员
     * @param accountId
     * @return
     */
    @GetMapping("/get/department/account")
    @ApiOperation(value = "更具平台用户id获取关联部门，职员")
    StoreFrontOrderVo getDepartmentByAccountId(@RequestParam(value = "accountId", required = true)String accountId);


    /**
     * 通过门店编码获取门店信息及其关联的职员、部门信息
     * @param classCode
     * @return
     */
    @GetMapping("/get/storeFront/classCode")
    @ApiOperation(value = "通过门店编码获取门店信息")
    StoreFrontOrderVo getStoreFrontByClassCode(@RequestParam(value = "classCode", required = true)String classCode);

    /**
     *通过小程序会员用户id获取关联信息
     * @param userId
     * @return
     */
    @GetMapping("/get/relationInfo/miniAccountId")
    @ApiOperation(value = "通过小程序会员id获取关联信息")
    RelationInfoVo getRelationInfoByMiniAccountId(@RequestParam(value = "userId", required = true)String userId);

    /**
     * 获取默认部门关系信息
     * @return
     */
    @GetMapping("/get/relationInfo/default/department")
    @ApiOperation(value = "获取默认部门关系信息")
    RelationInfoVo getRelationInfoDefaultDepartment();

    /**
     * 通过门店id获取关联信息
     * @param storeFrontId
     * @return
     */
    @GetMapping("/get/relationInfo/storeFrontId")
    @ApiOperation(value = "通过门店id获取关联信息")
    RelationInfoVo getRelationInfoByStoreFrontId(@RequestParam(value = "storeFrontId", required = true)String storeFrontId);

    /**
     * 通过用户id获取关联信息
     * @param accountId
     * @return
     */
    @GetMapping("/get/relationInfo/accountId")
    @ApiOperation(value = "通过用户id获取关联信息")
    RelationInfoVo getRelationInfoByAccountId(@RequestParam(value = "accountId", required = true)String accountId);

    /**
     * 通过标识获取微信消息模板信息
     * @param code
     * @return
     */
    @GetMapping("/get/wxMessageTemplate/code")
    @ApiOperation(value = "通过标识获取微信消息模板信息")
    WxMessageTemplateVo getWxMessageTemplateByCode(@RequestParam(value = "code", required = true)String code);

    /**
     * 获取租户小程序配置
     * @param tenantId
     * @return
     */
    @GetMapping("/get/MiniInfoVo/tenantId")
    @ApiOperation(value = "获取租户小程序配置")
    MiniInfoVo getMiniInfoVoByTenantId(@RequestParam(value = "tenantId", required = true)String tenantId);
}
