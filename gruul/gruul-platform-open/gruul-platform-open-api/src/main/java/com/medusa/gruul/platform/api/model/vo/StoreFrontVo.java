package com.medusa.gruul.platform.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 门店信息列表vo
 * @Date: Created in 18:50 2024/10/8
 */
@Data
@ApiModel(value = "StoreFrontVo对象", description = "门店信息列表vo")
public class StoreFrontVo {

    @ApiModelProperty(value = "id")
    private Long id;


    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String classCode;

    /**
     * 门店编号
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    /**
     * 门店全称
     */
    @ApiModelProperty(value = "门店全称")
    private String storeFullName;

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String storeArea;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private String employeeId;

    /**
     * 职员名称
     */
    @ApiModelProperty(value = "职员名称")
    private String employeeName;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private String stockId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String stockName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusId;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;


    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    private Double mapX;


    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    private Double mapY;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private String accountId;

    /**
     * 会员名称
     */
    @ApiModelProperty(value = "会员名称")
    private String accountName;

    /**
     * 仓库标识
     */
    @ApiModelProperty(value = "仓库标识")
    private String stockCode;

    /**
     * 是否末级
     */
    @ApiModelProperty(value = "是否末级")
    private String isCatalog;
}
