package com.medusa.gruul.afs.controller.manage;

import com.medusa.gruul.afs.model.OutAfsReceiveOrderParam;
import com.medusa.gruul.afs.model.OutAfsRefundOrderParam;
import com.medusa.gruul.afs.service.IManageAfsOrderService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:09 2024/10/15
 */
@Slf4j
@RestController
@RequestMapping("/out/afs")
@Api(tags = "外部接口-售后订单管理接口")
public class OutAfsOrderController {

    @Autowired
    private IManageAfsOrderService afsOrderService;

    /**
     * 外部系统查询售后订单-收款单
     * @param param
     * @return
     */
    @ApiOperation(value = "外部系统查询售后订单-收款单", notes = "外部系统查询售后订单-收款单")
    @PostMapping("/externalSearchOutReceiveOrder")
    public Result externalSearchAfsOutReceiveOrder(@Validated @RequestBody OutAfsReceiveOrderParam param) {
        PageUtils list=afsOrderService.searchOutAfsReceiveOrder(param);
        return Result.ok(list);
    }

    /**
     * 外部系统查询售后订单-退货单
     * @param param
     * @return
     */
    @ApiOperation(value = "外部系统查询售后订单-退货单", notes = "外部系统查询售后订单-退货单")
    @PostMapping("/externalSearchOutAfsRefundOrder")
    public Result externalSearchOutAfsRefundOrder(@Validated @RequestBody OutAfsRefundOrderParam param) {
        PageUtils list=afsOrderService.searchOutAfsRefundOrder(param);
        return Result.ok(list);
    }

}
