package com.medusa.gruul.api.model.dto;
import com.medusa.gruul.common.core.param.QueryParam;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Description: 外部系统
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改t_external_systemDTO")
public class ExternalSystemDto extends QueryParam {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人Id*/
    @ApiModelProperty(value = "创建人Id")
	private Long createUserId;
	/**修改人Id*/
    @ApiModelProperty(value = "修改人Id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**classCode*/
    @ApiModelProperty(value = "classCode")
	private String classCode;
	/**isCatalog*/
    @ApiModelProperty(value = "isCatalog")
	private String isCatalog;
	/**workflowNodeName*/
    @ApiModelProperty(value = "workflowNodeName")
	private String workflowNodeName;
	/**本系统地址*/
    @ApiModelProperty(value = "本系统地址")
	private String localAddress;
	/**本系统用户*/
    @ApiModelProperty(value = "本系统用户")
	private String localUserName;
	/**本系统密码*/
    @ApiModelProperty(value = "本系统密码")
	private String localPassword;
	/**同步告警地址*/
    @ApiModelProperty(value = "同步告警地址")
	private String errorAddress;
	/**系统名称*/
    @ApiModelProperty(value = "系统名称")
	private String name;
	/**系统地址*/
    @ApiModelProperty(value = "系统地址")
	private String address;
	/**系统用户*/
    @ApiModelProperty(value = "系统用户")
	private String userName;
	/**系统密码*/
    @ApiModelProperty(value = "系统密码")
	private String password;
	/**系统唯一标识*/
    @ApiModelProperty(value = "系统唯一标识")
	private String clientId;
	/**系统秘钥*/
    @ApiModelProperty(value = "系统秘钥")
	private String clientSecret;
	/**系统授权类型*/
    @ApiModelProperty(value = "系统授权类型")
	private String grantType;
	/**系统范围*/
    @ApiModelProperty(value = "系统范围")
	private String scope;
    /**token*/
    @ApiModelProperty(value = "token")
    private String token;
}
