package com.medusa.gruul.api.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.api.api.entity.ExternalSystem;
import com.medusa.gruul.api.api.entity.ExternalSystemItem;
import com.medusa.gruul.api.mapper.ExternalSystemItemMapper;
import com.medusa.gruul.api.model.dto.ExternalSystemItemDto;
import com.medusa.gruul.api.model.vo.ExternalSystemItemVo;
import com.medusa.gruul.api.service.IExternalSystemItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 外部系统子表
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
@Service
public class ExternalSystemItemServiceImpl extends ServiceImpl<ExternalSystemItemMapper, ExternalSystemItem> implements IExternalSystemItemService {

    @Autowired
    private  IExternalSystemItemService externalSystemItemService;

    @Override
    public IPage<ExternalSystemItemVo> selectList(ExternalSystemItemDto externalSystemItemDto) {
        IPage<ExternalSystemItemVo> page = new Page<>(externalSystemItemDto.getCurrent(), externalSystemItemDto.getSize());
        IPage<ExternalSystemItemVo> iPage =this.baseMapper.selectList(page,externalSystemItemDto);
//        List<ExternalSystemItemVo> records = iPage.getRecords();
        return iPage;
    }

    @Override
    public List<ExternalSystemItem> getByExternalSystemIds(List<Long> externalSystemIdList) {
        return  this.baseMapper.getByExternalSystemIds(externalSystemIdList);
    }

    @Override
    public List<ExternalSystemItem> newList() {
        return  this.baseMapper.listAll();
    }


    @Override
    public String externalAdd(ExternalSystemItem externalSystemItem) {
        externalSystemItemService.save(externalSystemItem);
        return "ExternalSystemItem数据添加成功！";
    }

    public String externalSaveBatch(List<ExternalSystemItem> externalSystemItemList) {
        externalSystemItemService.saveBatch(externalSystemItemList);
        return "ExternalSystemItem数据批量添加成功！";
    }

    public String externalUpdateBatchById(List<ExternalSystemItem> externalSystemItemList) {
        externalSystemItemService.updateBatchById(externalSystemItemList);
        return "ExternalSystemItem数据批量修改成功！";

    }
}
