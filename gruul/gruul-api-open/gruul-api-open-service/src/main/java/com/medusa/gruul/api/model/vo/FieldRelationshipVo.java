package com.medusa.gruul.api.model.vo;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Description: 字段关系
 * @Author: qsx
 * @Date:   2022-03-14
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改t_field_relationshipDTO")
public class FieldRelationshipVo extends QueryParam {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人id*/
    @ApiModelProperty(value = "创建人id")
	private Long createUserId;
	/**修改人id*/
    @ApiModelProperty(value = "修改人id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**关联t_External_System_item的id*/
    @ApiModelProperty(value = "关联t_External_System_item的id")
	private Long externalSystemItemId;
	/**源字段*/
    @ApiModelProperty(value = "源字段")
	private String sourceField;
	/**目标字段*/
    @ApiModelProperty(value = "目标字段")
	private String targetField;
    @ApiModelProperty(value = "该字段的表是什么类型的表，1->主表,2->子表")
    private String tableType;
}
