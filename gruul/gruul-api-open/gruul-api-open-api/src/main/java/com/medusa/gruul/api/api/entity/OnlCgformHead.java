package com.medusa.gruul.api.api.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
;


/**
 * @Description: 表信息
 * @Author: qsx
 * @Date:   2022-03-18
 * @Version: V1.0
 */
@Data
@TableName("onl_cgform_head")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="onl_cgform_head对象", description="表信息")
public class OnlCgformHead extends BaseEntity {
    
	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
	private String id;
	/**表名*/
    @ApiModelProperty(value = "表名")
	private String tableName;
	/**表类型: 0单表、1主表、2附表*/
    @ApiModelProperty(value = "表类型: 0单表、1主表、2附表")
	private Integer tableType;
	/**表版本*/
    @ApiModelProperty(value = "表版本")
	private Integer tableVersion;
	/**表说明*/
    @ApiModelProperty(value = "表说明")
	private String tableTxt;
	/**是否带checkbox*/
    @ApiModelProperty(value = "是否带checkbox")
	private String isCheckbox;
	/**同步数据库状态*/
    @ApiModelProperty(value = "同步数据库状态")
	private String isDbSynch;
	/**是否分页*/
    @ApiModelProperty(value = "是否分页")
	private String isPage;
	/**是否是树*/
    @ApiModelProperty(value = "是否是树")
	private String isTree;
	/**主键生成序列*/
    @ApiModelProperty(value = "主键生成序列")
	private String idSequence;
	/**主键类型*/
    @ApiModelProperty(value = "主键类型")
	private String idType;
	/**查询模式*/
    @ApiModelProperty(value = "查询模式")
	private String queryMode;
	/**映射关系 0一对多  1一对一*/
    @ApiModelProperty(value = "映射关系 0一对多  1一对一")
	private Integer relationType;
	/**子表*/
    @ApiModelProperty(value = "子表")
	private String subTableStr;
	/**附表排序序号*/
    @ApiModelProperty(value = "附表排序序号")
	private Integer tabOrderNum;
	/**树形表单父id*/
    @ApiModelProperty(value = "树形表单父id")
	private String treeParentIdField;
	/**树表主键字段*/
    @ApiModelProperty(value = "树表主键字段")
	private String treeIdField;
	/**树开表单列字段*/
    @ApiModelProperty(value = "树开表单列字段")
	private String treeFieldname;
	/**表单分类*/
    @ApiModelProperty(value = "表单分类")
	private String formCategory;
	/**PC表单模板*/
    @ApiModelProperty(value = "PC表单模板")
	private String formTemplate;
	/**表单模板样式(移动端)*/
    @ApiModelProperty(value = "表单模板样式(移动端)")
	private String formTemplateMobile;
	/**是否有横向滚动条*/
    @ApiModelProperty(value = "是否有横向滚动条")
	private Integer scroll;
	/**复制版本号*/
    @ApiModelProperty(value = "复制版本号")
	private Integer copyVersion;
	/**复制表类型1为复制表 0为原始表*/
    @ApiModelProperty(value = "复制表类型1为复制表 0为原始表")
	private Integer copyType;
	/**原始表ID*/
    @ApiModelProperty(value = "原始表ID")
	private String physicId;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
	private String updateBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
	private String createBy;
	/**主题模板*/
    @ApiModelProperty(value = "主题模板")
	private String themeTemplate;
	/**是否用设计器表单*/
    @ApiModelProperty(value = "是否用设计器表单")
	private String isDesForm;
	/**设计器表单编码*/
    @ApiModelProperty(value = "设计器表单编码")
	private String desFormCode;
}
