package com.medusa.gruul.payment.api.feign;

import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.payment.api.entity.EntPay;
import com.medusa.gruul.payment.api.entity.Payment;
import com.medusa.gruul.payment.api.model.dto.*;
import com.medusa.gruul.payment.api.model.param.EntPayReQuestParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2019/11/06
 */
@FeignClient(value = "payment-open")
public interface RemotePaymentService {

    /**
     * 支付请求
     *
     * @param payRequestDto 请求数据
     * @return 请求结果
     */
    @RequestMapping(value = "/pay/request", method = RequestMethod.POST)
    PayResultDto payRequest(@RequestBody PayRequestDto payRequestDto);


    /**
     * 获取指定订单交易状态
     *
     * @param outTradeNo 业务订单号
     * @param transactionId
     * @param payChannel 支付渠道
     * @return 状态
     */
    @RequestMapping(value = "/pay/status/{outTradeNo}", method = RequestMethod.GET)
    PayStatusDto getPayStatus(@PathVariable("outTradeNo") String outTradeNo,
                              @PathVariable("transactionId") String transactionId,
                              @RequestParam("payChannel") String payChannel);


    /**
     * 商户支付给个人请求
     *
     * @param param 请求数据
     * @return 请求结果
     */
    @RequestMapping(value = "/ent_pay/pay", method = RequestMethod.POST)
    EntPay payRequest(@RequestBody @Validated EntPayReQuestParam param);


    /**
     * 订单退款
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/pay/refund", method = RequestMethod.POST)
    Result payRefund(@RequestBody @Validated RefundRequestDto param);



    /**
     * 获取佣金提现金额状态
     * @param entPayId
     * @param id
     * @param queryTimes
     * @return
     */
    @RequestMapping(value = "/get/payStatus", method = RequestMethod.GET)
    EntQueryPayDto getPayStatus(@RequestParam(value = "entPayId", required = false) String entPayId,
                                       @RequestParam(value = "id", required = false) Long id,
                                       @RequestParam(value = "queryTimes", required = false) Integer queryTimes);

    /**
     * 根据订单流水号获取支付信息
     * @param transactionId
     * @return
     */
    @RequestMapping(value = "/get/payment", method = RequestMethod.GET)
    PaymentWechatDto getPayment( @RequestParam("transactionId") String transactionId );

    /**
     * 判断提现单状态是否为待收款
     * @param id
     * @return
     */
    @RequestMapping(value = "/vail/receiveMoneyState", method = RequestMethod.GET)
    Boolean vailReceiveMoneyState(@RequestParam("id")String id);

    /**
     * 更新微信转账单状态
     * @param id
     * @return
     */
    @RequestMapping(value = "/update/transferState", method = RequestMethod.GET)
    String updateTransferState(@RequestParam("id")String id);

}
