package com.medusa.gruul.payment.task;

import com.medusa.gruul.payment.service.IWxTransferV3Service;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:41 2025/4/23
 */
@Component
@Slf4j
public class PaymentXxlJob {

    @Autowired
    private IWxTransferV3Service wxTransferV3Service;


    @XxlJob("updateTransfersStatus")
    public ReturnT<String> updateTransfersStatus(String param) throws Exception {
        log.info("-----------更新微信转零钱订单状态定时任务开启-----------");
        wxTransferV3Service.updateTransfersStatus();
        log.info("-----------更新微信转零钱订单状态定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

}
