package com.medusa.gruul.payment.service.impl;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.medusa.gruul.account.api.model.message.UpdateCommissionMessage;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.TransferV3StateEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.payment.api.entity.WxTransferSceneV3;
import com.medusa.gruul.payment.api.entity.WxTransferV3;
import com.medusa.gruul.payment.api.model.message.WxTransferSceneV3Message;
import com.medusa.gruul.payment.api.model.message.WxTransferV3Message;
import com.medusa.gruul.payment.api.transfer.InitiateBatchTransferResponseNew;
import com.medusa.gruul.payment.api.transfer.TransferDetailEntityNew;
import com.medusa.gruul.payment.api.transfer.TransferSceneReportInfoNew;
import com.medusa.gruul.payment.conf.PayProperty;
import com.medusa.gruul.payment.mapper.WxTransferSceneV3Mapper;
import com.medusa.gruul.payment.mapper.WxTransferV3Mapper;
import com.medusa.gruul.payment.model.dto.InitiateBatchTransferRequestNew;
import com.medusa.gruul.payment.mq.Sender;
import com.medusa.gruul.payment.service.IWxTransferV3Service;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.vo.PayInfoVo;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.http.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:45 2025/4/22
 */
@Service
@Slf4j
public class WxTransferV3ServiceImpl extends ServiceImpl<WxTransferV3Mapper, WxTransferV3> implements IWxTransferV3Service {

    @Autowired
    private WxTransferSceneV3Mapper wxTransferSceneV3Mapper;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private PayProperty payProperty;
    @Autowired
    private Sender sender;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchTransfer(WxTransferV3Message message) {

        //获取店铺配置的小程序信息
        String tenantId = message.getTenantId();
        TenantContextHolder.setTenantId(tenantId);
        WxTransferV3 wxTransferV3 = new WxTransferV3();
        InitiateBatchTransferRequestNew initiateBatchTransferRequestNew = new InitiateBatchTransferRequestNew();

        BeanUtils.copyProperties(message,wxTransferV3);
        BeanUtils.copyProperties(message,initiateBatchTransferRequestNew);
        this.save(wxTransferV3);

        List<WxTransferSceneV3Message> list = message.getList();
        List<TransferSceneReportInfoNew> transferSceneReportInfos = new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (WxTransferSceneV3Message wxTransferSceneV3Message : list) {
                WxTransferSceneV3 wxTransferSceneV3 = new WxTransferSceneV3();
                BeanUtils.copyProperties(wxTransferSceneV3Message,wxTransferSceneV3);
                wxTransferSceneV3.setTransferId(wxTransferV3.getId()+"");
                TransferSceneReportInfoNew transferSceneReportInfoNew = new TransferSceneReportInfoNew();
                BeanUtils.copyProperties(wxTransferSceneV3Message,transferSceneReportInfoNew);
                transferSceneReportInfos.add(transferSceneReportInfoNew);
                wxTransferSceneV3Mapper.insert(wxTransferSceneV3);
            }
        }
        initiateBatchTransferRequestNew.setTransferSceneReportInfos(transferSceneReportInfos);
        //微信转账
        this.initiateBatchTransferNew(wxTransferV3,initiateBatchTransferRequestNew);
        //更新微信转账单状态
        this.updateById(wxTransferV3);

        UpdateCommissionMessage updateCommissionMessage = new UpdateCommissionMessage();
        updateCommissionMessage.setTenantId(wxTransferV3.getTenantId());
        updateCommissionMessage.setOutBillNo(wxTransferV3.getOutBillNo());
        updateCommissionMessage.setState(wxTransferV3.getState());
        updateCommissionMessage.setPackageInfo(wxTransferV3.getPackageInfo());
        updateCommissionMessage.setTransferBillNo(wxTransferV3.getTransferBillNo());
        updateCommissionMessage.setFailResult(wxTransferV3.getFailResult());
        //发送更新微信转账单状态队列
        sender.sendUpdateCommissionMessage(updateCommissionMessage);

    }





    @Override
    public void initiateBatchTransferNew(WxTransferV3 wxTransferV3,InitiateBatchTransferRequestNew request) {

        log.info("WxTransferV3Service.initiateBatchTransferNew request:{}", request.toString());

        //获取店铺配置的小程序信息
        ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();

        if (shopConfig == null) {
            throw new ServiceException("商户配置不存在");
        }
        PayInfoVo payInfo = shopConfig.getPayInfo();

        log.info("WxTransferV3Service.initiateBatchTransferNew payInfo:{}", payInfo.toString());

        try {
            Config config =
                    new RSAPublicKeyConfig.Builder()
                            .merchantId(payInfo.getMchId()) //微信支付的商户号
                            .privateKeyFromPath(payInfo.getPrivateKeyFromPath()) // 商户API证书私钥的存放路径
                            .publicKeyFromPath(payInfo.getPublicKeyFromPath() ) //微信支付公钥的存放路径
                            .publicKeyId(payInfo.getPublicKeyId()) //微信支付公钥ID
                            .merchantSerialNumber(payInfo.getMerchantSerialNumber()) //商户API证书序列号
                            .apiV3Key(payInfo.getApiV3Key()) //APIv3密钥
                            .build();
            String encryptName = config.createEncryptor().encrypt(request.getUserName());
            request.setUserName(encryptName);
            String requestPath = payProperty.getTransferUrl();
            HttpHeaders headers = new HttpHeaders();
            headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
            headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
            headers.addHeader("Wechatpay-Serial", config.createEncryptor().getWechatpaySerial());
            HttpRequest httpRequest =
                    new HttpRequest.Builder()
                            .httpMethod(HttpMethod.POST)
                            .url(requestPath)
                            .headers(headers)
                            .body(createRequestBody(request))
                            .build();
            HttpClient httpClient = new DefaultHttpClientBuilder().config(config).build();
            HttpResponse<InitiateBatchTransferResponseNew> httpResponse = httpClient.execute(httpRequest, InitiateBatchTransferResponseNew.class);
            InitiateBatchTransferResponseNew initiateBatchTransferResponseNew = httpResponse.getServiceResponse();
            log.info("WxTransferV3Service.initiateBatchTransferNew response:{}", initiateBatchTransferResponseNew);
            wxTransferV3.setState(initiateBatchTransferResponseNew.getState());
            wxTransferV3.setReason(initiateBatchTransferResponseNew.toString());
            wxTransferV3.setTransferBillNo(initiateBatchTransferResponseNew.getTransferBillNo());
            wxTransferV3.setPackageInfo(initiateBatchTransferResponseNew.getPackageInfo());
        }catch (Exception e){
            log.error("WxTransferV3Service.initiateBatchTransferNew error:{}",e);
            wxTransferV3.setState(TransferV3StateEnum.FAIL.getState());
            wxTransferV3.setReason(e.toString());
            wxTransferV3.setFailResult("请求接口错误");
        }

    }

    @Override
    public void updateTransfersStatus() {

        LambdaQueryWrapper<WxTransferV3>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxTransferV3::getDeleted, CommonConstants.NUMBER_ZERO);
        List<String>stateList = new ArrayList<>();
        stateList.add(TransferV3StateEnum.FAIL.getState());//失败
        stateList.add(TransferV3StateEnum.ERROR.getState());//错误
        stateList.add(TransferV3StateEnum.SUCCESS.getState());//成功
        wrapper.notIn(WxTransferV3::getState,stateList);

        List<WxTransferV3> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (WxTransferV3 wxTransferV3 : list) {
                TransferDetailEntityNew transferDetailByOutNoNew = this.getTransferDetailByOutNoNew(wxTransferV3);
                if(transferDetailByOutNoNew!=null){
                    if(!wxTransferV3.getState().equals(transferDetailByOutNoNew.getState())){
                        wxTransferV3.setState(transferDetailByOutNoNew.getState());
                        wxTransferV3.setFailResult(transferDetailByOutNoNew.getFailReason());
                        this.updateById(wxTransferV3);
                        UpdateCommissionMessage updateCommissionMessage = new UpdateCommissionMessage();
                        updateCommissionMessage.setTenantId(wxTransferV3.getTenantId());
                        updateCommissionMessage.setOutBillNo(wxTransferV3.getOutBillNo());
                        updateCommissionMessage.setState(wxTransferV3.getState());
                        updateCommissionMessage.setFailResult(wxTransferV3.getFailResult());
                        //发送更新微信转账单状态队列
                        sender.sendUpdateCommissionMessage(updateCommissionMessage);
                    }
                }
            }
        }
    }

    @Override
    public TransferDetailEntityNew getTransferDetailByOutNoNew(WxTransferV3 wxTransferV3) {
        log.info("WxTransferV3Service.getTransferDetailByOutNoNew request:{}", wxTransferV3);
        try{

            String tenantId = wxTransferV3.getTenantId();
            TenantContextHolder.setTenantId(tenantId);
            //获取店铺配置的小程序信息
            ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();

            if (shopConfig == null) {
                throw new ServiceException("商户配置不存在");
            }
            PayInfoVo payInfo = shopConfig.getPayInfo();

            Config config =
                    new RSAPublicKeyConfig.Builder()
                            .merchantId(payInfo.getMchId()) //微信支付的商户号
                            .privateKeyFromPath(payInfo.getPrivateKeyFromPath()) // 商户API证书私钥的存放路径
                            .publicKeyFromPath(payInfo.getPublicKeyFromPath() ) //微信支付公钥的存放路径
                            .publicKeyId(payInfo.getPublicKeyId()) //微信支付公钥ID
                            .merchantSerialNumber(payInfo.getMerchantSerialNumber()) //商户API证书序列号
                            .apiV3Key(payInfo.getApiV3Key()) //APIv3密钥
                            .build();

            String requestPath = payProperty.getQueryTransFerUrl();;
            requestPath = requestPath.replace("{out_bill_no}", UrlEncoder.urlEncode(wxTransferV3.getOutBillNo()));
            HttpHeaders headers = new HttpHeaders();
            headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
            headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
            HttpRequest httpRequest =
                    new HttpRequest.Builder()
                            .httpMethod(HttpMethod.GET)
                            .url(requestPath)
                            .headers(headers)
                            .build();
            HttpClient httpClient = new DefaultHttpClientBuilder().config(config).build();
            HttpResponse<TransferDetailEntityNew> httpResponse = httpClient.execute(httpRequest, TransferDetailEntityNew.class);
            TransferDetailEntityNew transferDetailEntityNew = httpResponse.getServiceResponse();
            log.info("WxTransferV3Service.getTransferDetailByOutNoNew response:{}", transferDetailEntityNew);
            return transferDetailEntityNew;
        }catch (Exception e){
            log.error("商户单号查询转账单失败",e);
            return null;
        }
    }

    @Override
    public Boolean vailReceiveMoneyState(String id) {
        Boolean result = false;
        LambdaQueryWrapper<WxTransferV3>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxTransferV3::getOutBillNo,id);
        WxTransferV3 wxTransferV3 = this.getOne(wrapper);
        TransferDetailEntityNew transferDetailByOutNoNew = this.getTransferDetailByOutNoNew(wxTransferV3);
        if(transferDetailByOutNoNew.getState().equals(TransferV3StateEnum.WAIT_USER_CONFIRM.getState())){
            result = true;
        }else{
            result = false;
            wxTransferV3.setFailResult(transferDetailByOutNoNew.getFailReason());
            wxTransferV3.setState(transferDetailByOutNoNew.getState());
            this.updateById(wxTransferV3);
            UpdateCommissionMessage updateCommissionMessage = new UpdateCommissionMessage();
            updateCommissionMessage.setTenantId(wxTransferV3.getTenantId());
            updateCommissionMessage.setOutBillNo(wxTransferV3.getOutBillNo());
            updateCommissionMessage.setState(wxTransferV3.getState());
            updateCommissionMessage.setFailResult(wxTransferV3.getFailResult());
            //发送更新微信转账单状态队列
            sender.sendUpdateCommissionMessage(updateCommissionMessage);
        }
        return result;
    }

    @Override
    public String updateTransferState(String id) {
        LambdaQueryWrapper<WxTransferV3>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxTransferV3::getOutBillNo,id);
        WxTransferV3 wxTransferV3 = this.getOne(wrapper);
        TransferDetailEntityNew transferDetailByOutNoNew = this.getTransferDetailByOutNoNew(wxTransferV3);
        String state = transferDetailByOutNoNew.getState();
        wxTransferV3.setFailResult(transferDetailByOutNoNew.getFailReason());
        wxTransferV3.setState(state);
        this.updateById(wxTransferV3);
        return state;
    }

    @Override
    public void wxPaySuccessCallback(HttpServletRequest request) {

//        String requestBody = getBodyString(request, "UTF-8");
//        //证书序列号（微信平台）   验签的“微信支付平台证书”所对应的平台证书序列号
//        String wechatPaySerial = request.getHeader("Wechatpay-Serial");
//        //微信传递过来的签名   验签的签名值
//        String wechatSignature = request.getHeader("Wechatpay-Signature");
//        //验签的时间戳
//        String wechatTimestamp = request.getHeader("Wechatpay-Timestamp");
//        //验签的随机字符串
//        String wechatpayNonce = request.getHeader("Wechatpay-Nonce");
//
//        // 1. 构造 RequestParam
//        RequestParam requestParam = new RequestParam.Builder()
//                .serialNumber(wechatPaySerial)
//                .nonce(wechatpayNonce)
//                .signature(wechatSignature)
//                .timestamp(wechatTimestamp)
//                .body(requestBody)
//                .build();
//        // 2. 构建Config RSAPublicKeyConfig
//        Config config =
//                new RSAPublicKeyConfig.Builder()
//                        .merchantId(payInfo.getMchId()) //微信支付的商户号
//                        .privateKeyFromPath(payInfo.getPrivateKeyFromPath()) // 商户API证书私钥的存放路径
//                        .publicKeyFromPath(payInfo.getPublicKeyFromPath() ) //微信支付公钥的存放路径
//                        .publicKeyId(payInfo.getPublicKeyId()) //微信支付公钥ID
//                        .merchantSerialNumber(payInfo.getMerchantSerialNumber()) //商户API证书序列号
//                        .apiV3Key(payInfo.getApiV3Key()) //APIv3密钥
//                        .build();
//        log.info("WxPayService.wxPaySuccessCallback request : wechatPaySerial is [{}]  , wechatSignature is [{}] , wechatTimestamp is [{}] , wechatpayNonce  is [{}] , requestBody is [{}]",wechatPaySerial,wechatSignature,wechatTimestamp,wechatpayNonce,requestBody);
//        // 3. 初始化 NotificationParser
//        NotificationParser parser = new NotificationParser((NotificationConfig) config);
//        try {
//            TransferDetailEntityNew entity = parser.parse(requestParam, TransferDetailEntityNew.class);
//            log.info("WxPayService.wxPaySuccessCallback responseBody: {}", entity != null ? JSON.toJSONString(entity) : null);
//
//        } catch (ValidationException e) {
//            log.error("Sign verification failed", e);
//            throw new ServiceException("签名验证失败",e);
//        } catch (Exception e) {
//            log.error("Exception occurred while processing", e);
//            throw new ServiceException("系统内部错误",e);
//        }
    }

    private static RequestBody createRequestBody(Object request) {
        return new JsonRequestBody.Builder().body(new Gson().toJson(request)).build();
    }



    public static String getBodyString(HttpServletRequest request, String charSet) {
        StringBuilder sb = new StringBuilder();
        InputStream inputStream = null;
        BufferedReader reader = null;
        try {
            inputStream = request.getInputStream();
            //读取流并将流写出去,避免数据流中断;
            reader = new BufferedReader(new InputStreamReader(inputStream, charSet));
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            log.error("获取requestBody异常", e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(reader);
        }
        return sb.toString();
    }

}
