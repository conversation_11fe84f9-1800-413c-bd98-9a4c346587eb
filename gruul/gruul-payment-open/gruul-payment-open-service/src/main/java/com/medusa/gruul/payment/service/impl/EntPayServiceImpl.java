package com.medusa.gruul.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.util.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.entpay.EntPayQueryRequest;
import com.github.binarywang.wxpay.bean.entpay.EntPayQueryResult;
import com.github.binarywang.wxpay.bean.entpay.EntPayRequest;
import com.github.binarywang.wxpay.bean.entpay.EntPayResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceApacheHttpImpl;
import com.medusa.gruul.account.api.conf.MiniInfoProperty;
import com.medusa.gruul.account.api.entity.MiniAccountCommissionCash;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.constant.enums.PayStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.payment.api.entity.EntPay;
import com.medusa.gruul.payment.api.entity.EntPayCallBackLog;
import com.medusa.gruul.payment.api.model.dto.EntPayReQuestDto;
import com.medusa.gruul.payment.api.model.dto.EntQueryPayDto;
import com.medusa.gruul.payment.api.model.param.EntPayReQuestParam;
import com.medusa.gruul.payment.api.util.GlobalConstant;
import com.medusa.gruul.payment.api.util.ParamMd5SignUtils;
import com.medusa.gruul.payment.mapper.EntPayMapper;
import com.medusa.gruul.payment.service.EntPayCallBackLogService;
import com.medusa.gruul.payment.service.EntPayService;
import com.medusa.gruul.platform.api.entity.MiniInfo;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.vo.MiniInfoVo;
import com.medusa.gruul.platform.api.model.vo.PayInfoVo;
import lombok.extern.java.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;


/**
 * <AUTHOR> by zq
 * @date created in 2019/11/18
 */
@Service(value = "entPayServiceImpl")
@Log
public class EntPayServiceImpl extends ServiceImpl<EntPayMapper, EntPay> implements EntPayService {


    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private EntPayCallBackLogService entPayCallBackLogService;

    @Resource
    private MiniInfoProperty miniInfoProperty;


    /**
     * 请求 商家对个人付款
     * <p>
     * TODO : 多渠道支付暂未接入,暂不做判断 默认wx
     *
     * @param payReQuestParam
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EntPay pay(EntPayReQuestParam payReQuestParam) {
        log.info(String.format("请求 商家对个人付款 start param : %s", payReQuestParam));

        if (!ParamMd5SignUtils.md5(payReQuestParam).equalsIgnoreCase(payReQuestParam.getMd5())) {
            log.warning(String.format("请求商家对个人付款, 请求参数 md5 匹配失败!"));
            throw new ServiceException(SystemCode.PARAM_VALID_ERROR.getMsg());
        }

        EntPayReQuestDto dto = new EntPayReQuestDto();
        BeanUtils.copyProperties(payReQuestParam, dto);

        //多渠道支付暂未接入,暂不做判断 默认wx
        WxPayService wxPayService = this.getWxPayService(dto);

        EntPay entPay = new EntPay();
        BeanUtils.copyProperties(dto, entPay);
        entPay.setTradeStatus(GlobalConstant.STRING_ZERO);
        if (!this.save(entPay)) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED.getMsg());
        }

        dto.setId(entPay.getId());
        EntPayCallBackLog entPayCallBackLog = new EntPayCallBackLog();
        BeanUtils.copyProperties(entPay, new EntPayCallBackLog());
        EntPayRequest entPayRequest = this.initRequestPayData(wxPayService.getConfig(), dto);
        EntPayResult entPayResult;
        try {
            entPayResult = wxPayService.getEntPayService().entPay(entPayRequest);
            log.info(String.format("请求 商家对个人付款, 接口调用返回数据. %s", entPayResult));
            if (GlobalConstant.STRING_SUCCESS.equals(entPayResult.getResultCode()) && GlobalConstant.STRING_SUCCESS.equals(entPayResult.getReturnCode())) {
                entPay.setTradeStatus(GlobalConstant.STRING_TOW);
                entPay.setTransactionId(entPayResult.getPaymentNo());
                entPay.setPayTime(entPayResult.getPaymentTime());
                this.updateById(entPay);
            } else {
                entPay.setTradeStatus(GlobalConstant.STRING_FOUR);
                this.updateById(entPay);
            }
            entPayCallBackLog.setEntPayId(entPay.getId());
            entPayCallBackLog.setCallbackContext(JSONObject.toJSONString(entPay));
            entPayCallBackLogService.save(entPayCallBackLog);
            return entPay;
        } catch (WxPayException e) {
            log.warning(String.format("请求 商家对个人付款, 接口调用异常. 异常信息: %s", e));
            entPay.setTradeStatus(GlobalConstant.STRING_THREE);
            this.updateById(entPay);
            entPayCallBackLog.setEntPayId(entPay.getId());
            entPayCallBackLog.setCallbackContext(e.getMessage());
            entPayCallBackLogService.save(entPayCallBackLog);
            return entPay;
        } catch (Exception e) {
            log.warning(String.format("请求 商家对个人付款, 接口调用异常. 异常信息: %s, " +
                    "参数实体数据 : %s, 请求实体数据 : %s ", e, entPay, entPayRequest));
            //throw new ServiceException(e.getMessage());
            return entPay;
        }
    }


    private EntPayRequest initRequestPayData(WxPayConfig config, EntPayReQuestDto dto) {
        EntPayRequest entPayRequest = new EntPayRequest();
        BeanUtils.copyProperties(dto, entPayRequest);
        entPayRequest.setMchAppid(config.getAppId());
        entPayRequest.setMchId(config.getMchId());
        entPayRequest.setPartnerTradeNo(dto.getId().toString());
        entPayRequest.setCheckName(dto.getCheckName().name());
        return entPayRequest;
    }

    private EntPayQueryRequest initRequestPayQueryData(WxPayConfig config,String partnerTradeNo){
        EntPayQueryRequest entPayQueryRequest = new EntPayQueryRequest();
        entPayQueryRequest.setMchId(config.getMchId());
        entPayQueryRequest.setPartnerTradeNo(partnerTradeNo);
        entPayQueryRequest.setAppid(config.getAppId());
        entPayQueryRequest.setNonceStr(UuidUtils.generateUuid());
        return entPayQueryRequest;
    }


    /**
     * 组装支付配置
     * @param dto
     * @return
     */
    @Override
    public WxPayService getWxPayService(EntPayReQuestDto dto) {
        ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
        if (shopConfig == null) {
            throw new ServiceException("商户配置不存在");
        }

        PayInfoVo payInfo = shopConfig.getPayInfo();
        log.info("店铺配置相关信息shopConfig log = " + JSONObject.toJSONString(shopConfig));
        //获取店铺配置的小程序信息
        MiniInfoVo miniInfoVo = remoteMiniInfoService.getShopConfigMini();
        log.info("店铺配置小程序信息 " + JSONObject.toJSONString(miniInfoVo));
        WxPayService wxPayService = new WxPayServiceApacheHttpImpl();
        WxPayConfig wxPayConfig = new WxPayConfig();
        wxPayConfig.setAppId(miniInfoVo.getAppId());
        wxPayConfig.setMchId(payInfo.getMchId());
        wxPayConfig.setMchKey(payInfo.getMchKey());
        wxPayConfig.setKeyPath(payInfo.getCertificatesPath());
        wxPayService.setConfig(wxPayConfig);
        dto.setId(null);
        return wxPayService;
    }

    @Override
    public EntQueryPayDto getPayStatus(String entPayId,Long id,Integer queryTimes) {
        EntQueryPayDto entQueryPayDto = new EntQueryPayDto();
        if(StringUtils.isEmpty(entPayId)){
            LambdaQueryWrapper<EntPay>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EntPay::getOrderId,id);
            EntPay entPay = this.baseMapper.selectOne(queryWrapper);
            if(entPay!=null){
                entPayId = entPay.getId()+"";
            }
        }
        if(queryTimes==null){
            queryTimes = 0;
        }
        if(StringUtils.isNotEmpty(entPayId)){
            WxPayService wxPayService = this.getWxPayService(new EntPayReQuestDto());
            EntPayQueryRequest entPayQueryRequest = initRequestPayQueryData(wxPayService.getConfig(), entPayId);
            try {
                EntPayQueryResult entPayQueryResult = wxPayService.getEntPayService().queryEntPay(entPayQueryRequest);
                if(entPayQueryResult.getReturnCode().equals(CommonConstants.SUCCESS_CODE)){
                    if(entPayQueryResult.getResultCode().equals(CommonConstants.SUCCESS_CODE)){
                        if(entPayQueryResult.getStatus().equals(PayStatusEnum.SUCCESS.getCode())){//支付成功
                            entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                            entQueryPayDto.setPayStatus(PayStatusEnum.SUCCESS.getCode());
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            LocalDateTime payTime = LocalDateTime.parse(entPayQueryResult.getPaymentTime(), formatter);
                            entQueryPayDto.setPayTime(payTime);
                        }
                        if(entPayQueryResult.getStatus().equals(PayStatusEnum.PROCESSING.getCode())){//支付处理中
                            if(queryTimes==2){
                                entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                                entQueryPayDto.setPayStatus(PayStatusEnum.PROCESSING.getCode());
                            }
                        }
                        if(entPayQueryResult.getStatus().equals(PayStatusEnum.FAILED.getCode())){//支付失败
                            entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                            entQueryPayDto.setPayStatus(PayStatusEnum.FAILED.getCode());
                            entQueryPayDto.setPayFailReason(entPayQueryResult.getReason());
                        }
                    }else{
                        String errCodeDes = entPayQueryResult.getErrCodeDes();
                        log.warning(String.format("请求查询商家对个人付款订单接口调用错误. 错误信息: %s, " +
                                "请求实体数据 : %s ", errCodeDes, entPayQueryRequest));
                        if(queryTimes==2){
                            entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                            entQueryPayDto.setPayStatus(PayStatusEnum.FAILED.getCode());
                            entQueryPayDto.setPayFailReason(String.format("请求查询商家对个人付款订单接口调用错误. 错误信息: %s",errCodeDes));
                        }
                    }
                }else{
                    String returnMsg = entPayQueryResult.getReturnMsg();
                    log.warning(String.format("请求查询商家对个人付款订单接口调用失败. 失败信息: %s, " +
                            "请求实体数据 : %s ", returnMsg, entPayQueryRequest));
                    if(queryTimes==2){
                        entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                        entQueryPayDto.setPayStatus(PayStatusEnum.FAILED.getCode());
                        entQueryPayDto.setPayFailReason(String.format("请求查询商家对个人付款订单接口调用失败. 失败信息: %s",returnMsg));
                    }
                }
            }catch (Exception e){
                log.warning(String.format("请求查询商家对个人付款订单接口调用异常. 异常信息: %s, " +
                        "请求实体数据 : %s ", e, entPayQueryRequest));
                if(queryTimes==2){
                    entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                    entQueryPayDto.setPayStatus(PayStatusEnum.FAILED.getCode());
                    entQueryPayDto.setPayFailReason(String.format("请求查询商家对个人付款订单接口调用异常. 异常信息: %s",e));
                }
            }
        }else{
            if(queryTimes==2){
                entQueryPayDto.setStatus(ApproveStatusEnum.TERMINATION.getStatus());
                entQueryPayDto.setPayStatus(PayStatusEnum.FAILED.getCode());
                entQueryPayDto.setPayFailReason("支付失败，未查到对应微信付款单");
            }
        }
        entQueryPayDto.setQueryTimes(queryTimes+1);
        return entQueryPayDto;
    }


}
